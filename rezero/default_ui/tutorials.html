@@master_page.html
<link href="css/page_table_list.css" rel="stylesheet" />
<style>
    #editorResult .ace_cursor {
        display: none !important; /* 或者使用 caret-color: transparent; */
    }

    .ace_editor {
        z-index: 999
    }
</style>
<div id="apibox" class="card">
    <div class="card-header"><h4>{{title}}</h4></div>
    <div class="p-l-15">
        <code>{{message1}}输出块,根目录以Model开头,非根目录以变量开头，支持C#自带函数</code>
    </div>
    <div class="p-l-15">
        <code>{{message2}} 代码块，可以if foreach 逻辑计算等，支持C#自带语法</code>
    </div>
    <div class="card-body">

        <div class="form-group">
            <label for="example-text-input">测试数据</label>
            <div id="editorJson" style="height: calc(30vh - 100px); min-height: 300px"></div>
        </div>
        <div class="form-group">
            <label for="example-text-input">测试模版 <span class="red">(复制这个)</span></label>
            <div id="editorTemplate" style="height: calc(30vh - 100px);min-height:500px"></div>
        </div>
        <div class="form-group">
            <button type="submit" @click="submitTest" class="btn btn-primary m-r-5">测 试</button>
            <button type="button" class="btn btn-default" onclick="javascript: window.close(); return false;">关 闭</button>
        </div>
        <div class="form-group">
            <label for="example-text-input">测试结果</label>
            <div id="editorResult" style="height: calc(30vh - 100px); min-height: 500px"></div>
        </div>
    </div>
</div>

<script src="js/ace/src-min/ace.js"></script>
<script src="js/ace/src-min/ext-language_tools.js"></script>
<script>
    var vueObj = new Vue({
        el: '#apibox',
        data: {
            title: null,
            message1: "{{}}",
            message2:"<%%>"
        },
        mounted() {
            this.fetchData();
        },
        methods: {
            fetchData() {
                var page = this.getPageInfo();
                this.initEditor("editorJson", "json5");
                this.initEditor("editorTemplate", page.style);
                this.initEditor("editorResult", page.style, true);
                this.bindDefalutTemplate();
                this.bindTemplateFormatJson();
                var urlParams = new URLSearchParams(window.location.search);
                var type = urlParams.get('type');
                if (type == 1)
                {
                    this.title = "实体模版在线教程";
                }
            },
            onSearch: function (page) {

            },
            initEditor: function (id, style, isReadOnly) {
                var editor = ace.edit(id);
                if (isReadOnly) {
                    editor.setTheme("ace/theme/monokai");
                } else {
                    editor.setTheme("ace/theme/twilight");
                }
                editor.setOption("showPrintMargin", false);
                editor.setOption("enableBasicAutocompletion", true);
                editor.setOption("enableSnippets", true);
                editor.setOption("useWorker", false);
                editor.setOption("enableLiveAutocompletion", true)
                editor.session.setMode("ace/mode/" + style);
                editor.setReadOnly(isReadOnly);
                editor.setValue(null);
                editor.selection.clearSelection();
                editor.resize();
            },
            getPageInfo: function () {
                var urlParams = new URLSearchParams(window.location.search);
                var type = urlParams.get('type');
                var style = urlParams.get('style');
                var result = { style: style };
                if (type == 1) {

                    return result;

                } else if (type == 2) {

                    return result;
                }
            },
            submitTest: function () {
                var data = ace.edit("editorJson").getValue();
                var template = ace.edit("editorTemplate").getValue();
                var urlParams = new URLSearchParams(window.location.search);
                var type = urlParams.get('type');
                var url = "/PrivateReZeroRoute/100003/ExecTemplate";
                axios.post(url, { type: type, data: data, template: template }, jwHeader)
                    .then(response => {
                        this.error = null;
                        var editor = ace.edit("editorResult");
                        if (response.data && response.data.message) {
                            editor.setValue(response.data.message);
                        } else { 
                            editor.setValue(response.data);
                        }
                        editor.selection.clearSelection();

                    })
                    .catch(error => {
                        this.error = error.message;
                        this.data = null;
                    });
            },
            bindDefalutTemplate: function () {
                var urlParams = new URLSearchParams(window.location.search);
                var type = urlParams.get('type');
                var url = "/PrivateReZeroRoute/100003/GetDefalutTemplate";
                axios.post(url, { type: type }, jwHeader)
                    .then(response => {
                        this.error = null;
                        var editor = ace.edit("editorTemplate");
                        editor.setValue(response.data);
                        editor.selection.clearSelection();
                    })
                    .catch(error => {
                        this.error = error.message;
                        this.data = null;
                    });
            },
            bindTemplateFormatJson: function () {
                var urlParams = new URLSearchParams(window.location.search);
                var type = urlParams.get('type');
                var url = "/PrivateReZeroRoute/100003/GetTemplateFormatJson";
                axios.post(url, { type: type }, jwHeader)
                    .then(response => {
                        this.error = null;
                        var editor = ace.edit("editorJson");
                        editor.setValue(JSON.stringify(response.data, null, 4));
                        editor.selection.clearSelection();
                    })
                    .catch(error => {
                        this.error = error.message;
                        this.data = null;
                    });
            }
        }
    });</script>