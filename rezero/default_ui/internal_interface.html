@@master_page.html
<link href="css/page_interface.css" rel="stylesheet" />
<div id="apibox">
    <div class="form-group row">
        <form id="frmSearch" onsubmit="return false;">
            <div class="row">
                <div class="col-md-2"> </div>
                <div class="col-md-6">
                    <input class="form-control" type="text" id="txtSearch" name="example-text-input" placeholder="Url or name">
                </div>
                <div class="col-md-4">
                    <button @click="onSearch()" id="btnSearch" class="btn btn-success" type="submit"><i class="mdi mdi-magnify"></i>搜索</button>
                    <button onclick="frmSearch.reset()" id="btnSearch" class="btn btn-close" type="submit">清空</button>
                </div>
            </div>
        </form>
    </div>
    <div class="data" v-for="masterItem in data" :key="masterItem.Key">
        <h2>{{ masterItem.Key }} </h2>
        <section v-for="item in masterItem.Value" :key="item.Id">
            <div class="endpoint">
                <p class="p" @click="toggleEndpoint">
                    <span class="method">{{ item.HttpMethod }}</span>
                    <span class="url"> {{item.Url}}   {{item.Name}}</span>
                    <span class="openapi">+</span>
                </p>
                <p>{{ item.Description }}</p>
                <div class="parameter-title" style="display:none">
                    Parameters:
                    <div class="try-out">
                        <button class="btn try-out__btn" @click="tryItOut(item)">Try it out </button>
                    </div>
                </div>
                <div class="parameter" style="display:none" v-if="item.DataModel.DefaultParameters && item.DataModel.DefaultParameters.length > 0">
                    <ul>
                        <li v-for="param in item.DataModel.DefaultParameters.filter(p => !p.ValueIsReadOnly && p.Value !== undefined && p.Value !== '')" :key="param.Name">
                            <code>{{ param.ValueIsReadOnly ?   'Readonly:':''}}{{ param.Name }}</code> ({{ param.ValueType }}) - {{param.Type}} {{ param.Description }}  {{ param.Value  ?  ' ['+param.Name +' = ' + param.Value+']' : ''}}
                        </li>
                        <li v-if="item.DataModel.OrderDynamicParemters" >
                            <code>OrderBy:[{FieldName:"FieldName",OrderByType:0}]  0 Asc, 1 Desc </code>
                        </li>
                    </ul>
                </div>
            </div>
        </section>
    </div>
</div>
<script>
    new Vue({
        el: '#apibox',
        data: {
            data: null,
            error: null
        },
        mounted() {
            // 在组件加载时触发数据获取
            this.fetchData(null);
        },
        methods: {
            fetchData(name) {
                var urlParams = new URLSearchParams(window.location.search);
                var idFromUrl = urlParams.get('InterfaceCategoryId');
                var url = url = '/PrivateReZeroRoute/100003/GetInternalInterfaceList?IsInitialized=1';
                var urlParameters = "&" + tools.objectToQueryString({
                    Name: txtSearch.value,
                    InterfaceCategoryId: idFromUrl
                });
                url = url + urlParameters;
                axios.get(url, jwHeader)
                    .then(response => {
                        this.data = response.data;
                        this.error = null;
                    })
                    .catch(error => {
                        this.error = error.message;
                        this.data = null;
                    });
            },
            tryItOut(item) {
                window.open('/rezero/try_api.html?id=' + item.Id, '_blank');
            },
            onSearch: function () {
                this.fetchData(txtSearch.value);
            },
            toggleEndpoint(event) {
                var $currentTarget = $(event.currentTarget);
                var $parent = $currentTarget.parent();
                var $parameterElements = $parent.find(".parameter-title, .parameter");
                var $textEle = $currentTarget.find(".openapi");
                var isTextPlus = $textEle.text() === "+"; 
                $parameterElements.toggle(100);
                $textEle.text(isTextPlus ? "-" : "+").toggleClass("reduction", isTextPlus);
            },
        }
    });
</script>
