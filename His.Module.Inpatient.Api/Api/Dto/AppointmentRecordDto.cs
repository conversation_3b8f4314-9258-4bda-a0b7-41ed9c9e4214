using System.ComponentModel.DataAnnotations;

namespace His.Module.Inpatient;

/// <summary>
/// 住院预约输出参数
/// </summary>
public class AppointmentRecordDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 入院途径  字典  InpatientWayType
    /// </summary>
    public string?   InpatientWay { get; set; }
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    /// <summary>
    /// 门诊挂号记录id
    /// </summary>
    public long? OutpatientRegisterId { get; set; }
    
    /// <summary>
    /// 就诊id
    /// </summary>
    public long? OutpatientVisitId { get; set; }
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }
    /// <summary>
    /// 就诊号
    /// </summary>
   public string? VisitNo { get; set; }
    /// <summary>
    /// 预约时间
    /// </summary>
    public DateTime? AppointmentTime { get; set; }
    
    /// <summary>
    /// 证件类型
    /// </summary>
    public string? IdCardType { get; set; }
    
    /// <summary>
    /// 证件号码
    /// </summary>
    public string? IdCardNo { get; set; }
    //
    /// <summary>
    /// 就诊卡id
    /// </summary>
     public virtual long? MedicalCardId { get; set; }
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
     public virtual string? MedicalCardNo { get; set; }
    /// <summary>
    /// 门诊号
    /// </summary>
     public virtual string? OutpatientNo { get; set; }
    
    // /// <summary>
    // /// 保险号
    // /// </summary>
    // public string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预约科室ID
    /// </summary>
    [Required]
    public long? DeptId { get; set; }
    
    /// <summary>
    /// 预约科室代码
    /// </summary>
    public string? DeptCode { get; set; }
    
    /// <summary>
    /// 预约科室名称
    /// </summary>
    public string? DeptName { get; set; }
    
    /// <summary>
    /// 预约医生ID
    /// </summary>
    public long? DoctorId { get; set; }
    
    /// <summary>
    /// 预约医生代码
    /// </summary>
    public string? DoctorCode { get; set; }
    
    /// <summary>
    /// 预约医生姓名
    /// </summary>
    public string? DoctorName { get; set; }
    
    /// <summary>
    /// 诊断代码
    /// </summary>
    public string? DiagnosticCode { get; set; }
    
    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    /// <summary>
    /// 费用类别ID
    /// </summary>
    public virtual long? FeeId { get; set; }
    // /// <summary>
    // /// 状态
    // /// </summary>
    // public int? Status { get; set; }
    //
    // /// <summary>
    // /// 创建组织ID
    // /// </summary>
    // public long? CreateOrgId { get; set; }
    //
    // /// <summary>
    // /// 创建组织名称
    // /// </summary>
    // public string? CreateOrgName { get; set; }
    //
    // /// <summary>
    // /// 创建时间
    // /// </summary>
    // public DateTime? CreateTime { get; set; }
    //
    // /// <summary>
    // /// 创建者Id
    // /// </summary>
    // public long? CreateUserId { get; set; }
    //
    // /// <summary>
    // /// 创建者姓名
    // /// </summary>
    // public string? CreateUserName { get; set; }
    //
    // /// <summary>
    // /// 更新时间
    // /// </summary>
    // public DateTime? UpdateTime { get; set; }
    //
    // /// <summary>
    // /// 修改者Id
    // /// </summary>
    // public long? UpdateUserId { get; set; }
    //
    // /// <summary>
    // /// 修改者姓名
    // /// </summary>
    // public string? UpdateUserName { get; set; }
    //
    // /// <summary>
    // /// 软删除
    // /// </summary>
    // public bool? IsDelete { get; set; }
    //
    // /// <summary>
    // /// 租户Id
    // /// </summary>
    // public long? TenantId { get; set; }
    
}
