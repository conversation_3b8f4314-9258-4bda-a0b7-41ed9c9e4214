using His.Module.OutpatientDoctor.Service.Dto;

namespace His.Module.OutpatientDoctor;

/// <summary>
/// 门诊退费申请输出参数
/// </summary>
public class RefundApplyOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 退费申请ID
    /// </summary>
    public long? ChargeId { get; set; }    
    
    /// <summary>
    /// 退费申请类型
    /// </summary>
    public   string? BillingType { get; set; }
    /// <summary>
    /// 单据Id(处方id)
    /// </summary> 
    public   long? BillingId { get; set; }
    
    /// <summary>
    /// 退费申请单号
    /// </summary>
    public string? ApplyNo { get; set; }    
    
    /// <summary>
    /// 退费申请时间
    /// </summary>
    public DateTime? ApplyTime { get; set; }    
     
    
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string? CardNo { get; set; }    
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }    
    
    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }    
    
    /// <summary>
    /// 挂号ID
    /// </summary>
    public long? RegisterId { get; set; }    
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }    
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }    
    
    /// <summary>
    /// 退费原因
    /// </summary>
    public string? ApplyReason { get; set; }    
    
    /// <summary>
    /// 状态 0 新增待审核 1 审核中 2 审核完成
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 审核状态 表refund_audit 状态
    /// </summary>
    public int? AuditStatus { get; set; }    
    
    /// <summary>
    /// 创建机构ID
    /// </summary>
    public long? CreateOrgId { get; set; }    
    
    /// <summary>
    /// 创建机构名称
    /// </summary>
    public string? CreateOrgName { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    

    /// <summary>
    /// 发票号
    /// </summary>
    public virtual string? InvoiceNumber { get; set; }

    /// <summary>
    /// 总金额
    /// </summary>
    public virtual decimal? TotalAmount { get; set; }

    /// <summary>
    /// 支付渠道
    /// </summary>
    public virtual int? PayChannel { get; set; }

    /// <summary>
    /// 支付方式1Id
    /// </summary>
    public virtual long? PayMethod1Id { get; set; }

    /// <summary>
    /// 支付方式1金额
    /// </summary>
    public virtual decimal? PayAmount1 { get; set; }

    /// <summary>
    /// 支付方式2Id
    /// </summary>
    public virtual long? PayMethod2Id { get; set; }

    /// <summary>
    /// 支付方式2金额
    /// </summary>
    public virtual decimal? PayAmount2 { get; set; }

    /// <summary>
    /// 打印次数
    /// </summary>
    public virtual Int16? PrintNumber { get; set; }

    /// <summary>
    /// 打印时间
    /// </summary>
    public virtual DateTime? PrintTime { get; set; }

    /// <summary>
    /// 打印者Id
    /// </summary>
    public virtual long? PrintUserId { get; set; }
 

    /// <summary>
    /// 是否日结
    /// </summary>
    public virtual YesNoEnum? DailySettle { get; set; }

    /// <summary>
    /// 日结Id
    /// </summary>
    public virtual long? DailySettleId { get; set; }

    /// <summary>
    /// 收费类型 1收费 0挂号
    /// </summary>
    public virtual Int16? Type { get; set; }

    /// <summary>
    /// 退费发票号
    /// </summary>
    public virtual string? RefundInvoiceNumber { get; set; }
 
     
    /// <summary>
    /// 执行科室Id
    /// </summary>
    public virtual long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public virtual long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public virtual long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public virtual long? BillingDoctorId { get; set; }
    
    /// <summary>
    /// 单据号
    /// </summary>
    public virtual string? BillingNo { get; set; }
    /// <summary>
    /// 开单时间
    /// </summary>
    public virtual DateTime? BillingTime { get; set; }
          
    /// <summary>
    /// 开单时间
    /// </summary>
    public virtual DateTime? ExecuteTime { get; set; }
    
    public List<ChargeDetailOutput>? Details { get; set; }
    /// <summary>
    /// 是否有审核权限
    /// </summary>
    public bool HasAuditPermission { get; set; } = false;
    
    public long? FlowRoleId { get; set; }
    
    public string? FlowRoleCode { get; set; }
    public string? FlowRoleName { get; set; }
    public long? FlowUserId { get; set; }
    public string? FlowUserName { get; set; }
    

}

