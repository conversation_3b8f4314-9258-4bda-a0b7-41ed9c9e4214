namespace His.Module.OutpatientDoctor.Service.SkinTest.Dto;

public class SkinTestResultOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 处方号
    /// </summary>
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    public string? DiagnosticName { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>

    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生姓名
    /// </summary>
    public string? BillingDoctorName { get; set; }
}

public class SkinTestResultDetailOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 处方主表Id
    /// </summary>
    public long? PrescriptionId { get; set; }
    /// <summary>
    /// 药品Id
    /// </summary>
    public long? DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 单次量
    /// </summary>
    public decimal? SingleDose { get; set; }
    /// <summary>
    /// 单次量单位
    /// </summary>
    public string? SingleDoseUnit { get; set; }

    /// <summary>
    /// 给药途径Id
    /// </summary>
    public long? MedicationRoutesId { get; set; }

    /// <summary>
    /// 给药途径名称
    /// </summary>
    public string? MedicationRoutesName { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    public string? FrequencyName { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>
    public int? SkinTestResults { get; set; }
}