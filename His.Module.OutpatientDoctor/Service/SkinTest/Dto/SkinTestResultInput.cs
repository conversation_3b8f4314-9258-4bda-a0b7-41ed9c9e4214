namespace His.Module.OutpatientDoctor.Service.SkinTest.Dto;

/// <summary>
/// 皮试结果录入输入参数
/// </summary>
public class AddSkinTestResultInput
{
    /// <summary>
    /// 处方详情Id
    /// </summary>
    [Required(ErrorMessage = "处方详情Id不能为空")]
    public long PrescriptionDetailId { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>
    [Required(ErrorMessage = "皮试结果不能为空")]
    public string SkinTestResult { get; set; }

    /// <summary>
    /// 皮试时间
    /// </summary>
    public DateTime? SkinTestTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 皮试方法
    /// </summary>
    [MaxLength(100, ErrorMessage = "皮试方法长度不能超过100字符")]
    public string? SkinTestMethod { get; set; }

    /// <summary>
    /// 皮试剂量
    /// </summary>
    [MaxLength(50, ErrorMessage = "皮试剂量长度不能超过50字符")]
    public string? SkinTestDose { get; set; }

    /// <summary>
    /// 反应描述
    /// </summary>
    [MaxLength(1000, ErrorMessage = "反应描述长度不能超过1000字符")]
    public string? ReactionDescription { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注长度不能超过500字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 批量皮试结果录入输入参数
/// </summary>
public class BatchAddSkinTestResultInput
{
    /// <summary>
    /// 处方详情Id列表
    /// </summary>
    [Required(ErrorMessage = "处方详情Id列表不能为空")]
    public List<long> PrescriptionDetailIds { get; set; }

    /// <summary>
    /// 皮试结果 1阴性 2阳性 3可疑
    /// </summary>
    [Required(ErrorMessage = "皮试结果不能为空")]
    public int SkinTestResult { get; set; }

    /// <summary>
    /// 皮试时间
    /// </summary>
    public DateTime? SkinTestTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 皮试方法
    /// </summary>
    [MaxLength(100, ErrorMessage = "皮试方法长度不能超过100字符")]
    public string? SkinTestMethod { get; set; }

    /// <summary>
    /// 皮试剂量
    /// </summary>
    [MaxLength(50, ErrorMessage = "皮试剂量长度不能超过50字符")]
    public string? SkinTestDose { get; set; }

    /// <summary>
    /// 反应描述
    /// </summary>
    [MaxLength(1000, ErrorMessage = "反应描述长度不能超过1000字符")]
    public string? ReactionDescription { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(500, ErrorMessage = "备注长度不能超过500字符")]
    public string? Remark { get; set; }
}

/// <summary>
/// 撤销皮试结果输入参数
/// </summary>
public class CancelSkinTestResultInput
{
    /// <summary>
    /// 处方详情Id
    /// </summary>
    [Required(ErrorMessage = "处方详情Id不能为空")]
    public long PrescriptionDetailId { get; set; }

    /// <summary>
    /// 撤销原因
    /// </summary>
    [Required(ErrorMessage = "撤销原因不能为空")]
    [MaxLength(200, ErrorMessage = "撤销原因长度不能超过200字符")]
    public string CancelReason { get; set; }
}

/// <summary>
/// 皮试结果查询输入参数
/// </summary>
public class PageSkinTestResultRecordInput : BasePageInput
{
    /// <summary>
    /// 患者姓名
    /// </summary>
    public string? PatientName { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }

    /// <summary>
    /// 皮试结果
    /// </summary>
    public string? SkinTestResult { get; set; }

    /// <summary>
    /// 皮试时间范围
    /// </summary>
    public DateTime?[] SkinTestTimeRange { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
}

public class PageSkinTestResultInput : BasePageInput
{
    /// <summary>
    /// 处方时间范围
    /// </summary>
    public DateTime?[] PrescriptionTimeRange { get; set; }

    /// <summary>
    /// 是否皮试
    /// </summary>
    public int? IsSkinTest { get; set; }

    /// <summary>
    /// 开单科室
    /// </summary>
    public long? BillingDeptId { get; set; }
}

public class SkinTestResultDetailInput
{
    /// <summary>
    /// 处方主表Id
    /// </summary>
    [Required(ErrorMessage = "处方主表Id不能为空")]
    public long PrescriptionId { get; set; }
}