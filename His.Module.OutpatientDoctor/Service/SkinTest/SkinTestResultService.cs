using His.Module.OutpatientDoctor.Service.SkinTest.Dto;
namespace His.Module.OutpatientDoctor.Service.SkinTest;

/// <summary>
/// 皮试结果服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class SkinTestResultService(
    SqlSugarRepository<PrescriptionMain> prescriptionMainRep,
    SqlSugarRepository<PrescriptionDetail> prescriptionDetail) : IDynamicApiController, ITransient
{

    /// <summary>
    /// 分页查询需要进行皮试的处方 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询需要进行皮试的处方")]
    [ApiDescriptionSettings(Name = "Page")]
    [HttpPost]
    public async Task<SqlSugarPagedList<SkinTestResultOutput>> Page(PageSkinTestResultInput input)
    {
        var query = prescriptionMainRep.AsQueryable()
            .LeftJoin<PrescriptionDetail>((u, a) => u.Id == a.PrescriptionId)
            .WhereIF(input.PrescriptionTimeRange?.Length == 2,
                (u, a) => u.CreateTime >= input.PrescriptionTimeRange[0] &&
                          u.CreateTime <= input.PrescriptionTimeRange[1])
            .WhereIF(input.BillingDeptId.HasValue, (u, a) => u.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.IsSkinTest.HasValue, (u, a) => a.SkinTestResults == null)
            .Where((u, a) => a.IsSkinTest == 1)
            .Select<SkinTestResultOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }
    /// <summary>
    /// 查询需要进行皮试的处方详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询需要进行皮试的处方详情")]
    [ApiDescriptionSettings(Name = "Detail")]
    [HttpPost]
    public async Task<List<SkinTestResultDetailOutput>> Detail(SkinTestResultDetailInput input)
    {
        return await prescriptionDetail.AsQueryable()
            .Where(u => u.PrescriptionId == input.Id)
            .Select<SkinTestResultDetailOutput>()
            .ToListAsync();
    }


}