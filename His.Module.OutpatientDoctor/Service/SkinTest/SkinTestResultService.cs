using Furion.DatabaseAccessor;
using His.Module.OutpatientDoctor.Service.SkinTest.Dto;
namespace His.Module.OutpatientDoctor.Service.SkinTest;

/// <summary>
/// 皮试结果服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class SkinTestResultService(
    SqlSugarRepository<PrescriptionMain> prescriptionMainRep,
    SqlSugarRepository<PrescriptionDetail> prescriptionDetailRep,
    SqlSugarRepository<SkinTestResult> skinTestResultRep,
    UserManager userManager) : IDynamicApiController, ITransient
{

    /// <summary>
    /// 分页查询需要进行皮试的处方 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询需要进行皮试的处方")]
    [ApiDescriptionSettings(Name = "Page")]
    [HttpPost]
    public async Task<SqlSugarPagedList<SkinTestResultOutput>> Page(PageSkinTestResultInput input)
    {
        var query = prescriptionMainRep.AsQueryable()
            .LeftJoin<PrescriptionDetail>((u, a) => u.Id == a.PrescriptionId)
            .WhereIF(input.PrescriptionTimeRange?.Length == 2,
                (u, a) => u.PrescriptionTime >= input.PrescriptionTimeRange[0] &&
                          u.PrescriptionTime <= input.PrescriptionTimeRange[1])
            .WhereIF(input.BillingDeptId.HasValue, (u, a) => u.BillingDeptId == input.BillingDeptId)
            .WhereIF(input.IsSkinTest.HasValue, (u, a) => a.SkinTestResult == null)
            .Where((u, a) => a.IsSkinTest == 1)
            .Select<SkinTestResultOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }
    /// <summary>
    /// 查询需要进行皮试的处方详情 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询需要进行皮试的处方详情")]
    [ApiDescriptionSettings(Name = "Detail")]
    [HttpGet]
    public async Task<List<SkinTestResultDetailOutput>> Detail([FromQuery] SkinTestResultDetailInput input)
    {
        return await prescriptionDetailRep.AsQueryable()
            .Where(u => u.PrescriptionId == input.PrescriptionId)
            .Where(u => u.IsSkinTest == 1)
            .Select<SkinTestResultDetailOutput>()
            .ToListAsync();
    }

    /// <summary>
    /// 录入皮试结果 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("录入皮试结果")]
    [ApiDescriptionSettings(Name = "Add")]
    [HttpPost]
    [UnitOfWork]
    public async Task Add(AddSkinTestResultInput input)
    {
        // 查询处方详情
        var prescriptionDetail = await prescriptionDetailRep.AsQueryable()
            .LeftJoin<PrescriptionMain>((pd, pm) => pd.PrescriptionId == pm.Id)
            .Where((pd, pm) => pd.Id == input.PrescriptionDetailId)
            .Select((pd, pm) => new
            {
                pd.Id,
                pd.PrescriptionId,
                pd.DrugId,
                pd.DrugCode,
                pd.DrugName,
                pd.IsSkinTest,
                pm.PatientId,
                pm.PatientName,
                pm.VisitNo
            })
            .FirstAsync();

        if (prescriptionDetail == null)
            throw Oops.Oh("未找到相关处方详情");

        // 验证是否需要皮试
        if (prescriptionDetail.IsSkinTest != 1)
            throw Oops.Oh($"药品【{prescriptionDetail.DrugName}】不需要皮试");

        // 验证是否已经录入过皮试结果
        var existingResult = await skinTestResultRep.AsQueryable()
            .Where(x => x.PrescriptionDetailId == input.PrescriptionDetailId && x.Status == 1)
            .FirstAsync();

        if (existingResult != null)
            throw Oops.Oh($"药品【{prescriptionDetail.DrugName}】已录入皮试结果");

        // 获取当前用户信息
        var staffId = userManager.UserId;
        var staffName = userManager.RealName;

        // 创建皮试结果记录
        var skinTestResult = new SkinTestResult
        {
            PrescriptionDetailId = input.PrescriptionDetailId,
            PrescriptionId = prescriptionDetail.PrescriptionId,
            DrugId = prescriptionDetail.DrugId,
            DrugCode = prescriptionDetail.DrugCode,
            DrugName = prescriptionDetail.DrugName,
            PatientId = prescriptionDetail.PatientId,
            PatientName = prescriptionDetail.PatientName,
            VisitNo = prescriptionDetail.VisitNo,
            SkinTestResultValue = input.SkinTestResult,
            SkinTestTime = input.SkinTestTime ?? DateTime.Now,
            SkinTestStaffId = staffId,
            SkinTestStaffName = staffName,
            SkinTestMethod = input.SkinTestMethod,
            SkinTestDose = input.SkinTestDose,
            ReactionDescription = input.ReactionDescription,
            Remark = input.Remark,
            Status = 1
        };

        await skinTestResultRep.InsertAsync(skinTestResult);

        // 同时更新处方详情表的皮试结果字段（保持兼容性）
        await prescriptionDetailRep.AsUpdateable()
            .SetColumns(x => new PrescriptionDetail
            {
                SkinTestResult = input.SkinTestResult, SkinTestTime = input.SkinTestTime ?? DateTime.Now
            })
            .Where(x => x.Id == input.PrescriptionDetailId)
            .ExecuteCommandAsync();
    }



    /// <summary>
    /// 撤销皮试结果 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("撤销皮试结果")]
    [ApiDescriptionSettings(Name = "Cancel")]
    [HttpPost]
    [UnitOfWork]
    public async Task Cancel(CancelSkinTestResultInput input)
    {
        var skinTestResult = await skinTestResultRep.GetByIdAsync(input.Id);
        if (skinTestResult == null)
            throw Oops.Oh("未找到相关皮试结果记录");

        if (skinTestResult.Status == 2)
            throw Oops.Oh("该皮试结果已被撤销");

        // 获取当前用户信息
        var staffId = userManager.UserId;
        var staffName = userManager.RealName;

        // 更新撤销信息
        skinTestResult.Status = 2;
        skinTestResult.CancelTime = DateTime.Now;
        skinTestResult.CancelStaffId = staffId;
        skinTestResult.CancelStaffName = staffName;
        skinTestResult.CancelReason = input.CancelReason;

        await skinTestResultRep.AsUpdateable(skinTestResult)
            .UpdateColumns(x => new
            {
                x.Status,
                x.CancelTime,
                x.CancelStaffId,
                x.CancelStaffName,
                x.CancelReason
            })
            .ExecuteCommandAsync();

        // 同时清空处方详情表的皮试结果字段
        await prescriptionDetailRep.AsUpdateable()
            .SetColumns(x => new PrescriptionDetail
            {
                SkinTestResult = null, SkinTestTime = null
            })
            .Where(x => x.Id == skinTestResult.PrescriptionDetailId)
            .ExecuteCommandAsync();
    }

    // /// <summary>
    // /// 分页查询皮试结果记录 🔖
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("分页查询皮试结果记录")]
    // [ApiDescriptionSettings(Name = "PageRecord")]
    // [HttpPost]
    // public async Task<SqlSugarPagedList<SkinTestResultRecordOutput>> PageRecord(PageSkinTestResultRecordInput input)
    // {
    //     var query = skinTestResultRep.AsQueryable()
    //         .WhereIF(!string.IsNullOrEmpty(input.PatientName), x => x.PatientName.Contains(input.PatientName))
    //         .WhereIF(!string.IsNullOrEmpty(input.VisitNo), x => x.VisitNo.Contains(input.VisitNo))
    //         .WhereIF(!string.IsNullOrEmpty(input.DrugName), x => x.DrugName.Contains(input.DrugName))
    //         .WhereIF(!string.IsNullOrWhiteSpace(input.SkinTestResult), x => x.SkinTestResultValue == input.SkinTestResult)
    //         .WhereIF(input.Status.HasValue, x => x.Status == input.Status)
    //         .WhereIF(input.SkinTestTimeRange?.Length == 2,
    //             x => x.SkinTestTime >= input.SkinTestTimeRange[0] &&
    //                  x.SkinTestTime <= input.SkinTestTimeRange[1])
    //         .Select<SkinTestResultRecordOutput>();
    //
    //     return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    // }

}