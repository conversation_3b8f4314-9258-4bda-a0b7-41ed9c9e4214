using Furion.DatabaseAccessor;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Dto;
using His.Module.OutpatientDoctor.OtherModelEntity;
using His.Module.Pharmacy.Api.DrugInventory;
namespace His.Module.OutpatientDoctor.Service;

/// <summary>
/// 处表服务 🧩
/// </summary>
[ApiDescriptionSettings(OutpatientDoctorConst.GroupName, Order = 100)]
public class TemplatePrescriptionService(
    UserManager userManager,
    IChargeApi chargeApi,
    SqlSugarRepository<DrugStorage> drugStorageRep,
    SqlSugarRepository<TemplatePrescriptionMain> templatePrescriptionMainRep,
    SqlSugarRepository<TemplatePrescriptionDetail> templatePrescriptionDetailRep,
    IDrugInventoryApi drugInventoryApi,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;
 

    /// <summary>
    /// 增加处方表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询处方模板列表")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    [UnitOfWork]
    public async Task<List<TemplatePrescriptionMain>> GetPrescriptionList(QueryTemplatePrescriptionInput input)
    {

       var list=await templatePrescriptionMainRep.AsQueryable()
            .ToListAsync();
        
        return list;
    }
    /// <summary>
    /// 增加处方表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询处方模板明细")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    [UnitOfWork]
    public async Task<List<TemplatePrescriptionDetail>> GetPrescriptionDetail([FromQuery] long id)
    {
 
        var details = await templatePrescriptionDetailRep
            .AsQueryable().Where(u => u.TemplateId == id).ToListAsync();
 
        return details;
    }
    /// <summary>
    /// 增加处方表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加模板模板")]
    [ApiDescriptionSettings(Name = "Save"), HttpPost]
    [UnitOfWork]
    public async Task<long> SavePrescription(TemplatePrescriptionInput input)
    {
        var entity = input.Main.Adapt<TemplatePrescriptionMain>();
 
        entity.Status = 1; // 默认为1
        // 默认单个药房 
        entity.StorageId = input.Details.FirstOrDefault()?.StorageId;
        entity.StorageName = input.Details.FirstOrDefault()?.StorageName;

        if (input.Main.Id.HasValue && input.Main.Id > 0)
        {
            await templatePrescriptionMainRep.UpdateAsync(entity) ;
            await templatePrescriptionDetailRep.AsDeleteable().Where(u => u.TemplateId == entity.Id)
                .ExecuteCommandAsync();
        }
        else
        {  entity.Id = 0; //修改时重新插入
            await templatePrescriptionMainRep.InsertAsync(entity) ;
        }

      
        var details = input.Details.Adapt<List<TemplatePrescriptionDetail>>();
        foreach (var item in details)
        {
            item.Id = 0; //修改时重新插入
            item.TemplateId = entity.Id;
            await templatePrescriptionDetailRep.InsertAsync(item);

        }
        return entity.Id;
    }


    /// <summary>
    /// 删除处方主表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除模板处方主表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeletePrescriptionMainInput input)
    {
        var main=await templatePrescriptionMainRep.GetFirstAsync(u => u.Id == input.Id);

        
        if(main.TemplateScope==3 && main.CreateUserId!=    userManager.UserId){
            
            throw Oops.Oh("只能删除自己创建的模板");
        }

        await templatePrescriptionMainRep.FakeDeleteAsync(main); 
        await templatePrescriptionDetailRep.AsDeleteable()
            .Where(u => u.TemplateId == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除处方明细表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除模板处方明细表")]
    [ApiDescriptionSettings(Name = "DeleteDetail"), HttpPost]
    public async Task DeleteDetail(DeletePrescriptionDetailInput input)
    { 
        var detail=await templatePrescriptionDetailRep.GetFirstAsync(u => u.Id == input.Id);
        var main=await templatePrescriptionMainRep.GetFirstAsync(u => u.Id == detail.TemplateId);

        
        if(main.TemplateScope==3 && main.CreateUserId!=    userManager.UserId){
            
            throw Oops.Oh("只能删除自己创建的模板");
        }

        await templatePrescriptionDetailRep.DeleteByIdAsync(input.Id); 

        //await _prescriptionDetailRep.DeleteAsync(entity);   //真删除
    }
 
 
 
}