using System.ComponentModel;

namespace His.Module.OutpatientDoctor.Enum;

/// <summary>
/// 皮试结果枚举
/// </summary>
[Description("皮试结果枚举")]
public enum SkinTestResultEnum
{
    /// <summary>
    /// 阴性
    /// </summary>
    [Description("阴性")]
    Negative = 1,

    /// <summary>
    /// 阳性
    /// </summary>
    [Description("阳性")]
    Positive = 2,

    /// <summary>
    /// 可疑
    /// </summary>
    [Description("可疑")]
    Suspicious = 3
}

/// <summary>
/// 皮试结果状态枚举
/// </summary>
[Description("皮试结果状态枚举")]
public enum SkinTestResultStatusEnum
{
    /// <summary>
    /// 有效
    /// </summary>
    [Description("有效")]
    Valid = 1,

    /// <summary>
    /// 撤销
    /// </summary>
    [Description("撤销")]
    Cancelled = 2
}
