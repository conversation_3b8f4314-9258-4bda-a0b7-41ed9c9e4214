using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.Registration.Const;
using His.Module.Registration.Entity;
using Microsoft.AspNetCore.Http;
using SKIT.FlurlHttpClient.Wechat.TenpayV3.Models;

namespace His.Module.Registration;

/// <summary>
/// 医生排班计划服务 🧩
/// </summary>
[ApiDescriptionSettings(RegistrationConst.GroupName, Order = 100, Name = "registration/schedulingPlan")]
public class SchedulingPlanService(
    SqlSugarRepository<SchedulingPlan> schedulingPlanRep,
    SqlSugarRepository<SchedulingPlanTemp> schedulingPlanTempRep,
    SqlSugarRepository<TimePeriod> timePeriodRep,
    TriageQueueService triageQueueService,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    private readonly ISqlSugarClient _sqlSugarClient = sqlSugarClient;

    /// <summary>
    /// 分页查询医生排班计划 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询医生排班计划")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<SchedulingPlanOutput>> Page(PageSchedulingPlanInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = schedulingPlanRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.DoctorName.Contains(input.Keyword) || u.TimePeriodCode.Contains(input.Keyword) ||
                     u.TimePeriodName.Contains(input.Keyword) || u.RegCategoryName.Contains(input.Keyword) ||
                     u.DeptName.Contains(input.Keyword) || u.WeekDay.Contains(input.Keyword) ||
                     u.RoomName.Contains(input.Keyword) || u.IpAddress.Contains(input.Keyword) ||
                     u.Remark.Contains(input.Keyword))
            .WhereIF(input.OutpatientDate != null, u => u.OutpatientDate.Value.Date == input.OutpatientDate.Date)
            .WhereIF(input.DeptId > 0, u => u.DeptId == input.DeptId)
            .Select<SchedulingPlanOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取排班科室 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取排班科室")]
    [ApiDescriptionSettings(Name = "GetSchedulingDept"), HttpPost]
    public async Task<dynamic> GetSchedulingDept(QuerySchedulingOfRegisterInput input)
    {
        // 查询今天有排班计划的科室 
        var today = input.OutpatientDate ?? DateTime.Today;

        var time = input.OutpatientDate?.TimeOfDay ?? DateTime.Now.TimeOfDay;

        var list = await schedulingPlanRep.AsQueryable().Where(u => u.OutpatientDate.Value.Date == today.Date)
            .Where(u => u.StartTime <= time && u.EndTime >= time)
            .Select(u => new { Id = u.DeptId, Name = u.DeptName }).Distinct().ToListAsync();
        return list;
    }

    /// <summary>
    /// 获取排班科室的医生 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取排班科室的医生")]
    [ApiDescriptionSettings(Name = "GetSchedulingDoctor"), HttpPost]
    public async Task<dynamic> GetSchedulingDoctor(QuerySchedulingOfRegisterInput input)
    {
        // 查询今天有排班计划的科室 
        var today = input.OutpatientDate ?? DateTime.Today;


        if (input.DeptId == null)
        {
            throw Oops.Oh("请选择科室！");
        }

        var time = input.OutpatientDate?.TimeOfDay ?? DateTime.Now.TimeOfDay;
        var list = await schedulingPlanRep.AsQueryable().Where(u => u.OutpatientDate == today
                                                                    && u.DeptId == input.DeptId
            )
            .Where(u => u.StartTime <= time && u.EndTime >= time)
            .Select(u => new
            {
                u.Id,
                u.DeptId,
                u.DeptName,
                u.DoctorId,
                u.DoctorName
            }).Distinct().ToListAsync();
        return list;
    }

    /// <summary>
    /// 获取排班科室的医生挂号类别 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取排班科室的医生挂号类别")]
    [ApiDescriptionSettings(Name = "GetSchedulingDoctorRegCategory"), HttpPost]
    public async Task<dynamic> GetSchedulingDoctorRegCategory(QuerySchedulingOfRegisterInput input)
    {
        // 查询今天有排班计划的科室 
        var today = input.OutpatientDate ?? DateTime.Today;
        if (input.DeptId == null)
        {
            throw Oops.Oh("请选择科室！");
        }

        var time = input.OutpatientDate?.TimeOfDay ?? DateTime.Now.TimeOfDay;
        var list = await schedulingPlanRep.AsQueryable().Where(u => u.OutpatientDate == today
                                                                    && u.DeptId == input.DeptId
                                                                    && u.DoctorId == input.DoctorId
            )
            .Where(u => u.StartTime <= time && u.EndTime >= time)
            .Select(u => new
            {
                u.Id,
                u.DeptId,
                u.DeptName,
                u.DoctorId,
                u.DoctorName,
                u.RegCategoryId,
                u.RegCategoryName
            }).Distinct().ToListAsync();
        return list;
    }

    /// <summary>
    /// 获取医生排班计划详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取医生排班计划详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<SchedulingPlan> Detail([FromQuery] QueryByIdSchedulingPlanInput input)
    {
        return await schedulingPlanRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加医生排班计划 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加医生排班计划")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost,UnitOfWork]
    public async Task<long> Add(AddSchedulingPlanInput input)
    {
        var entity = input.Adapt<SchedulingPlan>();
        return await Add([entity]);
    }

    
    private async Task<long> Add(List<SchedulingPlan> list)
    {
        var r = 0l;
        if (list.Count > 1)
        {
            await schedulingPlanRep.InsertRangeAsync(list);
    
            r = list.Count;
        }
        else
        {
            var entity = list.First();
              r= await schedulingPlanRep.InsertAsync(entity) ? entity.Id : 0;
       
           
        }
        // 创建队列
        await  triageQueueService.CreateTriageQueue(list);
        return r;
    }

    /// <summary>
    /// 更新医生排班计划 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新医生排班计划")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateSchedulingPlanInput input)
    {
        var entity = input.Adapt<SchedulingPlan>();
        await schedulingPlanRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除医生排班计划 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除医生排班计划")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost,UnitOfWork]
    public async Task Delete(DeleteSchedulingPlanInput input)
    {
        var entity = await schedulingPlanRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await schedulingPlanRep.FakeDeleteAsync(entity); //假删除
 
        await triageQueueService.DeleteTriageQueue([entity.Id]);
        //await _schedulingPlanRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除医生排班计划 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除医生排班计划")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteSchedulingPlanInput> input)
    {
        var exp = Expressionable.Create<SchedulingPlan>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await schedulingPlanRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        var ids=list.Select(u =>  (long?)u.Id).ToList();
        var r= await schedulingPlanRep.FakeDeleteAsync(list); //假删除
        await triageQueueService.DeleteTriageQueue(ids);

        return r;
        //return await _schedulingPlanRep.DeleteAsync(list);   //真删除
    }


    /// <summary>
    /// 下载医生排班计划数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载医生排班计划数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportSchedulingPlanOutput>(), "医生排班计划导入模板");
    }

    /// <summary>
    /// 使用计划模板 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("使用模板")]
    [ApiDescriptionSettings(Name = "UsePlanTemp"), HttpPost]
    public async Task<bool> UsePlanTemp(UsePlanTempInput input)
    {
        var list = await
            schedulingPlanTempRep.AsQueryable()
                .WhereIF(input.DeptId > 0, u => u.DeptId == input.DeptId)
                .Where(u => u.WeekDay == input.WeekDay)
                .Select<SchedulingPlanTempOutput>().ToListAsync();
        if (list.Count == 0)
        {
            throw Oops.Oh("没有【" + input.WeekDay + "】" + input.DeptName + "的模板！");
        }

//验证重复 
        var exists = await schedulingPlanRep.AsQueryable()
            .WhereIF(input.DeptId is > 0, u => u.DeptId == input.DeptId)
            .Where(u =>
                u.OutpatientDate == input.OutpatientDate
            ).ToListAsync();
        if (exists.Any())
        {
            throw Oops.Oh("请删除现有数据后再使用模板！");
        }


        var inputList = list.Adapt<List<SchedulingPlan>>();
        foreach (var plan in inputList)
        {
            plan.Id = 0;
            plan.OutpatientDate = input.OutpatientDate;
        }

        await Add(inputList);
        return true;
    }
}