using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.Registration.Const;
using His.Module.Registration.Entity;
using His.Module.Registration.Service.ClinicRoom.Dto;
using Microsoft.AspNetCore.Http;

namespace His.Module.Registration;

/// <summary>
/// 诊室维护服务 🧩
/// </summary>
[ApiDescriptionSettings(RegistrationConst.GroupName, Order = 100)]
public class ClinicRoomService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<ClinicRoom> _clinicRoomRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public ClinicRoomService(SqlSugarRepository<ClinicRoom> clinicRoomRep, ISqlSugarClient sqlSugarClient)
    {
        _clinicRoomRep = clinicRoomRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询诊室维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询诊室维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<ClinicRoomOutput>> Page(PageClinicRoomInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _clinicRoomRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Name.Contains(input.Keyword) || u.Code.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.IpAddress.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IpAddress), u => u.IpAddress.Contains(input.IpAddress.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<ClinicRoomOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }
    /// <summary>
    /// 分页查询诊室维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询诊室")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    public async Task<List<ClinicRoomOutput>> List(PageClinicRoomInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _clinicRoomRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Name.Contains(input.Keyword) || u.Code.Contains(input.Keyword) || u.DeptName.Contains(input.Keyword) || u.IpAddress.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DeptName), u => u.DeptName.Contains(input.DeptName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IpAddress), u => u.IpAddress.Contains(input.IpAddress.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<ClinicRoomOutput>();
        return await query.OrderBuilder(input).ToListAsync();
    }
    /// <summary>
    /// 获取诊室维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取诊室维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<ClinicRoom> Detail([FromQuery] QueryByIdClinicRoomInput input)
    {
        return await _clinicRoomRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加诊室维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加诊室维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddClinicRoomInput input)
    {
        var entity = input.Adapt<ClinicRoom>();
      
        
        return await Save(entity);
    }
    
    private async Task<long> Save(ClinicRoom entity)
    {
       
        // 判断名称，编号，ip 地址是否重复 
        var name =await _clinicRoomRep.AsQueryable().Where(u => u.Name == entity.Name
                                                          && u.DeptId == entity.DeptId)
                .WhereIF(entity.Id != 0, u => u.Id != entity.Id)
                .FirstAsync()
            ;
        if(name != null) 
            throw Oops.Oh("诊室名称【"+ entity.Name +"】重复，请重新输入");
        if (entity.Code != null)
        {
            var code = await _clinicRoomRep.AsQueryable().Where(u => u.Code == entity.Code
                                                                     && u.DeptId == entity.DeptId)
                .WhereIF(entity.Id != 0, u => u.Id != entity.Id)
                .FirstAsync();
            if(code!= null)
                throw Oops.Oh("诊室编号【"+ entity.Code +"】重复，请重新输入");
        }
        
        var ipAddress = await _clinicRoomRep.AsQueryable().Where(
            u => u.IpAddress == entity.IpAddress)
            .WhereIF(entity.Id != 0, u => u.Id != entity.Id)
            .FirstAsync();
        if(ipAddress != null) 
            throw Oops.Oh("诊室Ip【"+ entity.IpAddress +"】重复，请重新输入");
        if(entity.Id == 0)
            await _clinicRoomRep.InsertAsync(entity);
        else
            await _clinicRoomRep.AsUpdateable(entity)
                .ExecuteCommandAsync();
        return  entity.Id;
    }

    /// <summary>
    /// 更新诊室维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新诊室维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateClinicRoomInput input)
    {
        var entity = input.Adapt<ClinicRoom>();
          await Save(entity);
    }

    /// <summary>
    /// 删除诊室维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除诊室维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteClinicRoomInput input)
    {
        var entity = await _clinicRoomRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _clinicRoomRep.FakeDeleteAsync(entity);   //假删除
        //await _clinicRoomRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除诊室维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除诊室维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteClinicRoomInput> input)
    {
        var exp = Expressionable.Create<ClinicRoom>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _clinicRoomRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _clinicRoomRep.FakeDeleteAsync(list);   //假删除
        //return await _clinicRoomRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出诊室维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出诊室维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageClinicRoomInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportClinicRoomOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "诊室维护导出记录");
    }
    
    /// <summary>
    /// 下载诊室维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载诊室维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportClinicRoomOutput>(), "诊室维护导入模板");
    }
    
    /// <summary>
    /// 导入诊室维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入诊室维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportClinicRoomInput, ClinicRoom>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<ClinicRoom>>();
                    
                    var storageable = _clinicRoomRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Name?.Length > 255, "诊室名称长度不能超过255个字符")
                        .SplitError(it => it.Item.Code?.Length > 255, "诊室编码长度不能超过255个字符")
                        .SplitError(it => it.Item.DeptName?.Length > 255, "科室名称长度不能超过255个字符")
                        .SplitError(it => it.Item.IpAddress?.Length > 255, "ip地址长度不能超过255个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
