using Admin.NET.Core.Service;
using Furion.DatabaseAccessor;
using His.Module.Registration.Const;
using His.Module.Registration.Entity;
using Microsoft.AspNetCore.Http;

namespace His.Module.Registration;

/// <summary>
/// 分诊队列服务 🧩
/// </summary>
[ApiDescriptionSettings(RegistrationConst.GroupName, Order = 100)]
public class TriageQueueService(
    SqlSugarRepository<TriageQueue> triageQueueRep,
    SqlSugarRepository<ClinicConsole> clinicConsoleRep,
    ISqlSugarClient sqlSugarClient)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 自动创建分诊队列
    /// </summary>
    /// <param name="list"></param>
    [ApiDescriptionSettings(IgnoreApi = true), HttpPost]
    public async Task CreateTriageQueue(List<SchedulingPlan> list)
    {
        // 使用默认诊台 
        var consoles =
            await clinicConsoleRep.AsQueryable().Where(u => u.Status == 1).ToListAsync();

        var queues = new List<TriageQueue>();
        foreach (var item in list)
        {
            var queue = item.Adapt<TriageQueue>();
            var console =
                consoles.Where(p => p.RoomId == item.RoomId)
                    .ToList().FirstOrDefault();
            if (console != null)
            {
                queue.ConsoleId = console.Id;
                queue.ConsoleName = console.Name;
            }

            queue.TimePeriodStartTime = item.OutpatientDate + item.StartTime;
            queue.TimePeriodEndTime = item.OutpatientDate + item.EndTime;
            queue.SchedulingPlanId = item.Id;
            queues.Add(queue);
        }

        await triageQueueRep.InsertRangeAsync(queues);
    }

    /// <summary>
    /// 删除已存在的分诊队列
    /// </summary>
    /// <param name="schedulingPlanIds"></param>
    [ApiDescriptionSettings(IgnoreApi = true), HttpPost]
    public async Task DeleteTriageQueue(List<long?> schedulingPlanIds)
    {
        var list=await triageQueueRep.AsQueryable()
           .Where(u => schedulingPlanIds.Contains(u.SchedulingPlanId))
           .ToListAsync();
         await triageQueueRep.FakeDeleteAsync(list); //假删除
     
    }

    /// <summary>
    /// 分页查询分诊队列 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询分诊队列")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<TriageQueueOutput>> Page(PageTriageQueueInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = triageQueueRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.RoomName.Contains(input.Keyword) || u.ConsoleName.Contains(input.Keyword) ||
                     u.TimePeriodCode.Contains(input.Keyword) || u.TimePeriodName.Contains(input.Keyword) ||
                     u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RoomName), u => u.RoomName.Contains(input.RoomName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ConsoleName),
                u => u.ConsoleName.Contains(input.ConsoleName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TimePeriodCode),
                u => u.TimePeriodCode.Contains(input.TimePeriodCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TimePeriodName),
                u => u.TimePeriodName.Contains(input.TimePeriodName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.SchedulingPlanId != null, u => u.SchedulingPlanId == input.SchedulingPlanId)
            .WhereIF(input.RoomId != null, u => u.RoomId == input.RoomId)
            .WhereIF(input.ConsoleId != null, u => u.ConsoleId == input.ConsoleId)
            .WhereIF(input.QueueNumber != null, u => u.QueueNumber == input.QueueNumber)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .WhereIF(input.TimePeriodId != null, u => u.TimePeriodId == input.TimePeriodId)
            .WhereIF(input.TimePeriodStartTimeRange?.Length == 2,
                u => u.TimePeriodStartTime >= input.TimePeriodStartTimeRange[0] &&
                     u.TimePeriodStartTime <= input.TimePeriodStartTimeRange[1])
            .WhereIF(input.TimePeriodEndTimeRange?.Length == 2,
                u => u.TimePeriodEndTime >= input.TimePeriodEndTimeRange[0] &&
                     u.TimePeriodEndTime <= input.TimePeriodEndTimeRange[1])
            .Select<TriageQueueOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 分页查询分诊队列 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("查询分诊队列")]
    [ApiDescriptionSettings(Name = "List"), HttpPost]
    public async Task<List<TriageQueueOutput>> List(PageTriageQueueInput input)
    {
        input.DeptId ??= long.Parse(App.User.FindFirst(ClaimConst.OrgId).Value ?? "0");
        var query = triageQueueRep.AsQueryable()
            .Where(u => u.RoomId == input.RoomId)
            .Where(u => u.TimePeriodStartTime <= DateTime.Now)
            .Where(u => u.TimePeriodEndTime >= DateTime.Now)
            .Select<TriageQueueOutput>();
        return await query.OrderBuilder(input).ToListAsync();
    }

    /// <summary>
    /// 获取分诊队列详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取分诊队列详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<TriageQueue> Detail([FromQuery] QueryByIdTriageQueueInput input)
    {
        return await triageQueueRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加分诊队列 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加分诊队列")]
    [ApiDescriptionSettings(Name = "create"), HttpPost]
    public async Task<long> Create(List<AddTriageQueueInput> input)
    {
        var entity = input.Adapt<TriageQueue>();
        return await triageQueueRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 增加分诊队列 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加分诊队列")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddTriageQueueInput input)
    {
        var entity = input.Adapt<TriageQueue>();
        return await triageQueueRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新分诊队列 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新分诊队列")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateTriageQueueInput input)
    {
        var entity = input.Adapt<TriageQueue>();
        await triageQueueRep.AsUpdateable(entity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除分诊队列 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除分诊队列")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteTriageQueueInput input)
    {
        var entity = await triageQueueRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await triageQueueRep.FakeDeleteAsync(entity); //假删除
        //await _triageQueueRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除分诊队列 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除分诊队列")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteTriageQueueInput> input)
    {
        var exp = Expressionable.Create<TriageQueue>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await triageQueueRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await triageQueueRep.FakeDeleteAsync(list); //假删除
 
    }

    /// <summary>
    /// 导出分诊队列记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出分诊队列记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageTriageQueueInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportTriageQueueOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "分诊队列导出记录");
    }

    /// <summary>
    /// 下载分诊队列数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载分诊队列数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportTriageQueueOutput>(), "分诊队列导入模板");
    }

    /// <summary>
    /// 导入分诊队列记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入分诊队列记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportTriageQueueInput, TriageQueue>(file, (list, markerErrorAction) =>
            {
                sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        return true;
                    }).Adapt<List<TriageQueue>>();

                    var storageable = triageQueueRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.RoomName?.Length > 255, "诊室名称长度不能超过255个字符")
                        .SplitError(it => it.Item.ConsoleName?.Length > 255, "分诊台名称长度不能超过255个字符")
                        .SplitError(it => it.Item.TimePeriodCode?.Length > 64, "时间段编码长度不能超过64个字符")
                        .SplitError(it => it.Item.TimePeriodName?.Length > 64, "时间段名称长度不能超过64个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}