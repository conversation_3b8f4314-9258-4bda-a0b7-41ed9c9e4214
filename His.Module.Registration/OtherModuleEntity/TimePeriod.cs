namespace His.Module.Registration.Entity;
/// <summary>
/// 排班时间段表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("time_period", "排班时间段表")]
public class TimePeriod : EntityTenant
{
    /// <summary>
    /// 时间段编码
    /// </summary>
    [SugarColumn(ColumnName = "time_period_code", ColumnDescription = "时间段编码", Length = 64)]
    public virtual string? TimePeriodCode { get; set; }

    /// <summary>
    /// 时间段名称
    /// </summary>
    [SugarColumn(ColumnName = "time_period_name", ColumnDescription = "时间段名称", Length = 64)]
    public virtual string? TimePeriodName { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 32)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 32)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnName = "start_time", ColumnDescription = "开始时间")]
    public virtual TimeSpan? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnName = "end_time", ColumnDescription = "结束时间")]
    public virtual TimeSpan? EndTime { get; set; }

    /// <summary>
    /// 时间段
    /// </summary>
    [SugarColumn(ColumnName = "time_period_detail", ColumnDescription = "时间段", Length = 32)]
    public virtual string? TimePeriodDetail { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }
    
    [SugarColumn(ColumnName = "default_app_limit", ColumnDescription = "默认预约数量")]
    public virtual int? DefaultAppLimit { get; set; }
    
    [SugarColumn(ColumnName = "default_reg_limit", ColumnDescription = "默认挂号数量")]
    public virtual int? DefaultRegLimit { get; set; }

}