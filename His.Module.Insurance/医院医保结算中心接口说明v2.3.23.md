地纬医院医保结算中心

接口服务方案

（2.3.23版本）

**山大地纬软件股份有限公司**

2024年11月

版本记录：

<div class="joplin-table-wrapper"><table><tbody><tr><td><p>日期</p></td><td><p>版本</p></td><td><p>作者</p></td><td><p>描述说明</p></td></tr><tr><td><p>2016/3/29</p></td><td><p>0.5.8</p></td><td><p>马秀华</p></td><td><ol><li>智能医保审核：增加必传入参p_yltclb</li></ol></td></tr><tr><td><p>2016/3/31</p></td><td><p>0.5.9</p></td><td><p>马秀华</p></td><td><ol><li>Login提供两种登录方式。</li><li>增加第六章 异常处理</li><li>第三章增加小节批量上传医嘱、批量删除医嘱</li></ol></td></tr><tr><td><p>2016/4/8</p></td><td><p>0.6.0</p></td><td><p>马秀华</p></td><td><ol><li>增加接口：查询个人账户余额信息，仅省直用</li></ol></td></tr><tr><td><p>2016/4/25</p></td><td><p>0.6.2</p></td><td><p>马秀华</p></td><td><ol><li>撤销门诊结算 返回结果集 删除字段report</li><li>门诊结算、门诊预结算 传入参数 增加字段p_ptmzskbz</li></ol></td></tr><tr><td><p>2016/5/9</p></td><td><p>0.6.4</p></td><td><p>马秀华</p></td><td><ol><li>查询医保疾病目录 返回结果集 增加字段mzdblb;</li><li>通用询问服务 返回结果集 修改字段说明；</li></ol></td></tr><tr><td><p><a id="_Hlk451254023"></a>2016/5/12</p></td><td><p>0.6.5</p></td><td><p>马秀华</p></td><td><ol><li>增加小节 打印单据</li></ol></td></tr><tr><td><p>2016/5/17</p></td><td><p>0.6.6</p></td><td><p>马秀华</p></td><td><ol><li><a id="OLE_LINK6"></a><a id="OLE_LINK5"></a>门诊结算<a id="OLE_LINK15"></a><a id="OLE_LINK14"></a>：增加非必传参数p_needjsd；</li><li>住院结算：增加非必传参数p_needjsd;</li><li>出院：增加非必传参数p_needcyd;</li><li>增加接口：打印门诊结算发票print_jsfp；</li><li>查询医院项目信息 返回结果集 增加字段 rqlb、xzbz、ypbz。</li><li>增加济南专用接口：查询医院项目信息（济南）和查询医保核心端目录（济南）</li></ol></td></tr><tr><td><p>2016/5/19</p></td><td><p>0.6.7</p></td><td><p>马秀华</p></td><td><ol><li>目录管理 查询接口（query_yyxm_info、query_yyxm_info_mz、query_ml、query_ml_mz、query_si_sick和query_ys）增加传入参数p_filetype.</li></ol></td></tr><tr><td><p>2016/5/20</p></td><td><p>0.6.8</p></td><td><p>马秀华</p></td><td><ol><li>增加接口 查询住院审批信息</li><li>去掉接口个人账户消费，合并到门诊结算接口中。</li></ol></td></tr><tr><td><p>2016/5/27</p></td><td><p>0.6.9</p></td><td><p>马秀华</p></td><td><ol><li>修改接口 获取社保卡余额:传入参数增加必传参数p_kh，p_grbh改为非必传参数;返回结果集去掉参数xm,grbh,增加参数kh</li></ol></td></tr><tr><td><p>2016/5/31</p></td><td><p>0.7.0</p></td><td><p>马秀华</p></td><td><ol><li>删除接口 修改社保卡密码。</li></ol></td></tr><tr><td><p>2016/6/8</p></td><td><p>0.7.1</p></td><td><p>马秀华</p></td><td><ol><li>打印门诊结算发票 修改传入参数为p_jshid</li></ol></td></tr><tr><td><p>2016/6/14</p></td><td><p>1.0.0</p></td><td><p>马秀华</p></td><td><ol><li>医保智能审核 返回结果集 增加参数 审核结果标志shjgFlag。</li></ol></td></tr><tr><td><p>2016/6/16</p></td><td><p>1.0.1</p></td><td><p>马秀华</p></td><td><ol><li>增加接口 查询药品审批信息</li></ol></td></tr><tr><td><p>2016/6/20</p></td><td><p>1.0.2</p></td><td><p>马秀华</p></td><td><ol><li>增加接口 新增或者更新操作人员</li></ol></td></tr><tr><td><p>2016/6/29</p></td><td><p>1.0.3</p></td><td><p>马秀华</p></td><td><ol><li>修改接口&nbsp;智能医保审核&nbsp;传入参数p_fees_ds中增加必传参数bzsl、bzdw、zhxs、yypc，修改入参dw改为jbdw。</li></ol></td></tr><tr><td><p>2016/6/29</p></td><td><p>1.0.4</p></td><td><p>马秀华</p></td><td><ol><li>修改接口 住院的费用上传 传入参数p_fyxx_ds中增加非必传潍坊专用参数yzzh、yzsxh。</li><li>修改接口 住院结算 传入参数中增加非必传潍坊专用参数p_cyyxzd。</li><li>修改接口 获取个人基本信息（无卡） 传入参数中p_xm改为非必传。</li></ol></td></tr><tr><td><p>2016/7/4</p></td><td><p>1.0.5</p></td><td><p>马秀华</p></td><td><ol><li>增加接口 查询门诊就医信息</li></ol></td></tr><tr><td><p>2016/7/14</p></td><td><p>1．0.6</p></td><td><p>gy</p></td><td><p>1.修改接口query_zybrfy，增加出参zje</p></td></tr><tr><td><p>2016/7/14</p></td><td><p>1．0.7</p></td><td><p>刘龙</p></td><td><ol><li>修改接口settlt_zy的入参，增加p_sfsygrzh，修改p_grzhzf的说明</li><li>修改接口outhosp的入参，增加p_sfsygrzh，增加p_grzhzf</li></ol></td></tr><tr><td><p>2016/7/18</p></td><td><p>1.0.8</p></td><td><p>马秀华</p></td><td><ol><li>修改接口 住院登记 传入参数p_zyfs中说明增加住院方式6：康复住院。</li><li>修改接口 智能医保审核 返回结果集中 shjgFlag改为小写shjgflag</li></ol></td></tr><tr><td><p>2016/7/21</p></td><td><p>1.0.9</p></td><td><p></p></td><td><ol><li>修改接口 查询在院病人信息 返回结果集中 增加出参ryzd</li></ol></td></tr><tr><td><p>2016/7/27</p></td><td><p>1.1.0</p></td><td><p>何希真</p></td><td><ol><li>增加新接口 打印住院自费明细单</li></ol></td></tr><tr><td><p>2016/7/29</p></td><td><p>1.1.1</p></td><td><p>何希真</p><p>刘龙</p><p>gy</p><p></p></td><td><ol><li>修改接口 撤销门诊结算，增加入参p_cxyy</li><li>修改接口 住院费用上传，增加入参p_fyly</li><li>修改接口 查询药品审批信息，出参spbz增加说明-1</li><li>修改接口 门诊预结算、门诊结算，入参p_mzlx增加说明</li></ol></td></tr><tr><td><p>2016/08/24</p></td><td><p>1.1.2</p></td><td><p>马秀华</p></td><td><ol><li>对于入参中需要读卡传入卡号的服务，增加说明：若由地纬DLL控制读卡器，p_kh可不传；若由his控制读卡器，p_kh为必传。</li></ol></td></tr><tr><td><p>2016/8/31</p></td><td><p>1.1.3</p></td><td><p>马秀华</p></td><td><ol><li>第一章 接口方案介绍 增加说明：接口文档目前分</li></ol><p>DLL版和WEB版，HIS系统可根据自己的需要选择合适的调用方式。</p><ol><li>智能医保审核 入参 p_fees_ds中增加必传参数dcyl、xmlx。</li></ol></td></tr><tr><td><p>2016/9/1</p></td><td><p>1.1.4</p></td><td><p>gy</p></td><td><ol><li>修改接口门诊预结算, 门诊结算, 住院费用上传</li></ol><p>p_fypd_ds和p­_fyxx_ds中入参都增加字段gytj，dcyl，yypc。</p><ol><li>修改接口门诊预结算, 门诊结算，修改入参字段ptmzskbz的说明。</li><li>修改接口医保智能审核，修改入参xmlx为xmfl。</li></ol><p></p></td></tr><tr><td><p>2016/9/5</p></td><td><p>1.1.5</p></td><td><p>马秀华</p></td><td><ol><li>增加接口:门诊对账查询query_mzdz</li></ol></td></tr><tr><td><p>2016/9/6</p></td><td><p>1.1.6</p></td><td><p>马秀华</p></td><td><ol><li>智能医保审核 入参p_fees_ds中增加参数说明</li></ol></td></tr><tr><td><p>2016/9/9</p></td><td><p>1.1.7</p></td><td><p>马秀华</p></td><td><ol><li>门诊结算、门诊预结算 增加返回值sxzfje、ljmznrtc（济南专用）、tcfwnfd（济南专用）。</li></ol></td></tr><tr><td><p>2016/9/10</p></td><td><p>1.1.8</p></td><td><p>马秀华</p></td><td><ol><li>增加接口：个账消费明细查询</li><li>修改接口门诊结算：增加返回值mzjslx</li></ol></td></tr><tr><td><p>2016/9/22</p></td><td><p>1.1.9</p></td><td><p>马秀华</p></td><td><ol><li>修改接口：上传住院费用凭单中返回值fyid的参数说明；</li><li>修改接口：删除指定费用凭单中入参p_fyid的参数说明</li></ol></td></tr><tr><td><p>2016/9/26</p></td><td><p>1.2.0</p></td><td><p>裴儒渊</p></td><td><ol><li>修改2.1.4节中的数字格式描述：支持6位小数；</li><li>修改3.2.1节中的query_basic_info的出参cbdsbh、cbjgmc的描述。由省平台用改为省异地用</li><li>修改3.4.1节中门诊预结算settle_mz_pre和3.4.2节门诊结算settle_mz的出参：调整列的展示顺序，增加zje的构成说明</li><li>修改文档中p_kh的描述，统一为卡号</li><li>修改住院登记中的参数p_zyfs和p_yyltcdjh的位置</li><li>修改3.5.2.1住院费用上传入参p_date的描述：精确到天，支持到秒</li></ol></td></tr><tr><td><p>2016/10/08</p></td><td><p>1.2.1</p></td><td><p>何希真</p></td><td><ol><li>修改3.14节保存病历首页中的部分入参名称与中文名称不对应。</li></ol></td></tr><tr><td><p>2016/10/14</p></td><td><p>1.2.2</p></td><td><p>马秀华</p></td><td><ol><li>修改接口 出院 返回结果集中增加参数bcqfx、bcnrtcfw、bnynrtcfw、qttczf。</li></ol></td></tr><tr><td><p>2016/10/19</p></td><td><p>1.2.3</p></td><td><p>gy</p></td><td><ol><li>修改接口save_zydj，jbbm修改为必传项</li><li>附录中的治疗方式（ZLFS）改为his调服务获取</li></ol></td></tr><tr><td><p>2016/10/21</p></td><td><p>1.2.4</p></td><td><p>gy</p></td><td><ol><li>出院结算增加出参发票人员类别fprylb</li></ol></td></tr><tr><td><p>2016/11/15</p></td><td><p>1.2.5</p></td><td><p>马秀华</p></td><td><ol><li>住院登记增加生育工伤相关入参及参数说明：p_sydjh、p_syzh、p_ycq、p_hysj、p_yecssj、p_sytc、p_tegs、p_lxdh、p_sfwzzyb、p_gsfsrq。</li><li>结算增加入参：济南专用参数（p_sybfz、p_jhsysslb）、潍坊专用参数p_cyyxzd。</li><li>出院增加入参：济南专用参数（p_sybfz、p_jhsysslb）、潍坊专用参数（p_cyyxzd）、p_fmfs、。</li></ol></td></tr><tr><td><p>2016/11/25</p></td><td><p>1.2.6</p></td><td><p>gy</p></td><td><ol><li>读卡接口，修改出参sfxsptmztc，新增出参mzddsm、mzddbz</li><li>去掉附录，code由his调用数据字典接口获取，代码编号值见各接口入参说明。</li></ol></td></tr><tr><td><p>2016/11/29</p></td><td><p>1.2.7</p></td><td><p>马秀华</p></td><td><ol><li>修改入参中字段参数说明：yzzh、yzsxh、</li></ol></td></tr><tr><td><p>2016/12/06</p></td><td><p>1.2.8</p></td><td><p>何希真</p></td><td><p>1.将之前添加的急诊转住院的参数p_cxyy,p_fyly删除</p></td></tr><tr><td><p>2016/12/07</p></td><td><p>1.2.9</p></td><td><p>马秀华</p></td><td><ol><li>将查询医保核心端目录(职工普通门诊统筹专用)sxzfbl_ds中的返回参数yltclb，dyrylb删除。</li><li>门诊结算、住院结算增加返回参数：dbbzje大病补助金额（H3地区使用，目前仅潍坊使用）</li><li>读卡返回参数bcrylb修改参数说明。</li><li>修改住院登记、结算、出院中生育相关传入参数说明：表明xzbz = ‘E’时使用，用以区别淄博地区生育险种为“C”。</li></ol></td></tr><tr><td><p>2016/12/28</p></td><td><p>1.3.0</p></td><td><p>郭凯</p></td><td><p>修改参数为code的说明。</p></td></tr><tr><td><p>2017/2/16</p></td><td><p>1.3.1</p></td><td><p>gy</p></td><td><p>query_ gzxfmx增加入参sfzhm</p></td></tr><tr><td><p>2017/3/30</p></td><td><p>1.3.2</p></td><td><p>马秀华</p></td><td><p>query_ yyxm_info 增加返回参数mzjsxmbh，zyjsxmbh</p></td></tr><tr><td><p>2017/5/4</p></td><td><p>1.3.3</p></td><td><p>闫孟文</p></td><td><p>1.删除保存病历首页save_case</p><p>2.删除医嘱管理save_record</p><p>3. 3.16查询门诊就医信息 query_mzjyxx 仅供济南使用</p><p>4. 3.18个账消费明细查询query_ gzxfmx仅供济南使用</p></td></tr><tr><td><p>2017/6/15</p></td><td><p>1.3.4</p></td><td><p>gy</p></td><td><p>1.新增个账消费预结算和结算接口，目前仅有威海使用</p><p>2.新增个账扣款接口，目前仅威海使用</p></td></tr><tr><td><p>2017/8/7</p></td><td><p>1.3.5</p></td><td><p>孙传波</p></td><td><p>1.增加接口：新增修改或者注销医院科室add_dept</p></td></tr><tr><td><p>2017/8/9</p></td><td><p>1.3.6</p></td><td><p>马秀华</p></td><td><ol><li>修改门诊对账接口，增加威海返回参数。</li></ol><p>2、增加个账对账接口，威海使用。</p></td></tr><tr><td><p>2017/9/5</p></td><td><p>1.3.7</p></td><td><p>闫子武</p></td><td><ol><li>删除住院部分返回的report，门诊部分返回的report更名为门诊结算发票（阳煤专用）</li><li>读卡时，必须传入口令（阳煤专用）</li><li>读卡返回人员信息，增加返回参数保险编号ybbh（阳煤专用）</li><li>撤销门诊结算的时候，修改入参p_kl参数说明，阳煤必传</li><li>读卡增加返回字段：tjrylb和tlrylbmx，用来区分人群类别。（阳煤专用）</li><li>增加说明，读卡返回的kh字段为空。（阳煤专用）</li><li>修改门诊结算入参p_ptmzskbz说明：读卡时若由地纬DLL控制读卡器时并且需要进行刷卡校验密码时，此参数必须为“1”</li><li>查询结算信息（query_jsxx）：增加入参p_xzbz，出参xzbz</li></ol></td></tr><tr><td><p>2017/10/17</p></td><td><p>1.3.8</p></td><td><p>马秀华</p></td><td><ol><li>查询结算信息（query_jsxx）：增加出参jsbz(结算标志)</li></ol></td></tr><tr><td><p>2017/10/19</p></td><td><p>1.3.9</p></td><td><p>马秀华</p></td><td><ol><li>新增操作员接口（add_czy）:增加入参p_password</li></ol></td></tr><tr><td><p>2017/11/20</p></td><td><p>1.4.0</p></td><td><p>gy</p></td><td><p>1、修改出院结算接口，入参cyrq必填</p></td></tr><tr><td><p>2017/11/22</p></td><td><p>1.4.1</p></td><td><p>gy</p></td><td><ol><li>query_mzdz接口增加入参p_grbh, p_mzlb</li><li>结算、出院接口增加入参p_cyks</li><li>add_czy接口增加入参院区标识p_campusid</li><li>增加接口查询用户口令query_userkey</li><li>settle_mz增加入参p_passwd</li><li>query_mzdz接口增加入参p_jshid，出参ds中增加czybh</li><li>删除医保审核接口</li></ol></td></tr><tr><td><p>2017/12/10</p></td><td><p>1.4.2</p></td><td><p>闫孟文</p></td><td><ol><li>增加预撤销住院结算</li><li>增加预撤销门诊结算</li></ol></td></tr><tr><td><p>2017/12/16</p></td><td><p>1.4.3</p></td><td><p>gy</p></td><td><ol><li>query_mzdz增加返回字段grbh,sbjgbh, tczf, dezf, qttczf, ylbzje威海返回数据为统筹数据，包括住院和门诊</li><li>settle_mz_pre和settle_mz增加出参bnmzdbljbxje，bnptmzljbxje，仅支持hsu地区使用</li><li>settle_zy,settle_zy_pre,outhosp增加出参dbzyyfdje, dbzgrfdje, dbzybjsje，仅淄博使用</li><li>修改p_ptmzskbz参数说明</li></ol></td></tr><tr><td><p>2018/01/11</p></td><td><p>1.4.4</p></td><td><p>gy</p></td><td><ol><li>settle_mz_pre,settle_mz增加出参，二次报销金额，仅济南职工使用</li><li>query_basic_info新增多社保局出参（使用动态库无需处理）</li></ol></td></tr><tr><td><p>2018/01/17</p></td><td><p>1.4.5</p></td><td><p>gy</p></td><td><p>1、潍坊持卡就医新增参数</p><p>read_card新增出参 sbm,fwm</p><p>settle_mz_pre, settle_mz, save_zydj新增入参p_sbm,p_fwm</p><p>2、济南支持免费用药：</p><p>修改接口settle_mz_pre,settle_mz：新增入参p_mfyybz, 修改p_jbbm、p_fypd_ds的说明；</p><p>新增接口：query_mfyy_info</p><p>3、删除query_userkey接口</p></td></tr><tr><td><p>2018/01/30</p></td><td><p>1.4.6</p></td><td><p>gy</p></td><td><ol><li>无卡查询个人信息接口，新增出参mzddsm、mzddbz</li><li>修改read_card删除出参sbm、fwm</li><li>settle_zy_pre、settle_zy、outhosp增加入参sbm、fwm</li><li>增加接口查询手术信息 query_operation</li></ol></td></tr><tr><td><p>2018/03/02</p></td><td><p>1.4.7</p></td><td><p>闫孟文</p></td><td><p>出院结算outhosp接口增加入参：p_cyzd2 — p_cyzd9</p></td></tr><tr><td><p>2018/03/23</p></td><td><p>1.4.8</p></td><td><p>闫孟文</p></td><td><ol><li><a id="OLE_LINK35"></a>住院结算和出院增加返回值扶贫人员补充报销金额</li><li>增加打印pos小票的接口文档</li></ol></td></tr><tr><td><p>2018/03/28</p></td><td><p>1.4.9</p></td><td><p>闫孟文</p></td><td><ol><li>东营门诊结算、住院结算和出院增加返回值医疗机构减免和商业兜底</li><li><a id="OLE_LINK39"></a>query_mzdz增加出参：医疗统筹类别</li></ol></td></tr><tr><td><p>2018/04/09</p></td><td><p>1.5.0</p></td><td><p>闫孟文</p><p>gy</p></td><td><p>无卡查询人员信息和读卡查询人员信息增加返回值 ptmzjbs给his</p><p>修改费用ds中的单价、数量、总金额及字段说明</p></td></tr><tr><td><p>2018/05/11</p></td><td><p>1.5.1</p></td><td><p>王晨宇</p></td><td><p>打印出院单print_cyd接口增加入参：p_zylsh</p></td></tr><tr><td><p>2018/05/24</p></td><td><p>1.5.2</p></td><td><p>刘鹏</p></td><td><ol><li>新增门诊结算、门诊预结算接口入参：本次就医流水号；</li><li>出院预结算、出院结算、出院接口增加入参：出院诊断、次要诊断集合；新增出参：本次商保报销金额、本次报案流水号、保险公司编号、保险公司名称；</li><li>门诊对账查询接口mzdz_ds数据集中增加出参：本次商保报销金额、本次报案流水号、保险公司编号、保险公司名称；</li><li>新增检查结果上传接口；</li><li>新增发票信息上传接口。</li></ol></td></tr><tr><td><p>2018/05/28</p></td><td><p>1.5.3</p></td><td><p>gy</p></td><td><p>1、query_yyxm_info增加入参p_ypbz</p><p>2、济南：settle_zy_pre,settle_zy,outhosp增加出参wjbfje,mzbzje</p></td></tr><tr><td><p><a id="OLE_LINK49"></a>2018/7/3</p></td><td><p>1.5.4</p></td><td><p>马秀华</p></td><td><p>修改门诊结算、出院结算中关于p_kh的注释说明。</p><p>无卡结算：不传此参数；</p><p>有卡结算：若地纬DLL控制读卡器，持卡结算时，p_kh传“”，否则传正常卡号；若由his控制读卡器，p_kh传正常卡号。</p></td></tr><tr><td><p>2018/07/7</p></td><td><p>1.5.5</p></td><td><p>闫孟文</p></td><td><p>1.门诊结算、门诊预结算、住院结算、住院预结算、出院出参增加民政救助（mzbzje）、民政优抚（yfbzy）</p><p>2.住院结算、住院预结算、出院入参增加是否无第三方责任人意外伤害（p_ywshbz）</p></td></tr><tr><td><p>2018/07/10</p></td><td><p>1.5.6</p></td><td><p>夏汝宽</p></td><td><p>在个账消费、撤销个账结算、个账扣款中增加入参：psam卡号（p_psam）</p><p>将个账预结算出参入参替换为个账结算的出参入参</p><p>【聊城】新增住院登记接口入参：护士编码（hsbm）</p><p>住院结算增加出参：结算单（report）</p><p>门诊结算将门诊结算发票改名为结算单</p></td></tr><tr><td><p>2018/07/10</p></td><td><p>1.5.7</p></td><td><p>夏汝宽</p></td><td><p>新增或者更新医师信息增加入参：医职人员类别（yzrylb）</p><p>查询医院医师信息增加出参：医职人员类别（yzrylb）</p><p>住院登记增加入参：联系电话（lxdh）</p><p>读卡获取人员基本信息增加入参：普通门诊刷卡标志（ktmzskbz）；增加出参：单位名称（dwmc），单位性质（dwxz）</p><p>获取人员基本信息（无卡）增加出参：单位名称（dwmc），单位性质（dwxz）</p><p>新增接口：门诊大病用药范围查询（query_mzdbyyfw）</p></td></tr><tr><td><p>2018/9/14</p></td><td><p>1.5.8</p></td><td><p>赵学恒</p></td><td><p>在门诊结算中增加返参：乙类首先自付（bfzf）、累计统筹报销（ljbxje）</p><p>修改门诊结算中返参本年度累计普通门诊报销金额（bnptmzljbxje）的名称为“本年度累计普通门诊报销金额/门诊额度累计(济南)”</p></td></tr><tr><td><p>2018/9/25</p></td><td><p>1.5.9</p></td><td><p>gy、赵学恒</p></td><td><ol><li>读卡和无卡取人信息增加返回pkrkbz和jzrylb，无卡查询个人信息增加返回ye, pkrkbz和jzrylb</li><li>新增或者更新医院项目增加入参：适应症（p_syz）、批件产品名称（p_pjcpmc）、注册证号（p_zczh）、注册截止日期（p_zcjzrq）</li></ol></td></tr><tr><td><p>2018/10/25</p></td><td><p>1.6.0</p></td><td><p>鲁伟强</p></td><td><p>1、新增3.6.11 增量查询下载医院项目目录</p><p>2、新增3.6.12 增量查询下载医院项目目录（职工普通门诊统筹专用）</p><p>3、新增3.6.13 增量查询下载医保核心端目录</p><p>4、新增3.6.14 增量查询下载医保核心端目录(职工普通门诊统筹专用)</p></td></tr><tr><td><p>2018/11/1</p></td><td><p>1.6.1</p></td><td><p>邹宗军</p></td><td><p>1新增实账户地区MIS信息保存接口：save_misinfo</p></td></tr><tr><td><p>2018/11/6</p></td><td><p>1.6.2</p></td><td><p>鲁伟强</p></td><td><p>3.6.11 增量查询下载医院项目目录不再支持按照药品标志下载，防止下载目录不全</p></td></tr><tr><td><p>2018/11/14</p></td><td><p>1.6.3</p></td><td><p>马秀华</p></td><td><p>获取个人信息接口，xzbz改为必填，防止因为接受默认值导致获取sbjgbh错误。</p></td></tr><tr><td><p>2018/11/19</p></td><td><p>1.6.4</p></td><td><p>刘龙</p></td><td><p>1、读卡接口新增入参：p_ewm，新增出参：dzsbkh、qdh;</p><p>2、门诊结算接口新增入参：p_ewm,p_dzsbkh,p_qdh,p_qdzf, 新增出参：qdzfje；</p></td></tr><tr><td><p>2018/11/28</p></td><td><p>1.6.5</p></td><td><p>闫孟文</p></td><td><p>增加接口query_brfy查询病人结算信息</p></td></tr><tr><td><p>2018/12/3</p></td><td><p>1.6.6</p></td><td><p>郭凯</p></td><td><ol><li>修改结算接口，增加字段（绿色部分，只有调用卫宁审核需要传输绿色字段）</li><li>删除不用字段p_bcjylsh</li></ol></td></tr><tr><td><p>2018/12/12</p></td><td><p>1.6.7</p></td><td><p>张瑞</p></td><td><p>修改query_basic_info 、read_card接口，增加单位性质名称（dwxzmc）出参</p></td></tr><tr><td><p>2018/12/24</p></td><td><p>1.6.8</p></td><td><p>夏汝宽</p></td><td><p>read_card接口增加入参： p_xm,p_shbzhm,p_sbm,p_gfbb；增加返回参数：zhzybz，zhzysm。</p><p>新增接口read_card_qgyd。</p><p>settle_mz_pre、settle_mz接口去掉上一版本增加的p_dzsbkh,p_qdh入参；修改p_ewm，p_qdzf入参的描述；增加返回参数：累计自付金额（ljzfje），渠道标志（qdbz）。</p><p>更改save_zydj、settle_zy_pre、settle_zy、outhosp接口中传入参数p_sbm的参数说明</p></td></tr><tr><td><p>2019/1/4</p></td><td><p>1.6.9</p></td><td><p>刘龙</p></td><td><ol><li>读卡（read_card）接口新增p_psamkh入参；</li><li>新增根据电子社保卡二维码读取基本信息接口（read_ewm）;</li><li>新增3.30融合支付相关接口</li><li>新增3.31查询参保人就医信息（query_cbrjyxx）</li></ol></td></tr><tr><td><p>2019/2/14</p></td><td><p>1.7.0</p></td><td><p>张瑞</p></td><td><p>query_ml、query_ml_by_sxh接口增加zczh、pzwh返回参数</p></td></tr><tr><td><p>2019/03/11</p></td><td><p>1.7.1</p></td><td><p>夏汝宽</p></td><td><p>read_card接口新增返参：psam卡号（psamkh）</p><p>save_zydj、settle_mz_pre、settle_mz接口新增入参：psam卡号（psamkh）</p></td></tr><tr><td><p>2019/3/21</p></td><td><p>1.7.2</p></td><td><p>郭凯</p></td><td><p>settle_mz绿色部分增加说明</p></td></tr><tr><td><p>2019/3/29</p></td><td><p>1.7.3</p></td><td><p>夏汝宽</p></td><td><p>settle_zy_pre,settle_zy,outhosp接口删除无用参数分娩方式（p_fmfs），增加威海专用入参婴儿出生时间（p_yecssj）和流产或引产时间（p_ycq）；</p><p>query_jsxx和query_mzdz接口增加入参院区编号（p_campusid）</p><p></p></td></tr><tr><td><p>2019/4/22</p></td><td><p>1.7.4</p></td><td><p>刘龙</p><p>gy</p></td><td><ol><li>删除3.2.5节</li><li>修改3.2.2返回字段kh相关说明，删除出参dzsbkh、qdh字段</li><li>修改3.3.1中p_kh相关说明</li><li>修改3.3.1、3.4.3.1、3.4.3.2、3.4.3.3入参，新增p_ewm字段</li><li>修改门诊预结算settle_mz_pre接口，新增入参:p_ycq,p_hysj,p_jhzh淄博、威海生育使用；修改门诊结算settle_mz接口：新增入参p_jhzh,修改入参p_ycq,p_hysj的说明</li><li>修改住院登记save_zydj接口：新增入参p_sybfz,p_jhzh , p_jsschm修改入参p_ycq的说明</li><li>修改出院outhosp接口：新增入参p_ycq,p_hysj, p_sytc, p_tegs,p_jhzh,p_jsschm修改入参p_sybfz的说明；</li><li>修改住院预结算settle_zy_pre、结算settle_zy接口错误入参p_yrq为p_ycq并修改其说明，新增入参p_jhzh,p_jsschm,p_tegs, p_sytc, p_hysj；</li></ol></td></tr><tr><td><p>2019/5/07</p></td><td><p>1.7.5</p></td><td><p>gy</p></td><td><ol><li>修改门诊预结算、结算settle_mz_pre、 settle_mz，住院预结算、结算settle_zy_pre、settle_zy，出院outhosp接口：修改p_ycq的说明。</li><li>修改住院预结算、结算settle_zy_pre、settle_zy接口：新增入参p_jhzh,p_jsschm,p_tegs, p_sytc, p_hysj</li></ol></td></tr><tr><td><p>2019/5/24</p></td><td><p>1.7.6</p></td><td><p>gy</p></td><td><ol><li>修改读卡接口read_card，新增出参：行政区划（xzqh）,社保卡异地标志（sbkydbz）</li><li>新增山东省内异地卡消费接口settle_gzyd，退费接口destroy_gzyd</li><li>修改住院预结算、结算、出院settle_zy_pre、settle_zy、outhosp接口修改p_jsschm的说明</li></ol></td></tr><tr><td><p>2019/06/14</p></td><td><p>1.7.7</p></td><td><p>夏汝宽、赵学恒</p></td><td><ol><li>修改住院结算接口，新增出餐参：补充支付（bczf）,修改出参大病补助金额（dbbzje）的说明，修改入参是否需要结算单（p_needjsd）的说明</li><li>修改add_czy接口，新增入参：操作员类型（p_czylx）</li><li>修改settle_mz_pre接口，删除入参：本次就医流水号（p_bcjylsh）</li><li>修改settle_zy_pre、settle_zy和outhosp接口，删除入参：医院出院诊断（p_yycyzd）和次要诊断集合（p_cyzd_ds）；删除出参：本次商保报销金额（bcsbbx）、本次报案流水号（bcbalsh）、保险公司编号（bxgsbh）和保险公司名称（bxgsmc）</li><li>修改query_mzdz接口，删除出参：本次商保报销金额（mzdz_ds.bcsbbx）、本次报案流水号（mzdz_ds.bcbalsh）、保险公司编号（mzdz_ds.bxgsbh）和保险公司名称（mzdz_ds.bxgsmc）</li></ol></td></tr><tr><td><p>2019/08/26</p></td><td><p>1.7.9</p></td><td><p>闫孟文</p><p>gy</p></td><td><p>1、query_ml,query_ml_by_sxh,query_yyxm_info,query_yyxm_info_by_sxh,query_yyxm_info_mz,query_yyxm_info_mz_by_sxh接口增加返回值<a id="OLE_LINK54"></a>bzgg(包装规格)</p><p>2、滨州：出院预结算、结算、出院接口新增入参p_lclj,p_ssbm,p_sszlsm,p_dscbz</p><p>3、滨州：新增接口3.9.15定额疾病目录下载</p></td></tr><tr><td><p>2019/09/06</p></td><td><p>1.8.0</p></td><td><p>gy</p><p>刘煦阳</p></td><td><ol><li>威海：门诊预结算、结算增加返回苯丙酮尿酸症医保报销金额、苯丙酮尿酸症医院报销金额</li><li>滨州：新增接口，出院补录出院诊断和临床路径</li></ol></td></tr><tr><td><p>2019/10/17</p></td><td><p>1.8.1</p></td><td><p>刘煦阳</p></td><td><p>修改add_yyxm_info_all接口，增加入参p_cdm</p></td></tr><tr><td><p>2019/11/11</p></td><td><p>1.8.2</p></td><td><p>刘煦阳</p></td><td><ol><li>修改read_card接口，增加反参kyhzh</li><li>save_zy_script接口入参p_fyxx_ds增加外配标志，settle_mz和settle_mz_pre接口入参p_fypd_ds增加入参wpbz</li></ol></td></tr><tr><td><p>2019/12/17</p></td><td><p>1.8.3</p></td><td><p>刘煦阳</p><p>gy</p></td><td><p>聊城：新增慢病病人查询就医记录query_bryyqk接口</p><p>聊城：新增电子医保凭证账户消费（实账户地区）<a id="OLE_LINK92"></a>settle_ewmgz接口</p><p>济南：读卡和无卡取人信息返回工伤检查信息</p><p>济南：修改住院登记生育相关参数说明</p><p>出院结算新增生育入参<a id="OLE_LINK94"></a>p_poxm，p_posfzhm</p></td></tr><tr><td><p>2020/1/19</p></td><td><p>1.8.4</p></td><td><p>刘龙</p></td><td><p>读卡、门诊、住院相关接口增加医保电子凭证相关描述</p></td></tr><tr><td><p><a id="OLE_LINK46"></a>2020/3/9</p></td><td><p>1.8.5</p></td><td><p>gy</p></td><td><p>删除电子医保凭证账户消费settle_ewmgz接口</p></td></tr><tr><td><p>2020/3/12</p></td><td><p>1.8.6</p></td><td><p>闫孟文</p></td><td><p>东营：修改read_card接口、query_basic_info接口，增加返参lbjbbm</p></td></tr><tr><td><p>2020/03/23</p></td><td><p>1.8.7</p></td><td><p>赵学恒</p></td><td><p>修改destroy_mz_pre接口、destroy_mz 接口，增加入参p_ewm</p></td></tr><tr><td><p>2020/04/08</p></td><td><p>1.8.8</p></td><td><p>gy</p></td><td><p>1、新增实账户地区电子医保凭证账户消费和撤销settle_ewmgz，destory_ewmgz接口</p><p>2、读卡接口read_card增加返回ectoken</p></td></tr><tr><td><p>2020/04/30</p></td><td><p>1.8.9</p></td><td><p>王曹</p></td><td><p>枣庄：1、住院预结算接口sette_zy_pre增加出参：pkrkyljgjmbz</p><p>2、住院结算settle_zy、出院接口outhosp分别增加入参<strong>p_pkrkyljgjmje</strong>和出参<strong>pkrkyljgjmje</strong></p><p>（his根据预结算返回的pkrkyljgjmbz为1，结算时传入<strong>p_pkrkyljgjmje，</strong>以医院传入的为准计算贫困人口医疗机构减免金额）</p></td></tr><tr><td><p>2020/05/20</p></td><td><p>1.9.0</p></td><td><p>赵学恒</p></td><td><p>1、住院登记save_zydj增加入参p_zszh、p_jbr、p_posfzhm、p_poxm，修改入参p_ycq、p_jhzh、p_sytc的参数说明，删除入参p_jsschm</p><p>2、住院预结算settle_zy_pre、住院结算settle_zy、出院outhosp增加入参p_sffhsyjt，修改入参p_sytc、p_ycq、p_tegs、p_jsschm的参数说明，删除入参p_jhzh</p></td></tr><tr><td><p>2020/05/25</p></td><td><p>1.9.1</p></td><td><p>闫孟文</p></td><td><ol><li>威海工伤联网：住院登记save_zydj入参<a id="OLE_LINK57"></a></li></ol><p><strong>p_bcxm_ds增加：</strong>ssjg（受伤经过），sscd（受伤程度）, yhzh（银行账号）, dfjgid（开户行）,lxrdh（联系人电话）。</p><ol><li>增加获取代发机构信息接口<a id="OLE_LINK76"></a>get_dfjgxx。</li></ol></td></tr><tr><td><p>2020/06/02</p></td><td><p>1.9.2</p></td><td><p>gy</p></td><td><p>1、威海：新增接口3.6.5电子医保凭证账户消费</p></td></tr><tr><td><p>2020/06/15</p></td><td><p>1.9.3</p></td><td><p>王曹</p></td><td><p>枣庄住院结算：1、住院预结算（settle_zy_pre）时增加返回brfdje_s(个人负担金额)为多段结算的集合，类型为String逗号分隔，</p><p>2、修改住院结算（settle_zy）和出院接口（outhosp）的pkrkyljgjmje(贫困人员减免金额)为多段结算的集合，类型为String逗号分隔</p></td></tr><tr><td><p>2020/07/03</p></td><td><p>1.9.4</p></td><td><p>闫孟文</p></td><td><ol><li>济南：住院预结算（settle_zy_pre）、住院结算（settle_zy）、出院（outhosp）增加返参<a id="OLE_LINK120"></a>pkryzjzje（贫困人口再救助）、bczf（职工补充支付）。</li><li>泰安：生育住院登记增加必传入参：<a id="OLE_LINK107"></a>p_syzh</li><li><a id="OLE_LINK101"></a>泰安:<a id="OLE_LINK141"></a>住院预结算（settle_zy_pre）、住院结算（settle_zy）、 出院（outhosp）增加生育入参：<a id="OLE_LINK114"></a><a id="OLE_LINK136"></a><a id="OLE_LINK138"></a><a id="OLE_LINK135"></a>p_<a id="OLE_LINK137"></a>sybfz、 <a id="OLE_LINK125"></a>p_tegs<a id="OLE_LINK123"></a>（<a id="OLE_LINK121"></a>出院必传）、<a id="OLE_LINK127"></a> <a id="OLE_LINK132"></a>p_sytc（出院必传）、<a id="OLE_LINK112"></a><a id="OLE_LINK103"></a>p_<a id="OLE_LINK133"></a>jsschm（出院必传）、<a id="OLE_LINK128"></a>p_<a id="OLE_LINK134"></a>yxzmh(<a id="OLE_LINK129"></a>出生医学证明号)、<a id="OLE_LINK130"></a>p_fmfs(<a id="OLE_LINK131"></a>分娩方式)（出院必传）。<a id="OLE_LINK111"></a></li><li>增加查询科室信息接口query_hosp_dept。</li></ol></td></tr><tr><td><p>2020/07/13</p></td><td><p>1.9.5</p></td><td><p>闫孟文</p></td><td><p>1、潍坊：住院预结算（settle_zy_pre）、住院结算（settle_zy）、 出院（outhosp）增加出参：zdjbjzbxje（重大疾病救助金额）、zdjbzjzbxje（重大疾病再救助额）、bchjjze（ 本次合计救助额）。</p></td></tr><tr><td><p>2020/07/24</p></td><td><p>1.9.6</p></td><td><p>张瑞</p></td><td><p>滨州：save_zydj增加入参p_rjbfbz(日间病房标志),p_zlfhlrjbfbz(肿瘤放化疗日间病房标志)</p></td></tr><tr><td><p>2020/08/03</p></td><td><p>1.9.7</p></td><td><p>赵学恒</p></td><td><p>1、东营：settle_zy_pre、settle_zy、outhosp接口增加返回参数jsbjzzf（精神病救助保险报销额）</p></td></tr><tr><td><p>2020/08/08</p></td><td><p>1.9.8</p></td><td><p>闫孟文</p></td><td><p>潍坊：住院预结算（settle_zy_pre）、住院结算（settle_zy）、 出院（outhosp）增加出参：cqjcf（生育产前检查费）。</p></td></tr><tr><td><p>2020/08/11</p></td><td><p>1.9.9</p></td><td><p>闫孟文</p></td><td><p>淄博：查询慢病病人就医记录接口（query_bryyqk）增加返回 ejjbmc (疾病细类名称)。</p></td></tr><tr><td><p>2020/08/18</p></td><td><p>2.0.0</p></td><td><p>赵学恒</p></td><td><p>潍坊：住院预结算（settle_zy_pre）、住院结算（settle_zy）和出院（outhosp）接口，增加返回参数暂缓支付（zhzf）</p></td></tr><tr><td><p>2020/08/20</p></td><td><p>2.0.1</p></td><td><p>王庆源</p></td><td><p>滨州： 住院预结算（settle_zy_pre）、住院结算（settle_zy）和出院（outhosp）接口，增加返回参数医疗机构减免（fpryyljgjmje）重特大疾病救助（ztdjbzf）再救助（pkryzjzje）政府托底救助（zftd）重度残疾人托底救助（cjrtd）老党员托底救助（ldytdjzbxje）</p><p></p></td></tr><tr><td><p>2020/08/26</p></td><td><p>2.0.2</p></td><td><p>闫孟文</p></td><td><p>get_zfbl接口增加返回rqlb（人群类别）</p></td></tr><tr><td><p>2020/09/29</p></td><td><p>2.0.3</p></td><td><p>张瑞</p></td><td><p>聊城：settle_mz_pre，settle_mz增加pkryzjzje，ldybxje</p><p>settle_zy_pre,settle_zy增加ldybxje</p></td></tr><tr><td><p>2020/10/12</p></td><td><p>2.0.4</p></td><td><p>赵学恒</p></td><td><p>淄博：settle_mz_pre，settle_mz传入参数p_fypd_ds（费用凭单信息）修改yyts（用药天数）备注</p></td></tr><tr><td><p>2020/10/12</p></td><td><p>2.0.5</p></td><td><p>张瑞</p></td><td><p>滨州：save_zydj增加p_rjsxbz（日间手术标志）入参</p></td></tr><tr><td><p>2020/12/09</p></td><td><p>2.0.6</p></td><td><p>赵学恒</p></td><td><p>济宁：增加接口3.7.17增量查询下载医保疾病目录（query_si_sick_by_sxh）</p></td></tr><tr><td><p>2021/02/07</p></td><td><p>2.0.7</p></td><td><p>赵学恒</p></td><td><p>1、聊城：修改接口3.3.1门诊预结算（settle_mz_rpe）和3.3.2门诊结算（settle_mz），增加返回参数重复取药提示（cfqyts）【聊城：慢性病结算30天内重复取药时返回的提示】</p><p>2、济宁：修改接口3.3.1门诊预结算（settle_mz_rpe）和3.3.2门诊结算（settle_mz），增加传入参数工伤发生日期（p_gsfsrq）【济宁：工伤住院登记时使用，非必传】</p></td></tr><tr><td><p>2021/03/03</p></td><td><p>2.0.8</p></td><td><p>gy</p></td><td><ol><li>跨省异地门诊业务中：跨省异地电子凭证扫码调用read_card接口，sbjgbh传37000000，修改read_card接口的入参p_shbzhm,p_xm,p_sbm,p_gfbb的参数说明；读卡增加返回sbm</li><li>settle_mz_pre,settle_mz修改入参p_sbm的说明（传read_card返回的sbm）</li><li>settle_zy接口新增入参p_sbm</li></ol></td></tr><tr><td><p>2021/04/19</p></td><td><p>2.0.9</p></td><td><p>赵学恒</p></td><td><p>1、修改settle_zy_pre、settle_zy、outhosp接口，增加传入参数p_ylzcbs</p><p>2、增加query_ylzcbs接口</p></td></tr><tr><td><p>2021/04/21</p></td><td><p>2.1.0</p></td><td><p>赵学恒</p></td><td><p>1、修改settle_zy_pre、settle_zy、outhosp接口，增加传入参数p_syhbz(生育合并症，济南生育专用，非必传)</p></td></tr><tr><td><p>2021/04/25</p></td><td><p>2.1.1</p></td><td><p>赵学恒</p></td><td><p>1、修改settle_mz_pre、settle_mz接口，增加传入参数p_ylzcbs</p></td></tr><tr><td><p>2021/06/22</p></td><td><p>2.1.2</p></td><td><p>王曹</p></td><td><p>新增根据电子医保凭证二维码读取基本信息接口（read_ewm）</p></td></tr><tr><td><p>2021/06/28</p></td><td><p>2.1.3</p></td><td><p>王曹</p></td><td><p>枣庄住院登记接口（save_zydj）新增入参字段分娩方式（p_fmfs）</p></td></tr><tr><td><p>2021/07/08</p></td><td><p>2.1.4</p></td><td><p>王曹</p></td><td><p>门诊结算，预结算增加入参字段医疗政策标识（<strong>p_ylzcbs</strong>）</p></td></tr><tr><td><p>2021/07/21</p></td><td><p>2.1.5</p></td><td><p>赵学恒</p></td><td><p>个账消费（山东省内异地），修改接口说明</p></td></tr><tr><td><p>2021/08/27</p></td><td><p>2.1.6</p></td><td><p>董鹏</p></td><td><p>查询医院项目目录，增加入参字段医院项目编码（p_yyxmbm）</p></td></tr><tr><td><p>2021/10/18</p></td><td><p>2.1.7</p></td><td><p>王庆源</p></td><td><p>枣庄住院结算接口(settle_zy_pre 、settle_zy、outhosp)新增入参出院结算方式(p_cyjsfs),【枣庄住院结算时使用，必传，默认值为普通住院(ptzy)】</p></td></tr><tr><td><p>2022/01/17</p></td><td><p>2.1.8</p></td><td><p>苏钉</p></td><td><p>门诊预结算和门诊结算接口新增返参医疗统筹类别明细yltclbmx</p></td></tr><tr><td><p>2022/01/27</p></td><td><p>2.1.9</p></td><td><p>gy</p></td><td><ol><li>跨省异地门诊大病业务，settle_mz_pre,settle_mz新增入参主要诊断p_zyzd</li><li>read_card_qgyd增加入参p_yltclb；</li><li>read_card修改p_shbzhm说明，省内异地读卡也需传入</li><li>增量下载医院目录、核心目录，query_ml_by_sxh，query_yyxm_info_by_sxh接口增加返参xj_ds；</li></ol></td></tr><tr><td><p>2022/3/3</p></td><td><p>2.2.0</p></td><td><p>gy</p></td><td><ol><li>门诊：结算、预结算，住院：预结算、结算、出院接口增加返参hmbbxjemln（惠民保报销金额目录内）、hmbbxjemlw（惠民保报销金额目录外）、hmbbxje（惠民保报销金额）</li></ol></td></tr><tr><td><p>2022/5/30</p></td><td><p>2.2.1</p></td><td><p>zxh3</p></td><td><p>1、修改query_ml、query_ml_by_sxh接口，返回参数sxzfbl_ds增加返回xzbz</p><p>2、修改query_yyxm_info、query_yyxm_info_by_sxh接口，返回参数yyxm_ds增加返回yltclb、dj</p></td></tr><tr><td><p>2022/7/19</p></td><td><p>2.2.2</p></td><td><p>苏钉</p></td><td><p>读卡接口（read_card），门诊预结算接口（settle_mz_pre），门诊结算接口（settle_mz），撤销门诊结算接口（destroy_mz），住院登记接口（save_zydj），住院预结算接口（settle_zy_pre），住院结算接口（settle_zy），出院结算接口（outhosp）接口修改p_ewm的说明，使用刷脸设备且his控制设备时p_ewm的值需要传入身份信息检索码，地纬控制刷脸设备时，p_ewm的值传刷脸付标志”slf”。</p></td></tr><tr><td><p>2022/07/25</p></td><td><p>2.2.3</p></td><td><p>苏钉</p></td><td><p>生育住院预结算接口（settle_zy_pre），生育住院结算接口（settle_zy）新增入参是否报销产前检查费（p_cqjcf）</p></td></tr><tr><td><p>2022/08/09</p></td><td><p>2.2.4</p></td><td><p>gy</p></td><td><p>门诊预结算（settle_mz_pre），门诊结算（settle_mz），住院登记（save_zydj），住院预结算（settle_zy_pre），住院结算（settle_zy），出院（outhosp）接口新增入参收费认证方式（p_sfrzfs）。</p></td></tr><tr><td><p>2022/08/16</p></td><td><p>2.2.5</p></td><td><p>苏钉</p></td><td><p>读卡获取人员基本信息（read_card）和根据电子医保凭证获取个人身份证号和姓名（read_ewm）接口新增入参电子凭证业务码（<strong>p_dzpzywm</strong>）</p></td></tr><tr><td><p>2022/08/22</p></td><td><p>2.2.6</p></td><td><p>xym</p></td><td><p>settle_zy接口增加入参p_zyks</p></td></tr><tr><td><p>2022/9/1</p></td><td><p>2.2.7</p></td><td><p>苏钉</p></td><td><p>住院预结算，住院结算，出院接口新增返参跨省异地医疗补助金额（ksydylbzje）</p></td></tr><tr><td><p>2022/10/17</p></td><td><p>2.2.8</p></td><td><p>董鹏</p></td><td><p>住院登记(save_zydj)接口新增入参外伤标志(p_wsbz)、涉及第三方标志(p_sjdsfbz)、住院类型(p_zylx);门诊预结算(settle_mz_pre)、门诊结算(settle_mz)接口增加入参门诊急诊转诊标志(p_mzjzzzbz)</p><p>异地住院登记或门诊结算时根据实际情况传值。</p><p>读卡获取人员信息接口(read_card）增加反参参保地市编号（cbdsbh），参保机构名称(cbjgmc)。</p></td></tr><tr><td><p>2022/11/21</p></td><td><p>2.2.9</p></td><td><p>zxh3</p></td><td><p>读卡获取人员基本信息（全国异地）（read_card_qgyd）接口新增险种标志（p_xzbz）</p></td></tr><tr><td><p>2022/11/23</p></td><td><p>2.3.0</p></td><td><p>苏钉</p></td><td><p>住院登记接口（save_zydj）入参医疗统筹类别明细（p_yltclbmx）新增code说明 安宁疗护（990801）（德州用）</p></td></tr><tr><td><p>2022/12/14</p></td><td><p>2.3.1</p></td><td><p>苏钉</p></td><td><p>异地门诊预结算(settle_mz_pre)、门诊结算(settle_mz)接口增加入参外伤标志(p_wsbz)、涉及第三方标志(p_sjdsfbz)，异地门诊结算时根据实际情况传值。</p></td></tr><tr><td><p>2022/12/17</p></td><td><p>2.3.2</p></td><td><p>苏钉</p></td><td><p>查询医保核心端目录（query_ml），增量查询下载医保核心端目录（query_ml_by_sxh）接口返参医疗项目（ylxm_ds）数据集中增加返参上市备案号（lstd_fil_no）</p></td></tr><tr><td><p>2023/01/07</p></td><td><p>2.3.3</p></td><td><p>gy</p></td><td><p>1、门诊预结算（settle_mz_pre），门诊结算（settle_mz）新增入参医疗统筹类别明细（p_yltclbmx），新冠门诊报销时传入1102</p></td></tr><tr><td><p>2023/2/21</p></td><td><p>2.3.4</p></td><td><p>gy</p></td><td><p>1、门诊预结算（settle_mz_pre），门诊结算（settle_mz）接口新增入参多诊断信息（p_jbbm_ds）。</p></td></tr><tr><td><p>2023/3/6</p></td><td><p>2.3.5</p></td><td><p>徐玥明</p></td><td><p>德州 住院预结算(settle_zy_pre)、住院结算(settle_zy)、出院接口(outhosp)增加入参意外伤害医保负担比例（p_ywshybfdbl），修改出院结算方式（p_cyjsfs）说明</p></td></tr><tr><td><p>2023/4/26</p></td><td><p>2.3.6</p></td><td><p>徐玥明</p></td><td><p>枣庄 住院预结算(settle_zy_pre)、住院结算(settle_zy)、出院接口(outhosp)修改出院结算方式（p_cyjsfs）说明，增加一项中医优势病种</p></td></tr><tr><td><p>2023/5/25</p></td><td><p>2.3.7</p></td><td><p>徐玥明</p></td><td><p>聊城住院预结算(settle_zy_pre)、住院结算(settle_zy)、出院接口(outhosp)新增入参生育检查费p_syjcf</p></td></tr><tr><td><p>2023/5/27</p></td><td><p>2.3.8</p></td><td><p>gy</p></td><td><p>新增3.31,3.32移动支付2.0门诊结算/撤销推送接口</p></td></tr><tr><td><p>2023/6/29</p></td><td><p>2.3.9</p></td><td><p>徐玥明</p></td><td><ol><li>聊城住院登记接口(save_zydj)修改医疗统筹类别明细p_yltclbmx的说明</li><li>读卡获取人员基本信息（read_card）、根据电子医保凭证获取个人身份证号和姓名（read_ewm）的电子医保凭证业务码p_dzpzywm增加306：诊间核验身份</li></ol></td></tr><tr><td><p>2023/9/25</p></td><td><p>2.3.10</p></td><td><p>lfl</p></td><td><p>住院登记(save_zydj)接口新增入参住院情况登记(p_zyqkdj)</p></td></tr><tr><td><p>2023/10/21</p></td><td><p>2.3.11</p></td><td><p>lfl</p></td><td><p>个账消费(settle_ewmgz)接口，增加返参平台订单号(trxserno)和银联商户订单号(outtradeno)；</p></td></tr><tr><td><p>2023/12/21</p></td><td><p>2.3.12</p></td><td><p>kzh</p></td><td><p>无卡获取人员基本信息(query_basic_info)接口，读卡获取人员基本信息(read_card)增加返参门慢的二级代码(mzmxm_ejjbbm)和门慢的二级名称(mzmxm_ejjbmc)</p></td></tr><tr><td><p>2024/01/12</p></td><td><p>2.3.13</p></td><td><p>gy</p></td><td><p>1、修改读卡获取人员基本信息（read_card）接口新增入参业务关联身份证号码（p_ywglsfzhm）</p></td></tr><tr><td><p>2024/03/12</p></td><td><p>2.3.14</p></td><td><p>kzh</p></td><td><p>德州门诊预结算（settle_mz_pre），门诊结算（settle_mz）接口新增入参门诊结算方式（p_mzjsfs）、意外伤害医保负担比例（p_ywshybfdbl）</p></td></tr><tr><td><p>2024/03/19</p></td><td><p>2.3.15</p></td><td><p>gy</p></td><td><ol><li>删除电子医保凭证账户消费（实账户地区）接口</li></ol><p>说明：实账户地区（除济南，淄博，烟台，胜利油田外地区）：<strong>本地就医</strong>时，电子医保凭证<strong>本人</strong>个账消费在门诊结算或出院结算接口中完成，预结算返回最大可消费值grzhzf，结算时传入p_grzhzf大于0且小于等于预结算返回值即可消费。纯个账消费使用门诊结算接口，p_ptmzskbz传1。</p><p>本地就医实体卡本人个账消费模式不变。</p><ol><li>作废《地纬定点省内异地刷卡接口》</li><li>新增共济账户消费接口。</li><li>门诊预结算（settle_mz_pre）、门诊结算（settle_mz）、住院预结算(settle_zy_pre)、住院结算(settle_zy)、出院接口(outhosp)增加入参消费账户标志（p_xfzhbz）</li><li>读卡获取人员基本信息（read_card）接口增加返回共济账户余额，共济人参保地</li></ol></td></tr><tr><td><p>2024/04/8</p></td><td><p>2.3.16</p></td><td><p>gy</p></td><td><ol><li>修改接口住院预结算、住院结算、出院，增加返回部分自负（bfzf）、全额自付（qezf）、</li><li>修改门诊预结算、门诊结算、住院预结算、住院结算、出院接口，新增返回省异地个人账户支付sydgrzhzf</li></ol></td></tr><tr><td><p>2024/07/22</p></td><td><p>2.3.17</p></td><td><p>gy</p></td><td><ol><li>修改门诊预结算（settle_mz_pre）接口，新增入参科室编码（p_ksbm）；门诊结算（settle_mz）接口入参科室编码（p_ksbm）修改为必传项。。</li><li>修改门诊预结算（settle_mz_pre），门诊结算（settle_mz），住院费用上传(save_zy_script)接口，入参费用信息数据集新增入参开单医师编码（kdysbm）、执行医师编码（zxysbm）</li></ol></td></tr><tr><td><p>2024/09/11</p></td><td><p>2.3.18</p></td><td><p>万佳</p></td><td><p>修改门诊结算接口入参p_jbbm_ds（多诊断信息）、住院登记、出院接口入参增加p_jbbm_ds（多诊断信息）</p></td></tr><tr><td><p>2024/09/23</p></td><td><p>2.3.19</p></td><td><p>万佳</p></td><td><p>三类终端改造说明：</p><ol><li>修改门诊预结算（settle_mz_pre）、门诊结算（settle_mz）、撤销门诊预结算（destroy_mz_pre）、撤销门诊结算（destroy_mz）、修改住院预结算（settle_zy_pre）、住院结算（settle_zy）、撤销住院结算（destroy_zyjs）、出院接口（outhosp），新增入参刷脸授权码（p_authno）、撤销住院结算（destroy_zyjs）新增入参p_ewm</li><li>修改读卡（read_card）、门诊预结算（settle_mz_pre）、门诊结算（settle_mz）、预撤销门诊结算（destroy_mz_pre）、撤销门诊结算（destroy_mz）、住院登记（save_zydj）、住院预结算（settle_zy_pre）、住院结算（settle_zy）、出院（outhosp）接口中p_ewm的备注，修改读卡接口中p_shbzhm的备注，（如果调用settle_ewmgz接口入参中的p_ewm和p_authno参考其他接口）</li></ol></td></tr><tr><td><p><a id="_Toc13947"></a>2024/10/28</p></td><td><p>2.3.20</p></td><td><p>吴金瑞</p></td><td><p>门诊预结算（settle_mz_pre）、门诊结算（settle_mz）接口，费用信息数据集新增入参追溯码（zsm）、拆零标志(clbz)</p></td></tr><tr><td><p>2024/11/13</p></td><td><p>2.3.21</p></td><td><p>吴金瑞</p></td><td><ol><li>门诊结算（settle_mz）接口新增出参国标结算号id（gbjshid）</li><li>获取人员基本信息（无卡）（query_basic_info），读卡获取人员基本信息（read_card）接口新增出参人员id（ryid）</li><li>修改门诊预结算（settle_mz_pre），门诊结算（settle_mz），住院费用上传(save_zy_script)接口，入参费用信息数据集执行医师编码（zxysbm）改为非必传</li><li>修改新增修改或注销医院科室（add_dept）接口，增加入参p_kslb（科室类别）</li><li>修改门诊预结算（settle_mz_pre）和门诊结算（settle_mz）接口，入参p_ysbm参数名称改为主治医师编码，入参p_ksbm参数名称改为就诊科室编码；住院费用上传(save_zy_script)接口入参p_ysbm参数名称改为主治医师编码</li></ol></td></tr><tr><td><p>2025/01-07</p></td><td><p>2.3.22</p></td><td><p>万佳</p></td><td><p>1.修改住院预结算（settle_zy_pre）、住院结算（settle_zy）和出院接口（outhosp）入参中p_cyjsfs的说明，支持泰安传p_cyjsfs</p></td></tr><tr><td><p>2025/03-24</p></td><td><p>2.3.23</p></td><td><p>万佳</p></td><td><p>1.修改住院预结算（settle_zy_pre）、住院结算（settle_zy）和出院接口（outhosp）,新增返参gbjshid(国标结算号id)和yltcdjh(医疗统筹登记号)</p></td></tr></tbody></table></div>

目录

[版本记录： 2](#_Toc28597)

[目录 18](#_Toc13947)

[接口方案介绍 21](#_Toc25112)

[1.1 背景 21](#_Toc11365)

[1.2 业务框架 21](#_Toc16191)

[1.3 主要接口业务分类 21](#_Toc26074)

[第二章 调用规则 23](#_Toc30527)

[2.1 数据传递规则 23](#_Toc2914)

[2.1.1 参数编码格式 23](#_Toc11831)

[2.1.2 接口参数传递基本规则 24](#_Toc16233)

[2.1.3 时间日期格式 24](#_Toc6252)

[2.1.4 数字格式 24](#_Toc14989)

[第三章 详细说明 25](#_Toc5089)

[3.1 通用询问服务 25](#_Toc13102)

[3.2 个人信息获取 27](#_Toc21531)

[3.2.1 获取人员基本信息（无卡） 27](#_Toc12992)

[3.2.2 读卡获取人员基本信息 30](#_Toc22718)

[3.2.3 查询个人账户余额信息 33](#_Toc20079)

[3.2.4 读卡获取人员基本信息（全国异地） 34](#_Toc28173)

[3.2.5 根据电子医保凭证获取个人身份证号和姓名 35](#_Toc9743)

[3.3 门诊结算（含个人账户消费） 36](#_Toc11944)

[3.3.1门诊预结算 36](#_Toc25461)

[3.3.2门诊结算 43](#_Toc30710)

[3.3.3预撤销门诊结算 52](#_Toc1448)

[3.3.4撤销门诊结算 53](#_Toc3870)

[3.4 住院管理 54](#_Toc24207)

[3.4.1住院登记 54](#_Toc25207)

[3.4.2住院费用 59](#_Toc7933)

[3.4.3出院结算 62](#_Toc29660)

[3.4.4保存床位信息 79](#_Toc27737)

[3.5 共济账户消费 80](#_Toc6731)

[3.5.1 查询共济账户信息（动态库且地纬控卡） 81](#_Toc5158)

[3.5.2 查询共济账户信息（非动态库或非地纬控卡） 82](#_Toc32174)

[3.5.3 共济账户消费（动态库且地纬控卡） 83](#_Toc21821)

[3.5.4 共济账户消费（非动态库或非地纬控卡） 83](#_Toc1477)

[3.5.5 撤销共济账户消费 84](#_Toc13184)

[3.5.6 查询共济账户消费明细信息 85](#_Toc32135)

[3.6 目录管理 86](#_Toc12859)

[3.6.1 获取自付比例 86](#_Toc30330)

[3.6.2 查询医院项目目录 87](#_Toc29339)

[3.6.3 查询医保核心端目录 89](#_Toc5069)

[3.6.4 新增或者更新医院项目 91](#_Toc24294)

[3.6.5 新增或者更新医师信息 92](#_Toc25403)

[3.6.6 查询医保疾病目录 94](#_Toc4179)

[3.6.7 查询医保手术目录 95](#_Toc23032)

[3.6.8 查询医院医师信息 95](#_Toc19631)

[3.6.9 增量查询下载医院项目目录 97](#_Toc32326)

[3.6.10 增量查询下载医保核心端目录 99](#_Toc8764)

[3.6.11 定额疾病目录下载 102](#_Toc19035)

[3.6.12 查询医院医保科室信息 103](#_Toc7014)

[3.6.13 增量查询下载医保疾病目录 104](#_Toc31676)

[3.7 查询在院病人信息 105](#_Toc4420)

[3.8 查询在院病人费用明细 106](#_Toc23105)

[3.9 查询药品审批信息 107](#_Toc6910)

[3.10 查询住院审批信息 107](#_Toc7254)

[3.11 获取补充项目 108](#_Toc18835)

[3.12 数据字典 109](#_Toc31292)

[3.13 单据打印 109](#_Toc6801)

[3.13.1 打印入院通知单 109](#_Toc20564)

[3.13.2 打印结算单 110](#_Toc8630)

[3.13.3 打印出院单 110](#_Toc28858)

[3.13.4 打印门诊结算发票 111](#_Toc10403)

[3.13.5 打印住院自费明细单 111](#_Toc1719)

[3.13.6 打印POS小票 112](#_Toc15613)

[3.14 新增修改或者注销操作人员 112](#_Toc17113)

[3.15 个账消费明细查询 113](#_Toc10411)

[3.16 新增修改或者注销医院科室 114](#_Toc31920)

[3.17 加密机认证（无PSAM卡） 114](#_Toc27937)

[3.18 查询生育备案信息（威海） 116](#_Toc8061)

[3.19 查询门诊大病用药范围（聊城） 117](#_Toc25040)

[3.20 保存MIS交易信息（实账户地区） 118](#_Toc8536)

[3.21 查询病人费用明细 119](#_Toc22314)

[3.22 融合支付 120](#_Toc159)

[3.22.1 下单 120](#_Toc27376)

[3.22.2 查询订单 121](#_Toc24993)

[3.22.3 撤销订单 122](#_Toc24464)

[3.22.4 查询撤销订单 122](#_Toc30440)

[3.22.5 对账 123](#_Toc6118)

[3.23 查询参保人就医信息 124](#_Toc28813)

[3.24 出院补录出院诊断和临床路径 126](#_Toc8609)

[3.25 查询慢病病人就医记录接口 127](#_Toc21374)

[3.26 查询医疗政策标识 128](#_Toc19469)

[3.27 移动支付2.0门诊结算推送至诊间 128](#_Toc24478)

[3.28 移动支付2.0门诊结算撤销推送至诊间 129](#_Toc30242)

[第四章 开发建议 130](#_Toc9246)

[第五章 异常处理 131](#_Toc22016)

[1 异常处理方式 131](#_Toc7053)

[1.1 通用询问服务：ask_for_si 131](#_Toc19769)

接口方案介绍

## 背景

为了更加快速、高效的实现医院的联网报销和诊间结算业务，地纬公司搭建了医院医保结算平台，为了更好的实现定点医疗机构医保结算平台与医院自身信息管理系统(HIS)的对接，即HIS系统发生纳入统筹费用后转入结算平台，实现HIS系统向结算平台调用Web服务来完成结算业务，实现医院业务和医保业务同步进行，地纬公司研制设计了本接口系统。接口文档目前分DLL版和WEB版，HIS系统可根据自己的需要选择合适的调用方式。

## 业务框架

医院医保统一结算管理中心系统由统一结算路由平台、前置机组件服务平台组成，下图给出了系统逻辑架构。

由HIS系统向前置机组件服务平台发送请求，调用各种不同业务组件完成功能，通过统一结算路由平台向省、市不同机构不同险种的结算中心进行数据传输，完成中心结算，最终将结果返回HIS完成各类业务。

## 主要接口业务分类

根据不同的接口功能，业务分类主要分为以下两类：

- 查询类：可多次重复访问的接口功能，不会造成HIS与医保处数据不一致的服务。如：个人信息获取，智能医保审核，票据打印，数据下载等。
- 交易类：必须要保证HIS和医保数据一致性的服务（不能重复调用的服务）。如住院登记服务 ，同一次住院登记，多次调用中心服务可能会造成HIS与医保中心数据的不一致。如：个人账户消费，门诊统筹结算，住院登记，出院登记，住院结算等。

# 调用规则

1.  
2.  
3.  

## 数据传递规则

### 参数编码格式

接口中所有传递的参数采用json格式。

JSON(JavaScript Object Notation) 是一种轻量级的数据交换格式。易于人阅读和编写。同时也易于机器解析和生成。 它基于[JavaScript Programming Language](http://www.crockford.com/javascript), [Standard ECMA-262 3rd Edition - December 1999](http://www.ecma-international.org/publications/files/ecma-st/ECMA-262.pdf)的一个子集。JSON作为一种类似 XML的存储和交换文本信息的语法，比XML 更小、更快，更易解析。这些特性使JSON成为理想的数据交换语言。

JSON简单说就是javascript中的对象和数组，通过这两种结构可以表示各种复杂的结构；

对象：对象在js中表示为“{}”括起来的内容，数据结构为 {key：value,key：value,...}的键值对的结构。

数组：数组在js中是中括号“\[\]”括起来的内容，数据结构为 \["java","javascript","vb",...\]。

经过对象、数组2种结构就可以组合成复杂的数据结构了。

Json格式具体解析如下图：

### 接口参数传递基本规则

接口参数中传入参数名称前加“\*”的为必须传递参数，根据数据说明，必须传递参数也可传递空值“”。传出参数中参数名称前若有“\*”，表明强烈建议his保存此字段值。

### 时间日期格式

接口参数建议两种时间日期格式：

1.  时间日期格式A: yyyyMMddHHmmss
2.  时间日期格式B: yyyyMMdd

### 数字格式

1.  金额类型数值，均要求保留2位小数，支持6位小数。
2.  数量类型数值，均要求保留2位小数，支持6位小数。

例：数字为正数及0,如：45634.35；45634.20；45634.00；0.00

数字为负数，如：-45634.35；-45634.20；-45634.00

# 详细说明

1.  

## 通用询问服务

**接口名称：ask_for_si**

**接口作用：**查询医保结算平台服务调用是否成功

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_hisjyh | string | \*His交易号 | 原服务调用时的His交易号 |

**返回结果集：**

**当本次询问服务调用失败时，返回结果集为：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 不为0，表示本次询问服务调用失败。仍然未知医保结算平台服务调用是否成功，需要再次发起询问服务或者联系技术人员。 |

**当本次询问服务成功，而原服务调用失败时，返回结果集为：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 值为0，表示本次询问服务调用成功。而被询问业务是否执行成功需要参考success_flag的值。 |
| success_flag | string | \*被询问业务执行成功标志 | 值为0，表示本次询问的原服务执行失败。如询问住院结算服务，则表示医保结算平台结算失败。 |

**当本次询问服务成功，且原服务调用成功时，返回结果集为：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 值为0，表示本次询问服务调用成功。而被询问业务是否执行成功需要参考success_flag的值。 |
| success_flag | string | \*被询问业务执行成功标志 | 值为1，表示本次询问的原服务执行成功。 |
| …   | 数据集 | 返回数据 | 原服务所有返回结果集 |

**示例：住院登记询问服务**

**传入参数：**

|     |     |     |
| --- | --- | --- |
| 参数名称 | 参数值 | 说明  |
| p_hisjyh | 123456789 | 被询问的住院登记服务的His交易号 |

**返回结果集：**

**当住院登记询问服务调用失败时，返回结果集为：**

|     |     |     |
| --- | --- | --- |
| 参数名称 | 参数值 | 说明  |
| resultcode | \-6 | 本次询问操作执行失败。可以再次发起询问服务查询住院登记结算平台是否执行成功 |

**当住院登记询问服务调用成功，而被询问的原服务（save_zydj）调用失败时，返回结果集为：**

|     |     |     |
| --- | --- | --- |
| 参数名称 | 参数值 | 说明  |
| resultcode | 0   | 表示本次询问服务执行成功。而被询问业务（save_zydj）是否执行成功需要参考success_flag的值。 |
| success_flag | 0   | 表示本次询问的原服务（save_zydj）执行失败，医保结算平台不存在该条住院登记记录 |

**当住院登记询问服务调用成功，且被询问的原服务（save_zydj）调用成功时，返回结果集为：**

|     |     |     |
| --- | --- | --- |
| 参数名称 | 参数值 | 说明  |
| resultcode | 0   | 询问服务执行成功 |
| success_flag | 1   | 本次询问的原服务（save_zydj）执行成功。并返回原服务的返回结果集，如下字段（ryzd，bz，zylsh，qrbz）。 |
| ryzd | X26.902 | 入院诊断，此处保存入院疾病编码 |
| bz  | 退休职工&lt;测试03&gt;所在单位&lt;淄博测试yjcdd2&gt; | 医保审批意见 |
| zylsh | 16010802---001 | 住院流水号 |
| qrbz | 1   | 确认标志，0:尚未确认；1：联网确认；2：注销；3：不予确认；4：手工确认（不联网），其他具体值调用数据字典接口获取，代码编号：QRBZ |

## 个人信息获取

### 获取人员基本信息（无卡）

**接口名称：query_basic_info**

**接口作用：**在收到HIS的获取申请后，连接社保中心，获取个人基本信息。

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_grbh | string | \*个人编号 | 社会保障号码或者身份证号 |
| p_xzbz | string | \*险种标志 | 具体值调用数据字典接口获取，代码编号：XZBZ |
| p_xm | string | 姓名  | 该姓名必须和医保数据库中一致 |
| p_yltclb | string | 医疗统筹类别 | 0为仅获取人员基本信息，1为住院，4为门诊大病(特病)，6为普通门诊，不传时，默认值为0,其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_rq | string | 日期  | 住院日期或费用发生时间 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| xm  | string | 姓名  |     |
| xb  | string | 性别  | 1:男，2:女，9:不确定，可调用数据字典接口获取，代码编号：XB |
| grbh | string | 社会保障号码 | 社会保障号码或者身份证号 |
| zfbz | string | 支付标志（灰名单标志） | 0 :灰名单，1:白名单 |
| zfsm | string | 支付说明（灰名单原因） | 如果是白名单该值为空 |
| dwmc | string | 单位名称 | 获取持卡人所属单位名称 |
| ylrylb | string | 医疗人员类别 | 内容为汉字 |
| ydbz | string | 异地标志 | 1:异地，0:不是异地 |
| mzdbjbs | string | 疾病编码 | 结果格式是: 疾病病种的名称1 +’#m’+疾病病种编码1 + ‘/’ + 疾病病种的名称2 +’#m’+疾病病种编码2 + ‘/’ + ……,在此格式的基础上,请开发人员自行解析其中的编码和名称,并展示在功能界面上供操作人员选择。（每次结算只能选择一种疾病病种） |
| mzdbbz | string | 门诊大病备注 |     |
| sbjgbh | string | 社保局编码 |     |
| zhzybz | string | 有无15(医保参数控制)天内的住院记录 | 1:有，0 :无 |
| zhzysm | string | 15(医保参数控制)天内的住院记录说明 |     |
| zcyymc | string | 转出医院名称 | 如果zcyymc不为“”和“\*”,则表示本次住院是市内转院来的 |
| zccyrq | date | 转出医院出院日期 |     |
| rqlb | string | 人群类别 | A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| csrq | date | 出生日期 |     |
| yfdxbz | string | 优抚对象标志 | 济南专用。’1’为优抚对象 |
| yfdxlb | string | 优抚对象类别 | 济南专用。(汉字说明) |
| cbdsbh | string | 参保地市编号 | 省异地用 |
| cbjgmc | string | 参保机构名称 | 省异地用 |
| multisbj | string | 多社保局标志 | 0：标识病人只有一个参保身份，1：标识病人有多个参保身份 （使用mhs.dll动态库时，无需处理改参数） |
| multisbjds | 数据集 | 多社保局信息 | 当multisbj为1时，会返回此数据集，数据集中为病人的多个参保身份，his可根据病人需求选择其中一个身份进行业务办理（使用mhs.dll动态库时，无需处理改参数，否则需单独做页面展示多个社保局供操作员选择） |
| mzddbz | string | 门诊定点标志 | 值为1时，当前医院是该参保人的门诊统筹定点医院。（异地不返回） |
| mzddsm | string | 门诊定点说明 | 参保人的门诊统筹定点医院说明（异地不返回） |
| ptmzjbs | string | 普通门诊慢性病备案疾病 | 威海专用：医疗统筹类别为6时，返回普通门诊慢性病备案疾病。 |
| ye  | double | 余额  |     |
| jzrylb | string | 救助人员类别 | 具体值调用数据字典接口获取，代码编号：JZRYLB |
| pkrkbz | string | 贫困人口标志 | 1：贫困人口，0：非贫困人口 |
| dwxzmc | string | 单位性质名称 | 获取无卡人单位性质名称 |
| lbjbbm | string | 居民两病备案疾病编码 | 居民两病备案疾病编码 |
| gsjcxx | string | 工伤检查信息 | 济南使用 |
| mzmxm_ejjbbm | string | 门慢的二级代码 | 聊城使用 |
| mzmxm_ejjbmc | string | 门慢的二级名称 | 聊城使用 |
| ryid | string | 人员id |     |

multisbjds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| dwmc | string | 单位名称 |     |
| cbzt | string | 参保状态 |     |
| xzbz | string | 险种标志 |     |
| sbjgbh | string | 社保机构编号 |     |
| cbrylb | string | 参保人员类别 |     |
| sbjgmc | string | 社保机构名称 |     |

### 读卡获取人员基本信息

**接口名称：read_card**

**接口作用：**读取卡片信息，取得人员相关信息。

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_kh | string | 卡号  | 若由地纬DLL控制读卡器，p_kh可不传；若由his控制读卡器，p_kh为必传。 |
| p_xzbz | string | \*险种标志 | 具体值调用数据字典接口获取，代码编号：XZBZ |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’’ |
| p_yltclb | string | 医疗统筹类别 | 0为仅获取人员基本信息，1为住院，4为门诊大病(特病)，6为普通门诊，不传时，默认值为0，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_kl | string | 口令  | 口令由医保管理时，需传入口令；否则不传。（阳煤地区口令必传，如果口令没有修改过，则不会校验） |
| p_ptmzskbz | string | 普通门诊刷卡标志 | 纯个账消费传1，其他不传或传0 |
| p_shbzhm | string | 社会保障号码 | 由his控制读卡器时，跨省异地和省内异地读卡必传，跨省异地电子医保凭证时传空<br><br>三类终端刷脸或解码时，传入刷脸获取身份证号 |
| p_xm | string | 姓名  | 由his控制读卡器时，跨省异地读卡必传，跨省异地电子医保凭证时传空 |
| p_sbm | string | 卡识别码 | 由his控制读卡器时，跨省异地读卡必传，跨省异地电子医保凭证时传空 |
| p_gfbb | string | 卡规范版本 | 由his控制读卡器时，跨省异地读卡必传，跨省异地电子医保凭证时传空 |
| p_psamkh | string | psam卡号 | psam卡号，忻州省内异地必传 |
| p_dzpzywm | string | 电子医保凭证业务码 | 传电子医保凭证二维码时需传入。传入具体的code值<br><br>挂号：101<br><br>住院建档：102<br><br>入院登记：103<br><br>缴纳预缴金：104<br><br>问诊：201<br><br>预约检查：202<br><br>检查：203<br><br>治疗：204<br><br>结算：301<br><br>取药：302<br><br>取报告：303<br><br>打印票据和清单：304<br><br>病历材料复印：305<br><br>诊间核验身份：306 |
| p_ywglsfzhm | String | 业务关联身份证号码 | 使用电子医保凭证时，传入业务关联人的身份证号码。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| grbh | string | 个人编号 |     |
| kh  | string | 卡号  | 阳煤地区该字段为空,用二维码读卡时该字段为空 |
| rqlb | string | 人群类别 | A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| mzdbjbs | string | 疾病编码。 | 结果格式是: 疾病病种的名称1 +’#m’+疾病病种编码1 + ‘/’ + 疾病病种的名称2 +’#m’+疾病病种编码2 + ‘/’ + ……,在此格式的基础上,请开发人员自行解析其中的编码和名称,并展示在功能界面上供操作人员选择。（每次结算只能选择一种疾病病种） |
| sbjgbh | string | 社保机构编号 |     |
| sfzhm | string | 身份证号码 |     |
| xm  | string | 姓名  |     |
| zhye | number | 账户余额 |     |
| zfbz | string | 灰名单标志 | 0 代表灰名单,1 白名单 |
| zfsm | string | 灰名单原因 | 如果是白名单该值为空 |
| ylrylb | string | 医疗人员类别 | 汉字  |
| xb  | string | 性别  | 1:男,2:女,9:不确定，可调用数据字典接口获取，代码编号：XB |
| zcyymc | string | 转出医院名称。 | 如果zcyymc不为’’和’\*’,<br><br>则表示本次住院时候市内转院来的。 |
| ydbz | string | 异地标志 | 1:是,0: 否 |
| sfxsptmztc | string | 是否享受普通门诊统筹 | 如皋专用 |
| zccyrq | date | 转出出院日期。 |     |
| bcrylb | string | 补充人员类别。 | 该值用于判断参保人是否为补充医疗人员和保健人群(‘A‘ 补充医疗人员，’B’ 保健人群)，济南返回代码编号，H3地区返回具体汉字说明。 |
| csrq | datetime | 出生日期 |     |
| dwmc | string | 单位名称 |     |
| mzddbz | string | 门诊定点标志 | 值为1时，当前医院是该参保人的门诊统筹定点医院。（异地不返回） |
| mzddsm | string | 门诊定点说明 | 参保人的门诊统筹定点医院说明 （异地不返回） |
| mzdbbz | string | 门诊大病备注。 | yltclb= ‘4’时，不为空 |
| yfdxbz | string | 优抚对象标志 | 济南专用。’1’为优抚对象 |
| yfdxlb | string | 优抚对象人员类别 | 济南专用。(汉字说明) |
| ylrylbcode | string | 医疗人员类别代码 | 济南专用 |
| ybbh | string | 保险编号 | 磁卡正面的保险编号。阳煤专用 |
| tjrylb | string | 统计人员类别 | 与统计人员类别明细字段共同区分人员类别。阳煤专用。 |
| tjrylbmx | String | 统计人员类别明细 | 与统计人员类别字段共同区分人员类别。阳煤专用。 |
| ptmzjbs | string | 普通门诊慢性病备案疾病 | 威海专用：医疗统筹类别为6时，返回普通门诊慢性病备案疾病。 |
| jzrylb | string | 救助人员类别 | 具体值调用数据字典接口获取，代码编号：JZRYLB |
| pkrkbz | string | 贫困人口标志 | 1：贫困人口，0：非贫困人口 |
| dwxzmc | string | 单位性质名称 | 获取持卡人单位性质名称 |
| zhzybz | string | 有无15(医保参数控制)天内的住院记录 | 1:有，0 :无 |
| zhzysm | string | 15(医保参数控制)天内的住院记录说明 |     |
| psamkh | String | psam卡号 | 忻州省内异地使用 |
| xzqh | string | 行政区划 | 山东省内异地刷卡使用, 具体值调用数据字典接口获取，代码编号：YDXZQH |
| sbkydbz | string | 社保卡异地标志 | 山东省内异地刷卡使用，1：是，0：否 |
| kyhzh | string | 卡芯片账号 |     |
| lbjbbm | string | 居民两病备案疾病编码 | 居民两病备案疾病编码 |
| ectoken | string | 令牌  | 电子医保凭证扫码返回 |
| gsjcxx | string | 工伤检查信息 | 济南使用 |
| sbm | string | 识别码 | 跨省异地返回 |
| cbdsbh | string | 参保地市编号 | 省异地用 |
| cbjgmc | string | 参保机构名称 | 省异地用 |
| mzmxm_ejjbbm | string | 门慢的二级代码 | 聊城使用 |
| mzmxm_ejjbmc | string | 门慢的二级名称 | 聊城使用 |
| gjcbdbm | string | 共济人参保地编码 | 有共济绑定关系时返回，具体值调用数据字典接口获取，代码编号：DSBM |
| gjzhye | number | 共济账户余额 | 有共济绑定关系时返回 |
| ryid | string | 人员id |     |

### 查询个人账户余额信息

**接口名称：get_sscard_balance**

**接口作用：**查询个人账户余额信息，只有省直、东营等银行管理账户使用，济南、淄博无法使用，需通过read_card获取余额。

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_kh | string | 卡号  | 若由地纬DLL控制读卡器，p_kh可不传；若由his控制读卡器，p_kh为必传。 |
| p_grbh | string | 个人编号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultCode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| kh  | string | 卡号  |     |
| sbjgbh | string | 社保机构编号 |     |
| zhye | string | 账户余额 | 省直人员个人账户由银行管理，查询余额需通过服务get_sscard_balance。 |

### 读卡获取人员基本信息（全国异地）

**接口名称：read_card_qgyd**

**接口作用：**由地纬DLL控制读卡器时 ，全国异地读卡交易读卡获取人员基本信息。

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| p_yltclb | string | **\***医疗统筹类别 | 具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_xzbz | string | 险种标志 | 具体值调用数据字典接口获取，代码编号：XZBZ |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultCode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| sbm | string | 识别码 |     |
| xm  | string | 姓名  |     |
| shbzhm | string | 社保保障号码 |     |
| gfbb | string | 规范版本 |     |

除以上四个出参，其余与读卡获取人员基本信息（read_card）的出参一致

### 根据电子医保凭证获取个人身份证号和姓名

**接口名称：read_ewm**

**接口作用：**根据电子医保凭证二维码获取个人身份证号码和姓名。

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ewm | string | 电子医保凭证二维码 | 28位电子医保凭证 |
| p_dzpzywm | string | 电子医保凭证业务码 | 传电子医保凭证二维码时需传入。传入具体的code值<br><br>挂号：101<br><br>住院建档：102<br><br>入院登记：103<br><br>缴纳预缴金：104<br><br>问诊：201<br><br>预约检查：202<br><br>检查：203<br><br>治疗：204<br><br>结算：301<br><br>取药：302<br><br>取报告：303<br><br>打印票据和清单：304<br><br>病历材料复印：305<br><br>诊间核验身份：306 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultCode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| xm  | string | 姓名  |     |
| sfzhm | string | 身份证号码 |     |

## 门诊结算（含个人账户消费）

门诊结算方法的解释：

门诊结算过程包含门诊预结算(settle_mz_pre)和门诊结算(settle_mz)两个方法。settle_mz_pre只进行门诊预结算，返回预结算的数据，此时医保并没有结算信息；settle_mz对门诊费用进行实结算，返回结算信息。

注意：

1.  调用settle_mz_pre和settle_mz这两个方法的组合来进行门诊结算时，需要HIS自己开发预结算界面。
2.  济南地区的settle_mz服务仅包括门规、门诊统筹、个人账户消费，其中个人账户消费时，入参p_ptmzskbz传‘1’；淄博地区的settle_mz包含所有的门诊结算（含个人账户消费、门规），其中个人账户消费使用入参p_ptmzskbz来控制。

### 3.3.1门诊预结算

接口名称: settle_mz_pre

接口作用:进行门诊预结算，返回预结算结果，以让HIS系统和患者进行结算确认。

**接口类型：**查询类

**参数说明：**

传入参数：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_yltclb | string | **\***医疗统筹类别 | yltclb:4 门诊大病，5 意外伤害，6普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_grbh | string | **\***社会保障号码 |     |
| p_xm | string | **\***姓名 | 参保病人的姓名 |
| p_xb | string | **\***性别 | 参保病人性别（1:男 2:女 9:不确定），可调用数据字典接口获取，代码编号：XB |
| p_blh | string | **\***病历号 |     |
| p_fyrq | datetime | **\***费用日期 |     |
| p_ysbm | string | **\***主治医师编码 | HIS必须传入一个非空的医师编码，并且保证医师有资格，HIS系统需要与地纬结算系统编码保持一致 |
| p_jbbm | string | **\***疾病编码 | yltclb=‘4’时，必须传递；yltclb=‘5’或’6’且xzbz=’C’传递‘’,xzbz=’D’或xzbz=’E’，必须传递。济南免费用药必须传递，可传入疾病jbbm:jbmc（E14.901 ,: 糖尿病、I10 11 ,:原发性高血压、I25.101 ,:冠心病） |
| p_kh | string | 卡号  | 无卡结算：不传此参数；<br><br>有卡结算：若地纬DLL控制读卡器，持卡结算时，p_kh传“”，否则传正常卡号；若由his控制读卡器，p_kh传正常卡号。 |
| p_xzbz | string | **\***险种标志 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |
| p_ptmzskbz | string | 普通门诊刷卡标志 | 某地区职工既有门诊统筹又有纯个账消费，那么就需要传ptmzskbz，门诊统筹：0，纯个账消费：1，其他情况不需要传入。 |
| p_jbsm | string | 疾病说明 | 新增补充项目，用于保存疾病的相关说明 |
| p_jylb | string | 外地就医类别 | ‘01’本地定点就医；‘10’异地治疗.（可选参数），其他具体值调用数据字典接口获取，代码编号：JYLB |
| p_jyyybm | string | 就医医院编码 | 如果是异地治疗，需要传该参数 |
| p_jzkh | string | 就诊卡号。 | 枣庄增加参数 |
| p_mzlx | string | 门诊类型。 | 济南地区使用，其他地区不需要（2：普通门诊；3：急诊；4：急诊转住院） |
| p_sbm | string | 识别码 | 潍坊：读卡获取的识别码，持卡就医必传<br><br>跨省异地必传，传读卡返回的sbm |
| p_fwm | string | 复位码 | 潍坊：读卡获取的卡复位信息，持卡就医必传 |
| p_mfyybz | string | 免费用药标志 | 济南：社区医院/门诊、村卫生室免费用药时传入。1：免费用药，0：非免费用药，默认为0 |
| p_bcxm_ds | 数据集 | 补充项目信息 | p_bcxm_ds为结果集，bcxmbh为‘补充项目编号’，bcxmz为‘补充项目值’, 通过get_bcxm获取需要录入的补充项目 |
| p_fypd_ds | 数据集 | \*费用凭单信息 |     |
| p_pasmkh | string | psam卡号 | 忻州省内异地必传 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’’ |
| p_ycq | date | 预产期（计生手术时间） | 威海：xzbz = ‘E’时，必须传入。<br><br>淄博：xzbz = ‘E’时，必须传入 |
| p_hysj | date | 怀孕时间 | 威海：xzbz = ‘E’时，必须传入。<br><br>淄博：xzbz = ‘E’时传入。 |
| p_jhzh | string | 结婚证号 | 淄博：xzbz = ‘E’计生手术必传 |
| p_gsfsrq | date | 工伤发生日期 | 济宁：工伤住院登记时使用，非必传 |
| p_syhbz | string | 生育合并症 |     |
| p_ylzcbs | string | 医疗政策标识 | 可通过接口query_ylzcbs查询获取 |
| p_zyzd | string | 主要诊断 | 跨省异地门诊大病结算必传 |
| p_sfrzfs | **string** | \*收费认证方式 | 手工录入身份证号:00<br><br>医保电子凭证:01<br><br>读居民身份证:02<br><br>社会保障卡:03<br><br>终端扫码:04<br><br>终端扫脸:05<br><br>电子社会保障卡:06 |
| p_mzjzzzbz | **string** | 门诊急诊转诊标志 | 1:急诊,2:转诊,3:转诊合并急诊 |
| p_wsbz | **string** | 外伤标志 | 外伤门诊标志，1:是,0:否；默认为0，异地门诊外伤就医时必传 |
| p_sjdsfbz | **string** | 涉及第三方标志 | 1:是,0:否；默认为0，异地门诊外伤标志为“1”时，此项为必传 |
| p_yltclbmx | string | 医疗统筹类别明细 | 1102:新冠门诊，其他传空 |
| p_mzjsfs | string | 门诊结算方式 | 德州使用 |
| p_ywshybfdbl | string | 意外伤害医保负担比例 | 德州使用：p_mzjsfs传“ywsh”时必传 |
| p_xfzhbz | **string** | 消费账户标志 | 1:是,0:否；默认是“1”，异地就医使用(包括跨省、省内） |
| p_ksbm | string | \*就诊科室编码 |     |
| p_jbbm_ds | 数据集 | 多诊断信息 | 多个诊断时传入 |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |

p_fypd_ds为费用凭单信息，字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| yyxmbm | string | \*医院项目编码 |     |
| dj  | number | **\***单价 |     |
| zje | number | \*总金额 | （zje=dj\*sl） |
| sl  | number | \*数量 |     |
| zxksbm | string | **\***执行科室编码 |     |
| kdksbm | string | \*开单科室编码 |     |
| sxzfbl | number | \*自付比例 | 若医疗项目存在多个自付比例,需要根据获取自付比例服务获取对应的比例信息 |
| fyfssj | string | \*费用发生时间 |     |
| bzsl | number | 大包装的小包装数量 | 如果不传，则以地纬定点医疗结算系统中保存的为准 |
| yyxmmc | string | 医院项目名称 |     |
| gg  | string | 规格  |     |
| yyts | number | 用药天数 | 淄博复用：慢病结算控费项目时必传<br><br>注：这里传的可服用天数为总天数，例：本条费用为项目A，数量为2，那么传递的可服用天数就是两个A的可服用总天数 |
| sm  | string | 说明  |     |
| yysm | string | 用药说明 | （保存用药频次、单次用量、用量单位类型的辅助说明） |
| yzlsh | string | 医嘱流水号 |     |
| sfryxm | string | 收费人员姓名 |     |
| gytj | string | 给药途径 | 如“IG”：灌胃；“IV”：静脉注射，其他具体值调用数据字典接口获取，代码编号：YKGYTJ。 |
| dcyl | number | 单次用量 |     |
| yypc | string | 用药频次 | 具体值调用数据字典接口获取，代码编号：YKYYPC |
| wpbz | string | 外配标志 | 德州：外配费用标志。1：是，0：否，不传或传空默认为0 |
| zxysbm | string | 执行医师编码 |     |
| kdysbm | string | \*开单医师编码 |     |
| zsm | string | 追溯码 | 多个追溯码用,分隔，示例：11111111111111111111,22222222222222222222 |
| clbz | string | 拆零标志 | 拆零标志，0:否,1:是，不传默认为0 |

p_jbbm_ds为多诊断信息，字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| dzdjbbm | string | \*诊断疾病编码 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| zylsh | string | \*住院流水号 | 系统生成的住院流水号 |
| jbbm | string | \*疾病编码 | 门诊录入疾病返回(主要生育使用) |
| brfdje | number | \*病人负担金额 | 包含账户支付（本地结算） |
| ybfdje | number | 医保负担金额 | 医保报销总金额 |
| ylbzje | number | 医疗补助金额 | 优抚对象补助（伤残军人、遗属等） |
| yyfdje | number | 医院负担金额 |     |
| zje | number | 本次结算费用总额 | 总金额=病人负担+医保负担+医疗补助+医院负担；HIS可以不接，自己进行汇总 |
| yltcdjh | string | 医疗统筹登记号 |     |
| tjrylb | string | 统计人员类别 | 用于HIS打印门诊发票使用, 具体值调用数据字典接口获取，代码编号：TJRYLB |
| brjsrq | date | 病人结算日期 |     |
| grzhzf | number | 个人账户支付 |     |
| tczf | number | 本次统筹支付 |     |
| dezf | number | 本次大额支付 |     |
| desybx | number | 大额商业保险 |     |
| gwybz | number | 本次公务员补助 |     |
| czlz | number | 本次财政列支 |     |
| zhzf | number | 暂缓支付 |     |
| ljtczf | number | 累计统筹支付 |     |
| ljdezf | number | 累计大额支付 |     |
| ljmzed | number | 累计门诊额度 |     |
| ljgrzf | number | 累积个人支付 |     |
| qttczf | number | 其他统筹支付 |     |
| fph | string | 发票号 |     |
| fprylb | string | 发票人员类别 |     |
| zhye | number | 账户余额 |     |
| jmje | number | 优抚对象减免金额 |     |
| bcqfx | number | 本次起付线 |     |
| bcnrtcfw | number | 本次进入统筹额度 |     |
| bnynrtcfw | number | 本年进入统筹额度 |     |
| sxzfje | number | 首先自付金额 |     |
| tcfwnfd | number | 统筹范围内个人负担金额 | 统筹范围内自付（含本次起付线），济南专用 |
| ljmznrtc | number | 医保额度累计 | 按照医疗统筹类别来区分，若门规结算，则累计门规和住院的纳入统筹范围，若门诊统筹则单独累计，济南专用 |
| bcmlw | number | 本次目录外 | 济南、省直专用 |
| ljmlw | number | 累计目录外 | 济南、省直专用 |
| ljqfx | number | 累计起付线 | 济南、省直专用 |
| bcbczf | number | 本次补充支付 | 济南、省直专用 |
| ljbczf | number | 累计补充支付 | 济南、省直专用 |
| qzjbzhzf | number | 其中基本账户支付额 | 兖矿个人账户分为基本账户和补充账户，要求分别记录基本账户支付和补充账户支付，在定点中记录基本账户支付额。（兖矿专用） |
| xj  | number | 本次消费现金. | 济南普通门诊使用。 |
| dbbzje | number | 大病补助金额 |     |
| bnptmzljbxje | number | 本年度累计普通门诊报销金额/门诊额度累计(济南) |     |
| bnmzdbljbxje | number | 本年度累计门诊大病报销金额 |     |
| ecbxje | number | 二次报销金额 | 济南职工使用 |
| fpryyljgjm | number | 医疗机构减免 | 东营威海专用 |
| pkrkbcbxje | number | 商业兜底 | 潍坊：贫困人口补充报销金额； 东营：商业兜底。威海：商业兜底 |
| mzbzje | number | 民政救助 | 威海：民政救助 |
| yfbzy | number | 民政优抚 | 威海：民政优抚 |
| bfzf | number | 乙类首先自付 | 济南：乙类首先自付 |
| ljbxje | number | 累计统筹报销 | 济南：累计统筹报销 |
| ljzfje | number | 累计自付金额 | 省直：累计自付金额 |
| bbtnzbxje | number | 苯丙酮尿酸症医保报销金额 | 威海：苯丙酮尿酸症医保报销金额 |
| bbtnzyyfdje | number | 苯丙酮尿酸症医院报销金额 | 威海：苯丙酮尿酸症医院报销金额 |
| pkryzjzje | number | 贫困人口再救助 |     |
| ldybxje | number | 老党员报销金额 |     |
| cfqyts | string | 重复取药提示 | 聊城：慢性病结算30天内重复取药时返回的提示 |
| yltclbmx | string | 医疗统筹类别明细 | 601：普通门诊<br><br>602：门诊统筹 |
| hmbbxjemln | number | 惠民保报销金额目录内 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxjemlw | number | 惠民保报销金额目录外 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxje | number | 惠民保报销金额 |     |
| sydgrzhzf | number | 省异地个人账户支付 | 包含在ybfdje中，异地结算时消费的本人账户金额，可以打印发票使用。 |

中文名称前标注\*的为HIS必须接收的结果集。

备注：调用门诊预结算服务，结算平台返回结果后，需要HIS开发以下界面，显示预结算信息。

### 3.3.2门诊结算

接口名称: settle_mz

接口作用：本接口主要完成的功能：保存上一步传来的费用凭单，并上传中心进行结算，返回结算结果。

**接口类型：**交易类

备注：jshid字段的说明：本结算号为该次门诊结算在医保系统中的唯一标识,强烈建议HIS系统在自身数据库中记录这个结算号,便于票据重打,撤销结算等操作。

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_yltclb | string | **\***医疗统筹类别 | yltclb: 4 门诊大病，5 意外伤害，6普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_grbh | string | **\***社会保障号码 |     |
| p_xm | string | **\***姓名 | 参保病人的姓名 |
| p_xb | string | **\***性别 | 参保病人性别（1:男 2:女 9:不确定），可调用数据字典接口获取，代码编号：XB |
| p_blh | string | **\***病历号 |     |
| p_fyrq | datetime | **\***费用日期 |     |
| p_ysbm | string | \*主治医师编码 | HIS必须传入一个非空的医师编码，并且保证医师有资格，HIS系统需要与地纬结算系统编码保持一致 |
| p_jbbm | string | **\***疾病编码 | yltclb=‘4’时，必须传递；yltclb=’5’或’6’时，xzbz=’C’传递’’,xzbz=’D’或xzbz=’E’，必须传递。 |
| p_kh | string | 卡号  | 无卡结算：不传此参数；<br><br>有卡结算：若地纬DLL控制读卡器，持卡结算时，p_kh传“”，否则传正常卡号；若由his控制读卡器，p_kh传正常卡号； |
| p_xzbz | string | **\***险种标志 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |
| p_ptmzskbz | string | 普通门诊刷卡标志 | 某地区职工既有门诊统筹又有纯个账消费，那么就需要传ptmzskbz，门诊统筹：0，纯个账消费：1，其他情况不需要传入。<br><br>注：读卡时若由地纬DLL控制读卡器时并且需要进行刷卡校验密码时，此参数必须为“1”。 |
| p_grzhzf | number | 个人账户支付 |     |
| p_jylb | string | 外地就医类别 | ‘01’本地定点就医；‘10’异地治疗，其他具体值调用数据字典接口获取，代码编号：JYLB |
| p_jyyybm | string | 就医医院编码 | 如果是异地治疗，需要传该参数 |
| p_jbsm | string | 疾病说明 | 新增补充项目，用于保存疾病的相关说明 |
| p_needjsd | string | 是否需要结算单 | 在门诊过程中选择是否需要返回医保结算单。‘1’为需要返回；‘0’为不需要返回。不传时，默认为‘1’：返回。如果结算过程中不返回结算单，可通过print_jsd接口打印医保结算单。 |
| p_jzkh | string | 就诊卡号。 | 枣庄增加参数 |
| p_mzlx | string | 门诊类型。 | 济南地区使用，其他地区不需要其中（2：普通门诊；3：急诊；4：急诊转住院） |
| p_sydjh | string | 生育备案号 | 威海：若为生育中心备案，需要传入，若为联网自动备案，不需要传入。 |
| p_ycq | date | 预产期(计生手术时间) | 威海：xzbz = ‘E’时，必须传入。<br><br>淄博：xzbz = ‘E’时，必须传入 |
| p_sytc | number | 生育胎次 | 威海：xzbz = ‘E’,且为非流产疾病时必须传入。 |
| p_hysj | date | 怀孕时间 | 威海：xzbz = ‘E’时，必须传入。<br><br>淄博：xzbz = ‘E’时传入。 |
| p_jhzh | string | 结婚证号 | 淄博：xzbz = ‘E’,计生手术必须传入。 |
| p_syzh | string | 生育证号 | 威海：若为生育中心备案，需要传入，若为联网自动备案，不需要传入。 |
| p_lxdh | string | 联系电话 | 威海：xzbz = ‘E’时使用，非必传 |
| p_passwd | string | 社保卡口令 | 目前仅章丘使用 |
| p_sbm | string | 识别码 | 潍坊：读卡获取的的识别码，持卡就医必传<br><br>跨省异地必传，传读卡返回的sbm |
| p_fwm | string | 复位码 | 潍坊：读卡获取的的卡复位信息，持卡就医必传 |
| p_mfyybz | string | 免费用药标志 | 济南：社区医院/门诊、村卫生室免费用药时传入。1：免费用药，0：非免费用药，默认为0 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’’ |
| p_qdzf | number | 渠道支付金额 | 使用二维码所对应的渠道进行自费消费的金额 |
| p_bcxm_ds | 数据集 | 补充项目信息 | p_bcxm_ds为结果集，bcxmbh为‘补充项目编号’，bcxmz为‘补充项目值’ |
| p_fypd_ds | 数据集 | \*费用凭单信息 |     |
| p_hzid | string | \*病人ID | 对接诊间审核时必传，全院患者唯一标识，多次就诊此id应该是同一个 |
| p_jzbs | string | \*就诊标识 | 对接诊间审核时必传，当前就诊的医院标识，要能关联到结算时的数据。多次就诊应该保证此序号不同，住院和门诊的需要也不能重复 |
| p_sfbyzg | string | 是否本院职工 | 对接诊间审核时必传， |
| p_bqbs | string | 病区标识 | 对接诊间审核时必传，为了使判断更为精确，分解住院中要使用该值排转病区情况。 |
| p_zzdbm | string | 主诊断编码 | 对接诊间审核时必传，如果不传会影响诊断相关规则的判断 |
| p_zzdmc | string | 主诊断名称 | 对接诊间审核时必传，如果不传会影响诊断相关规则的判断 |
| p_dbzjsbs | string | 单病种结算标识 | 对接诊间审核时必传，\[0=否,1=是\] |
| p_bf | string | 病房号 | 对接诊间审核时必传，病房号 |
| p_cw | string | 病床号 | 对接诊间审核时必传，病床号 |
| p_bzlx | string | 病种类型 | 对接诊间审核时必传，\[1=慢性病(特殊病),2=单病种,3=急性病,9=一般疾病\] |
| p_bzdm | string | 病种代码 | 对接诊间审核时必传，对应病种类型 |
| p_bzmc | string | 病种名称 | 对接诊间审核时必传，对应病种类型 |
| p_ksbm | string | \*就诊科室编码 |     |
| p_yllb | string | \*医疗类别 | 对接诊间审核时必传，100：普通门诊,101：专科门诊,102：专家门诊,103：特需门诊,104：专病门诊105：门诊大病,106：门诊慢性病,107：门诊统筹,108：门诊特殊病,200：急诊,300：急诊观察,400：普通住院,401：特需住院,402：生育住院,403：精神病住院,500：加床,600：体检,700：普通购药,701：购药（门诊特殊病）,702：购药（门诊慢性病）,703：购药（特药）,999：其他 |
| p_syzt | string | 生育状态 | 对接诊间审核时必传，1:妊娠期 2:哺乳期 99：未知 |
| p_dzd | 数据集 | 多诊断 | 对接诊间审核时必传，包含入院诊断 |
| p_pasmkh | string | psam卡号 |     |
| p_gsfsrq | date | 工伤发生日期 | 济宁：工伤住院登记时使用，非必传 |
| p_syhbz | string | 生育合并症 |     |
| p_ylzcbs | string | 医疗政策标识 | 可通过接口query_ylzcbs查询获取 |
| p_zyzd | string | 主要诊断 | 跨省异地门诊大病结算必传 |
| p_sfrzfs | **string** | \*收费认证方式 | 手工录入身份证号:00<br><br>医保电子凭证:01<br><br>读居民身份证:02<br><br>社会保障卡:03<br><br>终端扫码:04<br><br>终端扫脸:05<br><br>电子社会保障卡:06 |
| p_mzjzzzbz | **string** | 门诊急诊转诊标志 | 1:急诊,2:转诊,3:转诊合并急诊 |
| p_wsbz | **string** | 外伤标志 | 外伤门诊标志，1:是,0:否；默认为0，异地门诊外伤就医时必传 |
| p_sjdsfbz | **string** | 涉及第三方标志 | 1:是,0:否；默认为0，异地门诊外伤标志为“1”时，此项为必传 |
| p_yltclbmx | string | 医疗统筹类别明细 | 1102:新冠门诊，其他传空 |
| p_mzjsfs | string | 门诊结算方式 | 德州使用 |
| p_ywshybfdbl | string | 意外伤害医保负担比例 | 德州使用：p_mzjsfs传“ywsh”时必传 |
| p_xfzhbz | **string** | 消费账户标志 | 1:是,0:否；默认是“1”，异地就医使用(包括跨省、省内） |
| p_jbbm_ds | 数据集 | 多诊断信息 | 多个诊断时传入 |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |

p_fypd_ds为费用凭单信息，字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| yyxmbm | string | \*医院项目编码 |     |
| dj  | number | **\***单价 |     |
| zje | number | \*总金额 | （zje=dj\*sl） |
| sl  | number | \*数量 |     |
| zxksbm | string | **\***执行科室编码 |     |
| kdksbm | string | \*开单科室编码 |     |
| sxzfbl | number | \*自付比例 | 若医疗项目存在多个自付比例,需要使用获取自付比例服务获取比例信息 |
| fyfssj | string | \*费用发生时间 |     |
| gg  | string | 规格  |     |
| bzsl | number | 大包装的小包装数量 | 如果不传，则以地纬定点医疗结算系统中保存的为准 |
| yyxmmc | string | 医院项目名称 |     |
| yyts | number | 用药天数 | 淄博复用：慢病结算控费项目时必传<br><br>注：这里传的可服用天数为总天数，例：本条费用为项目A，数量为2，那么传递的可服用天数就是两个A的可服用总天数 |
| yysm | string | 用药说明 | （保存用药频次、单次用量、用量单位类型的辅助说明） |
| sm  | string | 说明  |     |
| yzlsh | string | 医嘱流水号 |     |
| sfryxm | string | 收费人员姓名 |     |
| gytj | string | 给药途径 | 如“IG”：灌胃；“IV”：静脉注射，其他具体值调用数据字典接口获取，代码编号：YKGYTJ。 |
| dcyl | number | 单次用量 |     |
| yypc | string | 用药频次 | 具体值调用数据字典接口获取，代码编号：YKYYPC |
| yzzh | string | 医嘱组号(传处方号就行) | 对接诊间审核时必传，多个处方分组使用 |
| cfh | string | \*处方号 | 对接诊间审核时必传，用于分析单张处方药品种类超5种的情况。 |
| sfcqyz | string | 是否是长期医嘱 | 对接诊间审核时必传，1是 0否，9未知 |
| yzxw | string | 医嘱行为 | 对接诊间审核时必传，0:专科，1:住院,2:出院带药，3:转床,4:术后医嘱，5:产后医嘱,6:高病重，7:高病危,8:转区，9:会诊医嘱,10:观察医嘱，98:停止医嘱,99:其他医嘱 |
| yyjxbm | string | 医院项目（药品）剂型编码 | 对接诊间审核时必传，院内剂型编码 |
| yyjx | string | 医院项目（药品）剂型 | 对接诊间审核时必传，中文名称 |
| fylb | string | \*药品类别 | 对接诊间审核时必传，3：膳食费，4：输血费，5：护理费<br><br>0：检查费，7检验费（化验费），9：手术费，<br><br>10：输液费，11床位费，12：诊疗费，13：治疗费，15：注射费，16：输氧费，17：挂号费，<br><br>18：材料费，6：西药，27：中草药，28：中成药，99：其他 |
| cfts | number | 处方帖数 | 对接诊间审核时必传，中草药的计量 |
| jbdw | string | \*基本单位 | 对接诊间审核时必传，盒、瓶、包、袋<br><br>标准单位依据这个标识进行判断，建议传大单位，也就是数量的单位 |
| qsrq | String | \*起始日期 | 对接诊间审核时必传，医嘱开始日期,格式YYYYMMDDhh24mmss,例如<br><br>20150113221058 |
| zzrq | String | \*终止日期 | 对接诊间审核时必传，医嘱终止日期,格式YYYYMMDDhh24mmss,例如<br><br>20150113221058 |
| dcyldw | string | 单次用量单位 | 对接诊间审核时必传，如：ml、mg |
| yzdm | string | 医嘱代码 | 对接诊间审核时必传，如：大生化的代码 |
| yzmc | string | 医嘱名称 | 对接诊间审核时必传，需拼接，如：<br><br>多普勒超声检查（前列腺、肾） |
| bzdw | string | 包装单位 | 对接诊间审核时必传， |
| wpbz | string | 外配标志 | 德州：外配费用标志。1：是，0：否，不传或传空默认为0 |
| zhxs | number | 转换系数 | 对接诊间审核时必传,基本单位与包装单位的转换。例：100，表100片/盒 ，1基本单位\*转换系数 = 1包装单位 |
| zxysbm | string | 执行医师编码 |     |
| kdysbm | string | \*开单医师编码 |     |
| zsm | string | 追溯码 | 多个追溯码用,分隔，示例：11111111111111111111,22222222222222222222 |
| clbz | string | 拆零标志 | 拆零标志，0:否,1:是，不传默认为0 |

dzd为数据集,字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| zdbm | string | \*诊断编码 | 对接诊间审核时必传， |
| zdmc | string | \*诊断名称 | 对接诊间审核时必传， |
| zdlb | string | \*诊断类别 | \[1=入院诊断,2=出院诊断\] |
| zdlb2 | string | \*诊断类别 | 对接诊间审核时必传，\[0=主要诊断,1=第一辅诊,2=第二辅诊....\] |
| zdnr | string | 诊断内容 | 对接诊间审核时必传， |
| rybq | string | 入院病情 | 对接诊间审核时必传， |
| zdsj | string | 诊断时间 | 对接诊间审核时必传，YYYYMMDDhh24mmss,例如20150113221058 |

p_jbbm_ds为多诊断信息，字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| dzdjbbm | String | \*诊断代码 |     |
| maindiag_flag | String | \*主诊断标志 | 0：否，1：是 |
| diag_type | String | \*诊断类别 | 1：西医诊断，2：中医主病诊断，3：中医主证诊断，可调用数据字典接口获取，代码编号：DIAG_TYPE |
| diag_srt_no | Int | \*诊断顺序号 |     |
| diag_dept | String | \*诊断科室 | 传科室编码 |
| diag_dr_code | String | \*诊断医师编码 | 需传入国标医师编码 |
| diag_dr_name | String | 诊断医师姓名 |     |
| diag_time | Date | \*诊断时间 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| jshid | string | \*病人结算号 | (注:本结算号为该次门诊结算在医保系统中的唯一标识,强烈建议HIS系统在自身数据库中记录这个结算号,便于票据重打,撤销结算等操作) |
| jbbm | string | \*疾病编码 | 门诊录入疾病返回(主要生育使用) |
| zylsh | string | \*住院流水号 | 系统生成的住院流水号 |
| brfdje | number | \*病人负担金额 | 包含账户支付（本地结算） |
| ybfdje | number | 医保负担金额 | 医保报销总金额 |
| ylbzje | number | 医疗补助金额 | 优抚对象补助（伤残军人、遗属等） |
| yyfdje | number | 医院负担金额 |     |
| zje | number | 本次结算费用总额 | 总金额=病人负担+医保负担+医疗补助+医院负担；HIS可以不接，自己进行汇总 |
| yltcdjh | string | 医疗统筹登记号 |     |
| tjrylb | string | 统计人员类别 | 用于HIS打印门诊发票使用, , 具体值调用数据字典接口获取，代码编号：TJRYLB |
| brjsrq | date | 病人结算日期 |     |
| grzhzf | number | 个人账户支付 |     |
| tczf | number | 本次统筹支付 |     |
| dezf | number | 本次大额支付 |     |
| desybx | number | 大额商业保险 |     |
| gwybz | number | 本次公务员补助 |     |
| czlz | number | 本次财政列支 |     |
| zhzf | number | 暂缓支付 |     |
| ljtczf | number | 累计统筹支付 |     |
| ljdezf | number | 累计大额支付 |     |
| ljmzed | number | 累计门诊额度 |     |
| ljgrzf | number | 累积个人支付 |     |
| qttczf | number | 其他统筹支付 |     |
| jmje | number | 优抚对象减免金额 |     |
| fph | string | 发票号 |     |
| fprylb | string | 发票人员类别 |     |
| zhye | number | 账户余额 |     |
| bcqfx | number | 本次起付线 |     |
| bcnrtcfw | number | 本次进入统筹额度 |     |
| bnynrtcfw | number | 本年进入统筹额度 |     |
| report | String | 结算单 | 返回Base64编码的pdf格式 |
| sxzfje | number | 首先自付金额 |     |
| tcfwnfd | number | 统筹范围内个人负担金额 | 统筹范围内自付（含本次起付线），济南专用 |
| ljmznrtc | number | 医保额度累计 | 按照医疗统筹类别来区分，若门规结算，则累计门规和住院的纳入统筹范围，若门诊统筹则单独累计，济南专用 |
| bcmlw | number | 本次目录外 | 济南、省直专用 |
| ljmlw | number | 累计目录外 | 济南、省直专用 |
| ljqfx | number | 累计起付线 | 济南、省直专用 |
| bcbczf | number | 本次补充支付 | 济南、省直专用 |
| ljbczf | number | 累计补充支付 | 济南、省直专用 |
| qzjbzhzf | number | 其中基本账户支付额 | 兖矿个人账户分为基本账户和补充账户，要求分别记录基本账户支付和补充账户支付，在定点中记录基本账户支付额。（兖矿专用） |
| mzzdlsh | string | 门诊单据号 |     |
| xj  | number | 本次消费现金. |     |
| mzjslx | string | 门诊结算类型 |     |
| dbbzje | number | 大病补助金额 | 目前仅潍坊使用 |
| bnptmzljbxje | number | 本年度累计普通门诊报销金额/门诊额度累计(济南) |     |
| bnmzdbljbxje | number | 本年度累计门诊大病报销金额 |     |
| ecbxje | number | 二次报销金额 |     |
| fpryyljgjm | number | 医疗机构减免 | 东营专用 |
| pkrkbcbxje | number | 商业兜底 | 潍坊：贫困人口补充报销金额； 东营：商业兜底。威海：商业兜底 |
| mzbzje | number | 民政救助 | 威海：民政救助 |
| yfbzy | number | 民政优抚 | 威海：民政优抚 |
| bfzf | number | 乙类首先自付 | 济南：乙类首先自付 |
| ljbxje | number | 累计统筹报销 | 济南：累计统筹报销 |
| qdzfje | number | 渠道支付金额 |     |
| ljzfje | number | 累计自付金额 | 省直：累计自付金额 |
| qdbz | string | 渠道标志 | 电子社保卡交易的渠道名称 |
| bbtnzbxje | number | 苯丙酮尿酸症医保报销金额 | 威海：苯丙酮尿酸症医保报销金额 |
| bbtnzyyfdje | number | 苯丙酮尿酸症医院报销金额 | 威海：苯丙酮尿酸症医院报销金额 |
| pkryzjzje | number | 贫困人口再救助 |     |
| ldybxje | number | 老党员报销金额 |     |
| cfqyts | string | 重复取药提示 | 聊城：慢性病结算30天内重复取药时返回的提示 |
| yltclbmx | string | 医疗统筹类别明细 | 601：普通门诊<br><br>602：门诊统筹 |
| hmbbxjemln | number | 惠民保报销金额目录内 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxjemlw | number | 惠民保报销金额目录外 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxje | number | 惠民保报销金额 |     |
| sydgrzhzf | number | 省异地个人账户支付 | 包含在ybfdje中，异地结算时消费的本人账户金额，可以打印发票使用。 |
| gbjshid | string | 国标结算号ID |     |

中文名称前标注\*的为HIS必须接收的结果集。

### 3.3.3预撤销门诊结算

接口名称: destroy_mz_pre

接口作用: 预撤销门诊结算，实账户地区使用。

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*结算号id | (注:本结算号为该次门诊结算在医保系统中的唯一标识,强烈建议HIS系统在自身数据库中记录这个结算号,便于票据重打,撤销结算等操作) |
| p_blh | string | **\***病历号 |     |
| p_kh | string | 卡号  | 撤销个人账户消费时使用。 |
| p_kl | string | 口令  | 撤销个人账户消费时使用。口令由医保管理时，需传入口令；否则不传。 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’ |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |

### 3.3.4撤销门诊结算

接口名称: destroy_mz

接口作用: 撤销门诊结算

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*结算号id | (注:本结算号为该次门诊结算在医保系统中的唯一标识,强烈建议HIS系统在自身数据库中记录这个结算号,便于票据重打,撤销结算等操作) |
| p_blh | string | **\***病历号 |     |
| p_kh | string | 卡号  | 撤销个人账户消费时使用。 |
| p_kl | string | 口令  | 撤销个人账户消费时使用。口令由医保管理时，需传入口令；否则不传。 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’ |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| cxlsh | string | 冲销流水号 | 可以用于打印退费发票 |

## 住院管理

住院管理主要分为住院登记、住院费用、出院结算三类接口，住院登记包括住院登记和撤销住院登记接口，住院费用包括住院费用上传、删除指定费用和删除住院费用信息接口，出院结算包括出院结算、撤销出院和撤销出院结算接口，其结构如下图所示：

住院流程说明：1、住院登记－2、生成住院费用－3、住院费用上传－4、出院结算

撤销出院流程：1、撤销出院－2、撤销出院结算

### 3.4.1住院登记

3.4.1.1**住院登记**

**接口名称：save_zydj**

**接口作用：**保存参保职工的住院登记信息，并上传到社保中心

**接口类型：**交易类

**参数说明：**

**传入参数:**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | **\***病历号 | 病人住院时的住院号 |
| p_grbh | string | **\***社会保障号码 |     |
| p_kh | string | 卡号  | 无卡结算：不传此参数；<br><br>有卡结算：若地纬DLL控制读卡器，持卡结算时，p_kh传“”，否则传正常卡号；若由his控制读卡器，p_kh传正常卡号。 |
| p_xm | string | **\***姓名 | 参保病人的姓名 |
| p_xb | string | **\***性别 | 参保病人性别（1:男 2:女 9:不确定），可调用数据字典接口获取，代码编号：XB |
| p_yltclb | string | **\***医疗统筹类别 | 医疗统筹类别（1:住院 2:家床），其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_ksbm | string | **\***科室编码 |     |
| p_zyrq | datetime | **\***住院日期 | 住院病人的住院日期 |
| p_qzys | string | **\***确诊医师 | 确定病人病情的医师 |
| p_xzbz | string | \*险种标志 | 病人参加保险的险种（C:医疗 D:工伤 E:生育），可调用数据字典接口获取，代码编号：XZBZ |
| p_jylb | string | **\***外地就医类别 | ‘01’本地定点就医；‘10’异地治疗，其他具体值调用数据字典接口获取，代码编号：JYLB |
| p_jyyybm | string | **\***就医医院编码 | 如果是异地治疗，需要传该参数 |
| p_zyfs | string | **\***住院方式 | 具体值调用数据字典接口获取，代码编号：ZYFS |
| p_jbbm | string | **\***医保疾病编码 | 必传  |
| p_yyltcdjh | string | 转出医疗统筹登记号 | 当p_zyfs=6时，住院方式为‘市内转院’，需要录入 |
| p_cbdsbh | string | 参保地市编号 | 当本次住院为‘省级异地就医’时使用。省异地结算标示异地人员信息 |
| p_cbjgmc | string | 参保机构名称 | 当本次住院为‘省级异地就医 ’时使用。<br><br>省异地结算标示异地人员信息 |
| p_yltclbmx | string | 医疗统筹类别明细 | 101:普通住院 102:特病住院, 其他具体值调用数据字典接口获取，代码编号：YLTCLBMX<br><br>安宁疗护：990801（德州用）<br><br>医疗统筹类别为“1：住院”，则101:普通住院 102:特病住院；医疗统筹类别为“2：家床”，则203：医疗专护 204：机构护理 205：居家护理 208：失智长期照护 209：失智日间照护（聊城使用） |
| p_bqsm | string | 病情说明 | 对病人病情的描述（可选参数） |
| p_cw | number | 床位  | 病人住院时所住床位（可选参数） |
| p_fj | number | 房间  | 病人住院时所住房间（可选参数） |
| p_mzks | string | 门诊科室 |     |
| p_rqlb | string | 人群类别 | 人群类别(A：职工，B：居民)（可选参数）, 其他具体值调用数据字典接口获取，代码编号：RQLB |
| p_sydjh | string | 生育备案号 | 若为生育中心备案，需要传入，若为联网自动备案，不需要传入。 |
| p_syzh | string | 生育证号 | 若为生育中心备案，需要传入，若为联网自动备案，不需要传入。 |
| p_ycq | date | 预产期（计生手术时间） | xzbz = ‘E’时，必须传入。<br><br>xzbz=‘C’且jbbm为生育疾病时必传 |
| p_hysj | date | 怀孕时间 | xzbz = ‘E’时，必须传入。 |
| p_yecssj | date | 分娩或手术时间 | xzbz = ‘E’时使用，非必传 |
| p_sytc | number | 生育胎次 | xzbz = ‘E’,且为非流产疾病时必须传入。<br><br>xzbz=‘C’且jbbm为生育疾病时必传 |
| p_tegs | number | 胎儿个数 | xzbz = ‘E’时使用，非必传 |
| p_lxdh | string | 联系电话 | xzbz = ‘E’时使用，非必传 |
| p_jhzh | string | 结婚证号 | xzbz=‘C’且jbbm为生育疾病时使用，非必传 |
| p_sybfz | string | 生育并发症 | 险种标志为E时使用，暂时传空后续如何传入等医保通知，具体值调用数据字典接口获取，代码编号：SYBFZ。 |
| p_sfwzzyb | string | 是否危重职业病 | 工伤住院登记时使用：1:是,0:否。非必传 |
| p_gsfsrq | date | 工伤发生日期 | 工伤住院登记时使用，非必传 |
| p_bcxm_ds | 数据集 | 补充项目信息 | 用于以后功能扩展，目前一律传递’’.例如：{“gzrybh”:“1111”,“sybfz”:“003”} |
| p_fwm | string | 复位码 | 潍坊：读卡获取的复位码，持卡就医必传 |
| p_sbm | string | 识别码 | 跨省异地必传（潍坊：读卡获取的识别码，持卡就医必传） |
| p_hsbm | string | 护士编码 | 【聊城】住院登记时使用，非必传 |
| p_lxdh | string | 联系电话 | 【聊城必传】 |
| p_pasmkh | string | psam卡号 | 忻州省内异地必传 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’ |
| p_zszh | string | 准生证号 | xzbz = ‘E’时使用，非必传<br><br>xzbz=‘C’且jbbm为生育疾病时必传 |
| p_jbr | string | 经办人 | xzbz=‘C’且jbbm为生育疾病时必传 |
| p_posfzhm | string | 配偶身份证号码 | xzbz=‘C’且jbbm为生育疾病时使用，非必传 |
| p_poxm | string | 配偶姓名 | xzbz=‘C’且jbbm为生育疾病时使用，非必传 |
| p_rjbfbz | string | 日间病房标志 | 非必传,‘1’表示是,‘0’表示否。默认0 |
| p_zlfhlrjbfbz | string | 肿瘤放化疗日间病房标志 | 非必传,‘1’表示是,‘0’表示否。默认0 |
| p_rjsxbz | string | 日间手术标志 | 非必传,‘1’表示是,‘0’表示否。默认0 |
| p_fmfs | string | 分娩方式 | 枣庄：传入医保局下发的code值 |
| p_sfrzfs | string | \*收费认证方式 | 手工录入身份证号:00<br><br>医保电子凭证:01<br><br>读居民身份证:02<br><br>社会保障卡:03<br><br>终端扫码:04<br><br>终端扫脸:05<br><br>电子社会保障卡:06 |
| p_wsbz | **string** | 外伤标志 | 外伤审批住院标志，1:是,0:否；默认为0，异地住院外伤就医时必填 |
| p_sjdsfbz | **string** | 涉及第三方标志 | 1:是,0:否；默认为0，异地住院外伤标志为“1”时，此项为必填 |
| p_zylx | **string** | 住院类型 | 1:普通住院<br><br>2:急诊或抢救住院<br><br>3:转院住院<br><br>异地住院急诊、转诊必填 |
| p_zyqkdj | **string** | 住院情况登记 | 枣庄专用，必传, 默认值为普通住院(ptzy)。<br><br>具体分类(普通住院: ptzy,日间手术结算:rjss,单病种结算:dbz,意外伤害结算:ywsh，中医优势病种:zyysbz，按床日:acr，日间病房:rjbf)<br><br>德州：上传住院情况登记 |
| p_jbbm_ds | 数据集 | 多诊断信息 |     |

p_bcxm_ds为补充项目信息，示例：

|     |     |
| --- | --- |
| bcxmbh | Bcxmz |
| gzrybh | 1111 |
| gzryxm | 张三  |
| sybfz | 并发症1 |
| ssjg | 受伤经过（威海工伤使用） |
| sscd | 受伤程度（威海工伤使用） |
| yhzh | 银行账号（威海工伤使用） |
| dfjgid | 开户行（调用get_dfjgxx接口获取）（威海工伤使用） |
| lxrdh | 联系人电话（威海工伤使用） |

p_jbbm_ds为多诊断信息，字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| dzdjbbm | String | \*诊断代码 |     |
| maindiag_flag | String | \*主诊断标志 | 0：否，1：是 |
| diag_type | String | \*诊断类别 | 1：西医诊断，2：中医主病诊断，3：中医主证诊断，可调用数据字典接口获取，代码编号：DIAG_TYPE |
| diag_srt_no | Int | \*诊断顺序号 |     |
| diag_dept | String | \*诊断科室 | 传科室编码 |
| diag_dr_code | String | \*诊断医师编码 | 需传入国标医师编码 |
| diag_dr_name | String | 诊断医师姓名 |     |
| diag_time | Date | \*诊断时间 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ryzd | string | \*入院诊断 |     |
| bz  | string | \*医保审批意见 |     |
| zylsh | string | \*住院流水号 |     |
| qrbz | string | \*确认标志 | 0:尚未确认；1：联网确认；2：注销；3：不予确认；4：手工确认（不联网），其他具体值调用数据字典接口获取，代码编号：QRBZ |

备注：His开发建议：在原系统中，p­_jbbm和p_bcxm_ds为结算平台弹出窗口选择项，新的接口文档需要His传这两个参数。

3.4.1.2**撤销住院登记**

**接口名称：****destroy_zydj**

**接口作用:** 撤销住院登记，对于已出院和有已结算费用的登记记录不能撤销。撤销住院登记服务会删除本次住院登记的所有费用凭单和住院登记的记录。

**接口类型：**交易类

**传入参数:**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | **\***病历号 | 病人住院时的住院号 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败 |

### 3.4.2住院费用

*********住院费用上传**

**接口名称：save_zy_script**

**接口作用：**保存病人的住院费用凭单信息。

**接口类型：**交易类

**参数说明：**

**传入参数:**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | **\***病历号 |     |
| p_ysbm | string | **\***主治医师编码 | HIS必须传入一个非空的医师编码，并且保证医师有资格 |
| p_date | datetime | **\***费用发生日期 | 精确到天，支持到秒 |
| p_fyxx_ds | 数据集 | **\***费用信息 |     |

p­_fyxx_ds为数据集，其中包括传入的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| yyxmbm | string | \*医院项目编码 |     |
| dj  | number | \*单价 |     |
| sl  | number | \*数量 | zje=dj\*sl |
| zje | number | \*总金额 |     |
| kdksbm | string | \*开单科室编码 |     |
| zxksbm | string | \*执行科室编码 |     |
| sxzfbl | number | \*自付比例 |     |
| fysfsj | date | \*费用发生时间 |     |
| yyxmmc | string | 医院项目名称 |     |
| yzlsh | string | 医嘱流水号 |     |
| sfryxm | string | 收费人员姓名 |     |
| yyts | number | 用药天数 |     |
| yysm | string | 用药说明 | 保存用药频次、单次用量、用量单位类型的辅助说明 |
| bzsl | number | 大包装的小包装数量 | 如果不传，则以地纬定点医疗结算系统中保存的为准 |
| gg  | string | 规格  |     |
| fybclb | string | 费用明细说明 | (工伤住院联网，费用为非工伤医疗费的，传1，其他传空).济南专用 |
| yzzh | string | 医嘱组号 | 潍坊专用。若为医嘱产生的费用，该字段用于关联医嘱信息；否则不传或传“”。 |
| yzsxh | string | 医嘱顺序号 | 潍坊专用。若为医嘱产生的费用，该字段用于关联医嘱信息；否则不传或传“”。 |
| gytj | string | 给药途径 | 如“IG”：灌胃；“IV”：静脉注射，其他具体值调用数据字典接口获取，代码编号：YKGYTJ |
| dcyl | number | 单次用量 |     |
| yypc | string | 用药频次 | 具体值调用数据字典接口获取，代码编号：YKYYPC |
| wpbz | string | 外配标志 | 德州：外配费用标志。1：是，0：否，不传或传空默认为0 |
| zxysbm | string | 执行医师编码 |     |
| kdysbm | string | \*开单医师编码 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| fyid | string | \*费用凭单号 | 若产生多条费用凭单，返回结果用“，”隔开，如：“111,222” |

**3.4.2.2删除指定费用**

**接口名称：delete_fypd**

**接口作用：**删除住院期间某一个fyid对应的费用凭单（出院结算之前，若出现HIS系统费用数据与结算系统费用数据出现不一致，可以使用该服务删除某条出现问题的费用凭单，重新导入）。

**接口类型：**交易类

**传入参数:**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_fyid | string | \*费用凭单号 | 若删除多条费用凭单，用“，”隔开，如：“111,222” |
| p_blh | string | \*病历号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败 |

**3.4.2.3删除住院费用信息**

**接口名称：destroy_all_fypd**

**接口作用：**删除住院期间所有未结算的费用凭单（出院结算之前，若出现HIS系统费用数据与结算系统费用数据出现不一致，可以使用该服务删除费用信息，重新导入）。

**接口类型：**交易类

**传入参数:**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败 |

### 3.4.3出院结算

3.4.3.1**预结算**

**接口名称：****settle_zy_pre**

接口作用: 住院预结算。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 | 病历号 |
| p_cyzd | string | 出院诊断 | 参保人出院时确诊的疾病（传医保系统中存在的jbbm） |
| p_cyrq | date | \*出院日期 | 必须是病人出院的真实日期 |
| p_kh | string | 卡号  | 无卡结算：不传此参数；<br><br>有卡结算：若地纬DLL控制读卡器，持卡结算时，p_kh传“”，否则传正常卡号；若由his控制读卡器，p_kh传正常卡号。 |
| p_zlfs | string | 治疗方式 | 参保人住院期间治疗方式, 必须要传医保系统中存在的，具体值调用数据字典接口获取，代码编号：ZLFS。 |
| p_cyfs | string | 出院方式 | 参保人出院方式,必须传医保系统中存在的，具体值调用数据字典接口获取，代码编号：CYFS。 |
| p_sybfz | string | 生育并发症 | 济南专用，生育结算时使用，非必传。 |
| p_jhsysslb | string | 计划生育手术类别 | 济南专用，生育结算时使用，非必传。 |
| p_cyyxzd | string | 出院医学诊断 | 潍坊专用。传入医保系统中icd10的疾病编码。非必传。 |
| p_fwm | string | 复位码 | 潍坊：读卡获取的复位码,持卡就医必传 |
| p_sbm | string | 识别码 | 跨省异地必传（潍坊：读卡获取的识别码，持卡就医必传） |
| p_yecssj | date | 婴儿出生时间 | 生育出院结算时必传 |
| p_hysj | date | 怀孕时间 | xzbz = ‘E’时，必须传入。<br><br>xzbz = ‘E’生育必传 |
| p_sytc | number | 生育胎次 | xzbz=‘C’且cyzd为生育疾病时必传<br><br>xzbz = ‘E’生育必传 |
| p_tegs | number | 胎儿个数 | xzbz=‘C’且cyzd为生育疾病时必传<br><br>xzbz = ‘E’生育必传 |
| p_poxm | string | 配偶姓名 | 济南使用，男职工时必传，女职工费非必传 |
| p_posfzhm | string | 配偶身份证号码 | 济南使用，男职工时必传，女职工费非必传 |
| p_jhzh | string | 结婚证号 | xzbz=’E’时，计生手术必传 |
| p_jsschm | string | 计划生育服务手册（证明） | xzbz=‘C’且cyzd为生育疾病时使用，非必传 |
| p_ycq | date | 预产期（计生手术时间） | 流产出院结算时必传<br><br>xzbz=‘C’且cyzd为生育疾病时必传 |
| p_ywshbz | string | 是否无第三方责任人意外伤害 | 泰安：是：1<br><br>否：0或不传 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’ |
| p_lclj | string | 临床路径 | 滨州：临床路径只能传四种：AB、A、B、C。默认为空。临床路径和出院诊断是有对应关系的，根据上传的出院诊断传临床路径，具体的对应关系会在3.9.15定额疾病目录下载有说明。传入的出院诊断在单病种定额结算范围内时必传，其他传空。 |
| p_ssbm | string | 主要诊疗方式 | 滨州：主要诊疗方式和出院诊断是有对应关系的，根据上传的出院诊断传主要诊疗方式，具体的对应关系会在3.9.15定额疾病目录下载有说明。传入的出院诊断在单病种定额结算范围内时必传，其他传空。 |
| p_sszlsm | string | 手术诊疗说明 | 滨州：当上传的p_ssbm=’QT’的时候，该值不允许为空。 |
| p_dscbz | string | 手术单双侧标志 | 滨州：1:单侧；2：双侧;不区分可以不传 |
| p_sffhsyjt | string | 是否符合生育津贴发放条件 | 淄博xzbz=‘C’且cyzd为生育疾病时必传。0：否，1：是 |
| p_yxzmh | string | 出生医学证明号 |     |
| p_fmfs | string | 分娩方式 | 可调用数据字典接口获取，代码编号：FMFS |
| p_ylzcbs | string | 医疗政策标识 | 可通过接口query_ylzcbs查询获取 |
| p_syhbz | string | 生育合并症 | 济南专用，生育结算时使用，非必传。<br><br>具体值调用数据字典接口获取，代码编号：SYHBZ。 |
| p_cyjsfs | string | 出院结算方式 | 枣庄、泰安专用，必传, 默认值为普通住院(ptzy)。具体分类(普通住院: ptzy,日间手术结算:rjss,单病种结算:dbz,意外伤害结算:ywsh，中医优势病种:zyysbz，按床日:acr，日间病房:rjbf)<br><br>德州：上传出院结算方式 |
| p_ywshybfdbl | **number** | 意外伤害医保负担比例 | 德州：值的范围是0到1，出院结算方式为ywsh时必传 |
| p_cqjcf | string | 是否报销产前检查费 | 聊城生育专用，1：报销，0：不报销，默认为0 |
| p_sfrzfs | **string** | \*收费认证方式 | 手工录入身份证号:00<br><br>医保电子凭证:01<br><br>读居民身份证:02<br><br>社会保障卡:03<br><br>终端扫码:04<br><br>终端扫脸:05<br><br>电子社会保障卡:06 |
| p_syjcf | double | 生育检查费 | 聊城医疗生育专用（险种标志为C，疾病为生育疾病） |
| p_xfzhbz | **string** | 消费账户标志 | 1:是,0:否；默认是“1”，异地就医使用(包括跨省、省内） |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| jshid | string | \*医保系统的病人结算号 |     |
| brfdje | number | 病人负担金额 |     |
| ybfdje | number | 医保负担金额 | 医保报销总金额 |
| yyfdje | number | 医院负担金额 |     |
| ylbzje | number | 医疗补助金额 |     |
| grzhzf | number | 个人账户支付 |     |
| fph | string | 发票号 |     |
| brjsrq | date | 病人结算日期 |     |
| cgxjje | number | 超过限价金额 |     |
| cbcwf | number | 省属离休人员超标床位费. |     |
| bcqfx | number | 本次起付线 |     |
| bcnrtcfw | number | 本次进入统筹额度 |     |
| bnynrtcfw | number | 本年进入统筹额度 |     |
| kye | number | 账户余额 |     |
| qttczf | number | 其他统筹支付 |     |
| tczf | number | 统筹支付 | 本次统筹支付（2位小数）。 |
| zje | number | 总金额 | 本次住院总费用。 |
| jmje | number | 优抚对象减免金额。 |     |
| bntczfje | number | 本年统筹累计支付金额。 |     |
| bnzycs | number | 本年住院次数。 |     |
| czlz | number | 财政列支。 |     |
| desybx | number | 大额商业保险。 |     |
| dezf | number | 大额支付。 |     |
| gwybz | number | 公务员补助。 |     |
| dbbzje | number | 大病补助金额 | 潍坊，济南使用 |
| dbzybjsje | number | 单病种医保结算金额 | 单病种支付标准，仅淄博使用 |
| dbzyyfdje | number | 单病种医院负担金额 | 仅淄博使用 |
| dbzgrfdje | number | 单病种个人负担金额 | 仅淄博使用 |
| fpryyljgjm | number | 医疗机构减免 | 东营威海专用 |
| pkrkbcbxje | number | 商业兜底 | 潍坊：贫困人口补充报销金额； 东营：商业兜底。威海：商业兜底 |
| wjbfje | number | 卫计帮扶金额 |     |
| mzbzje | number | 民政补助金额 |     |
| bczf | number | 补充支付 |     |
| mzbzje | number | 民政救助 | 威海：民政救助 |
| yfbzy | number | 民政优抚 | 威海：民政优抚 |
| report | String | 结算单 | 返回Base64编码的pdf格式 |
| pkrkyljgjmbz | string | 贫困人口医疗机构减免标志 | 枣庄：0:否,1：是，为1时结算可上传pkrkyljgjmje，以医院上传的为准计算贫困人口医疗机构减免 |
| brfdje_s | string | 病人负担金额集合 | 枣庄：病人负担金额集合，多段结算时用逗号拼接成一个集合 |
| pkryzjzje | number | 贫困人口再救助 |     |
| bczf | number | 职工补充支付 | 济南职工补充支付，打发票使用 |
| zdjbjzbxje | number | 重大疾病救助额 |     |
| zdjbzjzbxje | number | 重大疾病再救助额 |     |
| bchjjze | number | 合计救助金额 | 本次合计救助是mzbcjze+zdjbjzbxje+zdjbzjzbxje |
| jsbjzzf | number | 精神病救助保险报销额 | 东营：精神病救助保险报销额 |
| cqjcf | number | 生育产前检查费 | 潍坊：生育产前检查费 |
| zhzf | number | 暂缓支付 |     |
| fpryyljgjmje | number | 医疗机构减免 |     |
| ztdjbzf | number | 重特大疾病救助 |     |
| zftd | number | 政府托底救助 |     |
| cjrtd | number | 重度残疾人托底救助 |     |
| ldytdjzbxje | number | 老党员托底救助 |     |
| ldybxje | number | 老党员报销金额 |     |
| hmbbxjemln | number | 惠民保报销金额目录内 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxjemlw | number | 惠民保报销金额目录外 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxje | number | 惠民保报销金额 |     |
| ksydylbzje | number | 跨省异地医疗补助金额 | 此金额为跨省异地医疗补助金额 + 跨省异地民政补助金额 |
| bfzf | number | 部分自付 |     |
| qezf | number | 全额自付 |     |
| sydgrzhzf | number | 省异地个人账户支付 | 包含在ybfdje中，异地结算时消费的本人账户金额，可以打印发票使用。 |
| yltcdjh | string | 医疗统筹登记号 |     |
| gbjshid | string | 国标结算号ID |     |

3.4.3.2**结算**

**接口名称：settle_zy**

接口作用: 只办理结算。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 | 病历号 |
| p_cyzd | string | 出院诊断 | 参保人出院时确诊的疾病（传医保系统中存在的jbbm） |
| p_cyrq | date | \*出院日期 | 必须是病人真实出院日期 |
| p_kh | string | 卡号  | 无卡结算：不传此参数；<br><br>有卡结算：若地纬DLL控制读卡器，持卡结算时，p_kh传“”，否则传正常卡号；若由his控制读卡器，p_kh传正常卡号。 |
| p_zlfs | string | 治疗方式 | 参保人住院期间治疗方式, 必须要传医保系统中存在的，具体值调用数据字典接口获取，代码编号：ZLFS。 |
| p_cyfs | string | 出院方式 | 参保人出院方式,必须传医保系统中存在的，具体值调用数据字典接口获取，代码编号：CYFS。 |
| p_needjsd | string | 是否需要结算单 | 在住院结算过程中选择是否需要返回医保结算单。‘1’为需要返回；‘0’为不需要返回。不传时，默认为‘1’：不返回。如果结算过程中不返回结算单，可通过print_jsd接口打印医保结算单。 |
| p_sybfz | string | 生育并发症 | 济南专用，生育结算时使用，非必传。 |
| p_jhsysslb | string | 计划生育手术类别 | 济南专用，生育结算时使用，非必传。 |
| p_cyyxzd | string | 出院医学诊断 | 潍坊专用。传入医保系统中icd10的疾病编码。非必传。 |
| p_sfsygrzh | string | 是否使用个人账户 | ‘1’使用个人账户，‘0’不使用个人账户，默认为‘0’，此参数为虚账户地区（济南、淄博、忻州）专用 |
| p_grzhzf | number | 个人账户支付 | 当p_grzhzf大于0时，p_sfsygrzh参数无效，默认取‘1’，淄博地区当p_grzhzf为0且p_sfsygrzh为1时，默认使用中心计算的可使用个人账户值，此参数为虚账户地区（济南、淄博）专用 |
| p_cyks | String | 出院科室 | 非必传，有需求时传入 |
| p_sbm | string | 识别码 | 跨省异地必传（潍坊：读卡获取的识别码，持卡就医必传） |
| p_fwm | string | 复位码 | 潍坊：读卡获取的复位码，持卡就医必传 |
| p_yecssj | date | 婴儿出生时间 | 生育出院结算时必传 |
| p_ycq | date | 预产期（计生手术时间） | 流产出院结算时必传<br><br>xzbz=‘C’且cyzd为生育疾病时必传 |
| p_hysj | date | 怀孕时间 | xzbz = ‘E’时，必须传入。 |
| p_sytc | number | 生育胎次 | xzbz=‘C’且cyzd为生育疾病时必传<br><br>xzbz = ‘E’生育必传 |
| p_tegs | number | 胎儿个数 | xzbz=‘C’且cyzd为生育疾病时必传<br><br>xzbz = ‘E’生育必传 |
| p_poxm | string | 配偶姓名 | 济南使用，男职工时必传，女职工费非必传 |
| p_posfzhm | string | 配偶身份证号码 | 济南使用，男职工时必传，女职工费非必传 |
| p_jhzh | string | 结婚证号 | xzbz=’E’时，计生手术必传 |
| p_jsschm | string | 计划生育服务手册（证明） | xzbz=‘C’且cyzd为生育疾病时必传 |
| p_ywshbz | string | 是否无第三方责任人意外伤害 | 泰安：是：1<br><br>否：0或不传 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’ |
| p_lclj | string | 临床路径 | 滨州：临床路径只能传四种：AB、A、B、C。默认为空。临床路径和出院诊断是有对应关系的，根据上传的出院诊断传临床路径，具体的对应关系会在3.9.15定额疾病目录下载有说明。传入的出院诊断在单病种定额结算范围内时必传，其他传空。 |
| p_ssbm | string | 主要诊疗方式 | 滨州：主要诊疗方式和出院诊断是有对应关系的，根据上传的出院诊断传主要诊疗方式，具体的对应关系会在3.9.15定额疾病目录下载有说明。传入的出院诊断在单病种定额结算范围内时必传，其他传空。 |
| p_sszlsm | string | 手术诊疗说明 | 滨州：当上传的p_ssbm=’QT’的时候，该值不允许为空。 |
| p_dscbz | string | 手术单双侧标志 | 滨州：1:单侧；2：双侧;不区分可以不传 |
| p_pkrkyljgjmje | string | 贫困人口医疗机构减免金额 | 枣庄：预结算返回的pkrkyljgjmbz为1时传入，分段结算时由逗号分隔连接多次减免金额一起传入，**不传默认为0** |
| p_sffhsyjt | string | 是否符合生育津贴发放条件 | 淄博xzbz=‘C’且cyzd为生育疾病时必传。0：否，1：是 |
| p_yxzmh | string | 出生医学证明号 |     |
| p_fmfs | string | 分娩方式 | 可调用数据字典接口获取，代码编号：FMFS |
| p_ylzcbs | string | 医疗政策标识 | 可通过接口query_ylzcbs查询获取 |
| p_syhbz | string | 生育合并症 | 济南专用，生育结算时使用，非必传。<br><br>具体值调用数据字典接口获取，代码编号：SYHBZ。 |
| p_cyjsfs | string | 出院结算方式 | 枣庄、泰安专用，必传, 默认值为普通住院(ptzy)。具体分类(普通住院: ptzy,日间手术结算:rjss,单病种结算:dbz,意外伤害结算:ywsh，中医优势病种:zyysbz，按床日:acr，日间病房:rjbf)<br><br>德州：上传出院结算方式 |
| p_ywshybfdbl | **number** | 意外伤害医保负担比例 | 德州：值的范围是0到1，出院结算方式为ywsh时必传 |
| p_cqjcf | string | 是否报销产前检查费 | 聊城生育专用，1：报销，0：不报销，默认为0 |
| p_sfrzfs | **string** | \*收费认证方式 | 手工录入身份证号:00<br><br>医保电子凭证:01<br><br>读居民身份证:02<br><br>社会保障卡:03<br><br>终端扫码:04<br><br>终端扫脸:05<br><br>电子社会保障卡:06 |
| p_zyks | string | 是否为中医科室 | 德州 是否为中医院中医科室，1：是，0：否，默认为0，非必传 |
| p_syjcf | double | 生育检查费 | 聊城医疗生育专用（险种标志为C，疾病为生育疾病） |
| p_xfzhbz | **string** | 消费账户标志 | 1:是,0:否；默认是“1”，异地就医使用(包括跨省、省内） |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| jshid | string | \*医保系统的病人结算号 | 本结算号为该次住院在医保系统中的最后一次结算的唯一标识，强烈建议HIS系统在自身数据库中记录这个结算号，便于票据重打，撤销结算等操作。 |
| brfdje | number | 病人负担金额 |     |
| ybfdje | number | 医保负担金额 | 医保报销总金额 |
| yyfdje | number | 医院负担金额 |     |
| ylbzje | number | 医疗补助金额 |     |
| grzhzf | number | 个人账户支付 |     |
| fph | string | 发票号 |     |
| brjsrq | date | 病人结算日期 |     |
| cgxjje | number | 超过限价金额 |     |
| cbcwf | number | 省属离休人员超标床位费. |     |
| bcqfx | number | 本次起付线 |     |
| bcnrtcfw | number | 本次进入统筹额度 |     |
| bnynrtcfw | number | 本年进入统筹额度 |     |
| qttczf | number | 其他统筹支付 |     |
| tczf | number | 统筹支付 | 本次统筹支付（2位小数）。 |
| zje | number | 总金额 | 本次住院总费用。 |
| jmje | number | 优抚对象减免金额。 |     |
| bntczfje | number | 本年统筹累计支付金额。 |     |
| bnzycs | number | 本年住院次数。 |     |
| czlz | number | 财政列支。 |     |
| desybx | number | 大额商业保险。 |     |
| dezf | number | 大额支付。 |     |
| gwybz | number | 公务员补助。 |     |
| dbbzje | number | 大病补助金额 |     |
| dbzybjsje | number | 单病种医保结算金额 | 单病种支付标准，仅淄博使用 |
| dbzyyfdje | number | 单病种医院负担金额 | 仅淄博使用 |
| dbzgrfdje | number | 单病种个人负担金额 | 仅淄博使用 |
| fpryyljgjm | number | 医疗机构减免 | 东营威海专用 |
| pkrkbcbxje | number | 商业兜底 | 潍坊：贫困人口补充报销金额； 东营：商业兜底。威海：商业兜底 |
| wjbfje | number | 卫计帮扶金额 |     |     |
| mzbzje | number | 民政补助金额 |     |     |
| mzbzje | number | 民政救助 | 威海：民政救助 |
| yfbzy | number | 民政优抚 | 威海：民政优抚 |
| pkrkyljgjmje | munber | 贫困人口医疗机构减免金额 | 枣庄：贫困人口医疗机构减免金额 |
| pkryzjzje | number | 贫困人口再救助 |     |
| bczf | number | 职工补充支付 | 济南职工补充支付，打发票使用 |
| zdjbjzbxje | number | 重大疾病救助额 |     |
| zdjbzjzbxje | number | 重大疾病再救助额 |     |
| bchjjze | number | 合计救助金额 | 本次合计救助是mzbcjze+zdjbjzbxje+zdjbzjzbxje |
| jsbjzzf | number | 精神病救助保险报销额 | 东营：精神病救助保险报销额 |
| cqjcf | number | 生育产前检查费 | 潍坊：生育产前检查费 |
| zhzf | number | 暂缓支付 |     |
| fpryyljgjmje | number | 医疗机构减免 |     |
| ztdjbzf | number | 重特大疾病救助 |     |
| zftd | number | 政府托底救助 |     |
| cjrtd | number | 重度残疾人托底救助 |     |
| ldytdjzbxje | number | 老党员托底救助 |     |
| ldybxje | number | 老党员报销金额 |     |
| hmbbxjemln | number | 惠民保报销金额目录内 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxjemlw | number | 惠民保报销金额目录外 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxje | number | 惠民保报销金额 |     |
| ksydylbzje | number | 跨省异地医疗补助金额 | 此金额为跨省异地医疗补助金额 + 跨省异地民政补助金额 |
| bfzf | number | 部分自付 |     |
| qezf | number | 全额自付 |     |
| sydgrzhzf | number | 省异地个人账户支付 | 包含在ybfdje中，异地结算时消费的本人账户金额，可以打印发票使用。 |
| yltcdjh | string | 医疗统筹登记号 |     |
| gbjshid | string | 国标结算号ID |     |

备注：His开发建议：在原系统中，p_cyzd、p_cyrq、p_zlfs、p_cyfs为结算平台弹出窗口选择项，新的接口文档需要His传这四个参数。

3.4.3.3**出院**

**接口名称：****outhosp**

接口作用: 结算并办理出院手续。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |
| p_zlfs | string | \*治疗方式 | 参保人住院期间治疗方式, 必须要传医保系统中存在的，具体值调用数据字典接口获取，代码编号：ZLFS。 |
| p_cyfs | string | \*出院方式 | 参保人出院方式,必须传医保系统中存在的，具体值调用数据字典接口获取，代码编号：CYFS。 |
| p_cyzd | string | 出院诊断 | 参保人出院时确诊的疾病（传医保系统中存在的jbbm） |
| p_cyrq | date | \*出院日期 | 必须是病人出院的真实日期 |
| p_kh | string | 卡号  | 无卡结算：不传此参数；<br><br>有卡结算：若地纬DLL控制读卡器，持卡结算时，p_kh传“”，否则传正常卡号；若由his控制读卡器，p_kh传正常卡号。 |
| p_needcyd | string | 是否需要出院单 | 在出院时选择是否需要返回出院单（即统筹结算单）。‘1’为需要返回；‘0’为不需要返回。不传时，默认为‘1’：返回。如果出院时不返回出院单，可通过print_cyd接口打印出院单。 |
| p_sfsygrzh | string | 是否使用个人账户 | ‘1’使用个人账户，‘0’不使用个人账户，默认为‘0’，此参数为虚账户地区（济南、淄博）专用 |
| p_grzhzf | number | 个人账户支付 | 当p_grzhzf大于0时，p_sfsygrzh参数无效，默认取‘1’，淄博地区当p_grzhzf为0且p_sfsygrzh为1时，默认使用中心计算的可使用个人账户值，此参数为虚账户地区（济南、淄博）专用 |
| p_sybfz | string | 生育并发症 | 济南专用，生育结算时使用，非必传。<br><br>xzbz = ‘E’时使用，，暂时传空后续如何传入等医保通知，具体值调用数据字典接口获取，代码编号：SYBFZ。 |
| p_jhsysslb | string | 计划生育手术类别 | 济南专用，生育结算时使用，非必传。 |
| p_ycq | date | 预产期（计生手术时间） | xzbz = ‘E’时传入<br><br>xzbz=‘C’且cyzd为生育疾病时必传 |
| p_hysj | date | 怀孕时间 | xzbz = ‘E’时，必须传入。 |
| p_sytc | number | 生育胎次 | xzbz=‘C’且cyzd为生育疾病时必传<br><br>xzbz = ‘E’生育必传 |
| p_tegs | number | 胎儿个数 | xzbz=‘C’且cyzd为生育疾病时必传<br><br>xzbz = ‘E’生育必传 |
| p_poxm | string | 配偶姓名 | 济南使用，男职工时必传，女职工费非必传 |
| p_posfzhm | string | 配偶身份证号码 | 济南使用，男职工时必传，女职工费非必传 |
| p_jhzh | string | 结婚证号 | xzbz=’E’时，计生手术必传 |
| p_jsschm | string | 计划生育服务手册（证明） | xzbz=‘C’且cyzd为生育疾病时必传 |
| p_cyyxzd | string | 出院医学诊断 | 潍坊专用。 |
| p_cyks | String | 出院科室 | 非必传，有需求时传入 |
| p_fwm | string | 复位码 | 潍坊：读卡获取的复位码，持卡就医必传 |
| p_yecssj | date | 婴儿出生时间 | 威海生育出院结算时必传 |
| p_yrq | date | 流产或引产手术时间 | 威海流产出院结算时必传 |
| p_sbm | string | 识别码 | 跨省异地必传（潍坊：读卡获取的识别码，持卡就医必传） |
| p_cyzd2 | string | 出院诊断2 | 增加出院诊断，传p_jbbm_ds，出院诊断2-9可不传 |
| p_cyzd3 | string | 出院诊断3 | 增加出院诊断 |
| p_cyzd4 | string | 出院诊断4 | 增加出院诊断 |
| p_cyzd5 | string | 出院诊断5 | 增加出院诊断 |
| p_cyzd6 | string | 出院诊断6 | 增加出院诊断 |
| p_cyzd7 | string | 出院诊断7 | 增加出院诊断 |
| p_cyzd8 | string | 出院诊断8 | 增加出院诊断 |
| p_cyzd9 | string | 出院诊断9 | 增加出院诊断 |
| p_ywshbz | string | 是否无第三方责任人意外伤害 | 泰安：是：1<br><br>否：0或不传 |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’ |
| p_lclj | string | 临床路径 | 滨州：临床路径只能传四种：AB、A、B、C。默认为空。临床路径和出院诊断是有对应关系的，根据上传的出院诊断传临床路径，具体的对应关系会在3.9.15定额疾病目录下载有说明。传入的出院诊断在单病种定额结算范围内时必传，其他传空。 |
| p_ssbm | string | 主要诊疗方式 | 滨州：主要诊疗方式和出院诊断是有对应关系的，根据上传的出院诊断传主要诊疗方式，具体的对应关系会在3.9.15定额疾病目录下载有说明。传入的出院诊断在单病种定额结算范围内时必传，其他传空。 |
| p_sszlsm | string | 手术诊疗说明 | 滨州：当上传的p_ssbm=’QT’的时候，该值不允许为空。 |
| p_dscbz | string | 手术单双侧标志 | 滨州：1:单侧；2：双侧;不区分可以不传 |
| p_pkrkyljgjmje | string | 贫困人口医疗机构减免金额 | 枣庄：预结算返回的pkrkyljgjmbz为1时传入，分段结算时由逗号分隔连接多次减免金额一起传入，**不传默认为0** |
| p_sffhsyjt | string | 是否符合生育津贴发放条件 | 淄博xzbz=‘C’且cyzd为生育疾病时必传。0：否，1：是 |
| p_yxzmh | string | 出生医学证明号 |     |
| p_fmfs | string | 分娩方式 | 可调用数据字典接口获取，代码编号：FMFS |
| p_ylzcbs | string | 医疗政策标识 | 可通过接口query_ylzcbs查询获取 |
| p_syhbz | string | 生育合并症 | 济南专用，生育结算时使用，非必传。<br><br>具体值调用数据字典接口获取，代码编号：SYHBZ。 |
| p_cyjsfs | string | 出院结算方式 | 枣庄、泰安专用，必传, 默认值为普通住院(ptzy)。具体分类(普通住院: ptzy,日间手术结算:rjss,单病种结算:dbz,意外伤害结算:ywsh，中医优势病种:zyysbz，按床日:acr，日间病房:rjbf)<br><br>德州：上传出院结算方式 |
| p_ywshybfdbl | **number** | 意外伤害医保负担比例 | 德州：值的范围是0到1，出院结算方式为ywsh时必传 |
| p_sfrzfs | **string** | \*收费认证方式 | **手工录入身份证号:00**<br><br>**医保电子凭证:01**<br><br>**读居民身份证:02**<br><br>**社会保障卡:03**<br><br>**终端扫码:04**<br><br>**终端扫脸:05**<br><br>**电子社会保障卡:06** |
| p_syjcf | double | 生育检查费 | 聊城医疗生育专用（险种标志为C，疾病为生育疾病） |
| p_xfzhbz | **string** | 消费账户标志 | 1:是,0:否；默认是“1”，异地就医使用(包括跨省、省内），若先调用settle_zy再调用该接口，可不传。 |
| p_jbbm_ds | **数据集** | 多诊断信息 |     |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |

p_jbbm_ds为多诊断信息，字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| dzdjbbm | String | \*诊断代码 |     |
| maindiag_flag | String | \*主诊断标志 | 0：否，1：是 |
| diag_type | String | \*诊断类别 | 1：西医诊断，2：中医主病诊断，3：中医主证诊断，可调用数据字典接口获取，代码编号：DIAG_TYPE |
| diag_srt_no | Int | \*诊断顺序号 |     |
| diag_dept | String | \*诊断科室 | 传科室编码 |
| diag_dr_code | String | \*诊断医师编码 | 需传入国标医师编码 |
| diag_dr_name | String | 诊断医师姓名 |     |
| diag_time | Date | \*诊断时间 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |     |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |     |
| jshid | string | \*医保系统的病人结算号 | 本结算号为该次住院在医保系统中的最后一次结算的唯一标识，强烈建议HIS系统在自身数据库中记录这个结算号，便于票据重打，撤销结算等操作。如果出院时没有需要结算的费用，则返回为空 |     |
| brfdje | number | 病人负担金额 |     |     |
| ybfdje | number | 医保负担金额 | 医保报销总金额 |     |
| yyfdje | number | 医院负担金额 |     |     |
| ylbzje | number | 医疗补助金额 |     |     |
| grzhzf | number | 个人账户支付 |     |     |
| fph | string | 发票号 |     |     |
| brjsrq | date | 病人结算日期 |     |     |
| qtbrjsh | string | 其他病人结算号 | 若病人一次住院有多次结算，本结算号为该次住院结算在医保系统中的非最后一次结算的结算标识，组合方式：brjsh1#brjsh2#，强烈建议HIS系统在自身数据库中记录这个结算号,便于撤销结算等操作。若本次住院时只有一次结算，则返回值为空 |     |
| cgxjje | number | 超过限价金额 |     |     |
| bcqfx | number | 本次起付线 |     |     |
| bcnrtcfw | number | 本次进入统筹额度 |     |     |
| bnynrtcfw | number | 本年进入统筹额度 |     |     |
| qttczf | number | 其他统筹支付 |     |     |
| cbcwf | number | 省属离休人员超标床位费. |     |     |
| jmje | number | 优抚对象减免金额。 |     |     |
| bntczfje | number | 本年统筹累计支付金额。 |     |     |
| bnzycs | number | 本年住院次数。 |     |     |
| czlz | number | 财政列支。 |     |     |
| desybx | number | 大额商业保险。 |     |     |
| dezf | number | 大额支付。 |     |     |
| gwybz | number | 公务员补助。 |     |     |
| tczf | number | 统筹支付 | 本次统筹支付（2位小数）。核3地区有，济南、省直地区无 |     |
| zje | number | 总金额 | 本次住院总费用。核3地区有，济南、省直地区无 |     |
| fprylb | string | 发票人员类别 |     |     |
| dbbzje | number | 大病补助金额 | H3地区使用，目前仅潍坊使用 |     |
| dbzybjsje | number | 单病种医保结算金额 | 单病种支付标准，仅淄博使用 |     |
| dbzyyfdje | number | 单病种医院负担金额 | 仅淄博使用 |     |
| dbzgrfdje | number | 单病种个人负担金额 | 仅淄博使用 |     |
| fpryyljgjm | number | 医疗机构减免 | 东营威海专用 |     |
| pkrkbcbxje | number | 商业兜底 | 潍坊：贫困人口补充报销金额； 东营：商业兜底。威海：商业兜底 |     |
| wjbfje | number | 卫计帮扶金额 | 济南使用 |     |
| mzbzje | number | 民政补助金额 | 济南使用 |     |
| mzbzje | number | 民政救助 | 威海：民政救助 |     |
| yfbzy | number | 民政优抚 | 威海：民政优抚 |     |
| pkrkyljgjmje | munber | 贫困人口医疗机构减免金额 | 枣庄：贫困人口医疗机构减免金额 |
| pkryzjzje | number | 贫困人口再救助 |     |
| bczf | number | 职工补充支付 | 济南职工补充支付，打发票使用 |
| zdjbjzbxje | number | 重大疾病救助额 |     |
| zdjbzjzbxje | number | 重大疾病再救助额 |     |
| bchjjze | number | 合计救助金额 | 本次合计救助是mzbcjze+zdjbjzbxje+zdjbzjzbxje |
| jsbjzzf | number | 精神病救助保险报销额 | 东营：精神病救助保险报销额 |
| cqjcf | number | 生育产前检查费 | 潍坊：生育产前检查费 |
| zhzf | number | 暂缓支付 |     |
| fpryyljgjmje | number | 医疗机构减免 |     |
| ztdjbzf | number | 重特大疾病救助 |     |
| zftd | number | 政府托底救助 |     |
| cjrtd | number | 重度残疾人托底救助 |     |
| ldytdjzbxje | number | 老党员托底救助 |     |
| hmbbxjemln | number | 惠民保报销金额目录内 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxjemlw | number | 惠民保报销金额目录外 | 这部分金额在惠民保报销金额（hmbbxje）里面 |
| hmbbxje | number | 惠民保报销金额 |     |
| ksydylbzje | number | 跨省异地医疗补助金额 | 此金额为跨省异地医疗补助金额 + 跨省异地民政补助金额 |
| bfzf | number | 部分自付 |     |
| qezf | number | 全额自付 |     |
| sydgrzhzf | number | 省异地个人账户支付 | 包含在ybfdje中，异地结算时消费的本人账户金额，可以打印发票使用。 |
| yltcdjh | string | 医疗统筹登记号 |     |
| gbjshid | string | 国标结算号ID |     |

3.4.3.5**撤销住院结算**

接口名称：destroy_zyjs

**接口作用:** 撤销出院结算。

**接口类型：**交易类

**接口说明：**撤销出院结算前必须先撤销此次出院。

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*病人结算号 |     |
| p_blh | string | \*病历号 |     |
| p_authno | **string** | 刷脸授权码 | 传医保综合服务终端（三类终端刷脸）返<br><br>回的授权码authNo |
| p_ewm | string | 二维码 | 电子社保卡或医保电子凭证二维码号或令牌ectoken（三类终端刷脸或解码时获取），传二维码号时p_kh传’ |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |

3.4.3.6**撤销出院**

**接口名称：**destroy_cy

**接口作用:** 撤销出院。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |

### 3.4.4保存床位信息

**接口名称：save_cw**

**接口作用:** 保存住院病人的房间号床位信息。除济南以外地区使用。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |
| p_fj | date | \*房间 |     |
| p_cw | string | \*床位号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |

1.  
2.  1.  
    2.  
    3.  
    4.  

## 共济账户消费

虚账户地区：济南，淄博，烟台，胜利油田

就诊人异地就医、就诊人参保地是实账户地区、就诊人参保地和共济人参保地不同，符合以上三种任意一种情况的共济账户消费需调用该接口。举例：

例1：:就诊人张三和共济人李四都是枣庄参保，就诊人张三在枣庄（实账户地区）或山东省内其他地市（异地就医）消费李四的共济账户时，需调用该接口消费。

例2：就诊人张三是枣庄参保，共济人李四是济南参保（参保地不同），就诊人张三在枣庄或山东省内其他地市就医消费李四的共济账户时，需调用该接口消费。

例3：就诊人张三是济南参保，共济人李四也是济南参保，就诊人张三在济南就医消费李四的共济账户时，原门诊或者住院结算接口中可实现同步消费李四的共济账户余额，无需额外调用此接口。

注意：调用该接口前需先调用门诊结算或出院结算接口，使用电子医保凭证时正确上传p_ewm信息，获取结算号后调用共济账户消费接口。

调用说明：

1.  调用地纬动态库接口且地纬控卡

消费流程：read_card—settle_gzyd

退费流程：read_card—destroy_gjzh

1.  调用地纬动态库接口且地纬不控卡，或者不调用地纬动态库接口。

消费流程：query_gjzh—settle_gjzh

退费流程：query_gjzh—destroy_gjzh

- - 1.  查询共济账户信息（动态库且地纬控卡）

接口名称: read_card

接口作用：调用地纬动态库接口，使用社保卡或电子医保凭证且地纬控卡时，查询共济账户信息及消费平台订单号

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |

|     |     |     |     |
| --- | --- | --- | --- |
| **p_cbdbm** | string | 参保地编码 | 异地就医时传入，本地就医可不传，不传默认为本地就医。具体值调用数据字典接口获取，代码编号：DSBM |
| **p_ewm** | string | 二维码 | 参保人使用电子医保凭证时必传<br><br>联迪A8智能POS支持传入“dzybpz”调用pos扫码，其他情况传入电子医保凭证码值 |
| **p_sfzhm** | string | 身份证号码 |     |
| **p_xm** | string | 姓名  |     |
| **p_kh** | string | 卡号  |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | **类型** | **中文名称** | **说明** |
| **resultcode** | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| **sptddh** | string | 平台订单号 | 调用平台进行查询时返回的订单号，在共济账户消费或退费时使用 |
| **gjzhxm** | string | 共济账户姓名 |     |
| **gjsfzhm** | string | 共济人身份证号码 |     |
| **gjcbdbm** | string | 共济人参保地编码 | 具体值调用数据字典接口获取，代码编号：DSBM |
| **gjzhye** | number | 共济账户余额 |     |
| **bz** | string | 备注  | 2020年7月17日，参保人XX绑定XXX（配偶） |

- - 1.  查询共济账户信息（非动态库或非地纬控卡）

接口名称: query_gjzh

接口作用：调用地纬动态库接口且地纬不控卡，或不调用动态库接口时，查询共济账户信息及消费订单号

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |

|     |     |     |     |
| --- | --- | --- | --- |
| **p_cbdbm** | string | 参保地编码 | 异地就医时传入，本地就医可不传，不传默认为本地就医。具体值调用数据字典接口获取，代码编号：DSBM |
| **p_ewm** | string | 二维码 | 参保人使用电子医保凭证时必传 |
| **p_sfzhm** | string | **\***身份证号码 |     |
| **p_xm** | string | **\***姓名 |     |
| **p_kh** | string | 卡号  | 参保人使用社保卡时必传 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | **类型** | **中文名称** | **说明** |
| **resultcode** | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| **sptddh** | string | 平台订单号 | 调用平台进行查询时返回的订单号，在共济账户消费或退费时使用 |
| **gjzhxm** | string | 共济账户姓名 |     |
| **gjsfzhm** | string | 共济人身份证号码 |     |
| **gjcbdbm** | string | 共济人参保地编码 | 具体值调用数据字典接口获取，代码编号：DSBM |
| **gjzhye** | number | 共济账户余额 |     |
| **bz** | string | 备注  | 2020年7月17日，参保人XX绑定XXX（配偶） |

- - 1.  共济账户消费（动态库且地纬控卡）

接口名称: settle_gzyd

接口作用：调用地纬动态库接口，使用社保卡或电子医保凭证且地纬控卡时，消费共济账户余额。

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| **p_cbdbm** | string | **\***参保地编码 | 具体值调用数据字典接口获取，代码编号：DSBM |
| **p_sptddh** | string | **\***平台订单号 | 调用查询共济账户信息read_card返回的平台订单号 |
| **p_zje** | number | **\***消费金额 |     |
| **p_jshid** | string | **\***结算号ID | 住院或门诊结算后返回的结算号 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | 类型  | 中文名称 | 说明  |
| **sptddh** | string | \*平台订单号 | 退费时候必传 |
| **gjzhye** | number | 共济账户余额 |     |
| **gjzhxfje** | number | 共济账户支付金额 |     |

- - 1.  共济账户消费（非动态库或非地纬控卡）

接口名称: settle_gjzh

接口作用：调用地纬动态库接口且地纬不控卡，或不调用动态库接口时，消费共济账户余额。

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| **p_cbdbm** | string | 参保地编码 | 异地就医时传入，本地就医可不传，不传默认为本地就医。具体值调用数据字典接口获取，代码编号：DSBM |
| **p_sptddh** | string | **\***平台订单号 | 调用查询共济账户信息query_gjzh返回的平台订单号 |
| **p_zfje** | number | **\***消费金额 |     |
| **p_jshid** | string | **\***结算号ID | 住院或门诊结算后返回的结算号 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | 类型  | 中文名称 | 说明  |
| **sptddh** | string | \*平台订单号 | 退费时候必传 |
| **gjzhye** | number | 共济账户余额 |     |
| **gjzhxfje** | number | 共济账户支付金额 |     |

- - 1.  撤销共济账户消费

接口名称: destroy_gjzh

接口作用：消费共济账户后，调用该接口进行撤销。

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | **类型** | **中文名称** | **说明** |
| **p_cbdbm** | string | 参保地编码 | 异地就医时传入，本地就医可不传，不传默认为本地就医。具体值调用数据字典接口获取，代码编号：DSBM |
| **p_sptddh** | string | **\***平台订单号 | 调用查询共济账户信息返回的平台订单号 |
| **p_ysptddh** | string | \*原平台订单号 | 消费的时候返回的**sptddh** |
| **p_tfje** | number | **\***退费金额 | 传入正数 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| gjzhye | number | 共济账户余额 |     |
| gjzhxfje | number | 共济账户退费金额 |     |

- - 1.  查询共济账户消费明细信息

接口名称: query_jyxx_gjzh

接口作用：查询共济账户消费明细信息。

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | **类型** | **中文名称** | **说明** |
| **p_sptddh** | stirng | **\***平台订单号 | 消费的时候返回的sptddh |

返回结果集：

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | 类型  | 中文名称 | 说明  |
| jymx_ds | 数据集 | 交易明细 |     |

jymx_ds为交易明细数据集，字段如下：

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| jydbm | string | 就医地编码 |     |
| yljgbh | string | 医疗机构编号 |     |
| cbdbm | string | 参保地编码 |     |
| sptddh | string | 平台订单号 |     |
| ysptddh | string | 原平台订单号 | jylx为2时传递，为对应正交易的省平台订单号 |
| ddkssj | datetime | 订单开始时间 | 格式：yyyyMMddHHmmss |
| ddjssj | datetime | 订单结束时间 | 格式：yyyyMMddHHmmss |
| jyzt | string | 交易状态 | 1:处理中,2:成功,3:失败,4:未知 |
| gjzhxm | string | 共济人姓名 |     |
| gjsfzhm | string | 共济人身份证号码 |     |
| jylx | string | 交易类型 | 1:消费,2:退费 |
| gjzhxfje | number | 共济账户支付金额 |     |
| xm  | string | 就诊人姓名 |     |
| sfzhm | string | 就诊人证件号码 |     |

## 目录管理

### 获取自付比例

**接口名称：get_zfbl**

**接口作用:** 取得单个医疗项目的自付比例及其说明。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_yyxmbm | string | \*医院项目编码 |     |
| p_rq | date | \*日期 |     |
| p_grbh | string | 个人编号 |     |
| p_yltclb | string | 医疗统筹类别 | 4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_xzbz | string | 险种标准 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| zfbl_ds | 数据集 | 自付比例信息 | 若zfbl_ds为空，则表明自付比例为0 |

zfbl_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| zfbl | number | 自付比例 |     |
| sm  | string | 说明  |     |
| rqlb | string | 人群类别 | 原社保机构类型，A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |

### 查询医院项目目录

**接口名称：query_yyxm_info**

**接口作用:** 查询医院项目目录。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |
| p_ypbz | string | 药品标志 | 全部下载时数据量过大，容易下载失败，增加参数，按药品标志分类下载,调用数据字典接口获取，代码编号：YPBZ |
| p_yyxmbm | string | 医院项目编码 | 默认接空，如果为空时下载全部，不为空时按yyxmbm过滤进行下载。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| yyxm_ds | 数据集 | 医院项目信息 |     |

yyxm_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| yyxmbm | string | 医院项目编码 |     |
| yyxmmc | string | 医院项目名称 |     |
| mzjsxmbh | string | 门诊结算项目编号 |     |
| zyjsxmbh | string | 住院结算项目编号 |     |
| zfbl | number | 自付比例 | 自负比例的百分数,比如自负比例为15%,则该数值为15 |
| sm  | string | 自付比例说明 |     |
| ylxmbm | string | 医疗项目编码 |     |
| gg  | string | 规格  | 包装单位 |
| dw  | string | 单位  |     |
| ckj | number | 参考价 |     |
| jxm | string | 剂型  | 具体值调用数据字典接口获取，代码编号：JXMC |
| scqy | string | 生产企业 |     |
| cfybz | string | 处方药标志 | 1:是,0或空:否 |
| gmpbz | string | GMP标志 | 1:是,0或空:否 |
| zxgg | string | 最小规格 |     |
| bzsl | number | 包含数量 |     |
| gxsj | string | 更新时间 |     |
| qsrq | date | 起始日期 | 自付比例的起始日期 |
| zzrq | date | 终止日期 | 自付比例的终止日期 |
| spbz | string | 审批标志 | ‘0’：尚未审批；‘1’:审批通过；‘2’：审批未通过, 可调用数据字典接口获取，代码编号：SPBZ |
| xzbz | string | 险种标志 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |
| ypbz | string | 药品标志 | 1：药品, 其他具体值调用数据字典接口获取，代码编号：YPBZ |
| rqlb | string | 人群类别 | 原社保机构类型，A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| bzgg | string | 包装规格 |     |
| yltclb | string | 医疗统筹类别 | 自付比例的医疗统筹类别 |
| dj  | number | 单价  |     |

### 查询医保核心端目录

**接口名称：query_ml**

**接口作用:** 查询医保核心端目录和自付比例信息。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ylxm_ds | 数据集 | 医疗项目 |     |
| sxzfbl_ds | 数据集 | 首先自付比例 |     |

ylxm_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ylxmbm | string | 医疗项目编码 |     |
| ylxmbzmc | string | 医疗项目标准名称 |     |
| py  | string | 拼音  |     |
| syz | string | 适用症 |     |
| jj  | string | 禁忌  |     |
| gg  | string | 规格  | 包装单位 |
| dw  | string | 单位  |     |
| ckj | number | 参考价 |     |
| jxm | string | 剂型码 | 具体值调用数据字典接口获取，代码编号：JXMC |
| zxbz | string | 注销标志 | 1:注销,0或空:未注销，可调用数据字典接口获取，代码编号：ZXBZ |
| scqy | string | 生产企业 |     |
| cdm | string | 产地码 | C:进口GMP达标,D,进口非GMP达标,G:国产,H:合资,M:合资GMP达标, 其他具体值调用数据字典接口获取，代码编号：CDM |
| cfybz | string | 处方药标志 | 1:是,0或空:否 |
| gmpbz | string | GMP标志 | 1:是,0或空:否 |
| zxgg | string | 最小规格 |     |
| gxsj | string | 更新时间 |     |
| ypbz | string | 药品标志 | 1：药品, 其他具体值调用数据字典接口获取，代码编号：YPBZ |
| jsxmbh | string | 结算项目编号 |     |
| zczh | string | 注册证号 |     |
| pzwh | string | 批准文号 |     |
| bzgg | string | 包装规格 |     |
| lstd_fil_no | string | 上市备案号 |     |

sxzfbl_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ylxmbm | string | 医疗项目编码 |     |
| rqlb | string | 人群类别 | 原社保机构类型：A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| yltclb | string | 医疗统筹类别 | 4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| dyrylb | string | 待遇人员类别 | 具体值调用数据字典接口获取，代码编号：DYRYLB |
| qsrq | date | 起始日期 | 自付比例的起始日期 |
| zzrq | date | 终止日期 | 自付比例的终止日期 |
| sxzfbl | number | 首先自付比例 |     |
| sm  | string | 说明  |     |
| xzbz | string | 险种标志 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |

### 新增或者更新医院项目

**接口名称：add_yyxm_info_all**

**接口作用:** HIS系统新增一个医院项目，同时调用该服务存入地纬结算系统数据库，如果该医院项目已经存在则视为更新。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_yyxmbm | string | \*医院项目编码 |     |
| p_yyxmmc | string | \*医院项目名称 |     |
| p_ypbz | string | \*药品标志 | 1：药品，0:诊疗，2:一次性材料, 其他具体值调用数据字典接口获取，代码编号：YPBZ |
| p_dj | number | \*最小包装规格的单价 |     |
| p_zxgg | string | \*最小规格 |     |
| p_bhsl | number | \*大包装包含小规格的数量 |     |
| p_zdgg | string | \*大包装规格 |     |
| p_mzjsxmbh | string | \*门诊结算项目编号 | 使用地纬结算系统的项目编号 |
| p_zyjsxmbh | string | \*住院结算项目编号 | 使用地纬结算系统的项目编号 |
| p_jj | string | 禁忌  |     |
| p_scqy | string | 生产企业 |     |
| p_spm | string | 商品名 |     |
| p_dw | string | 单位  |     |
| p_gmpbz | string | 是否GMP | 1：GMP，0：非GMP |
| p_cfybz | string | 是否处方药 | 1：处方药，0：非处方药 |
| p_jxm | string | 剂型  | 具体值调用数据字典接口获取，代码编号：JXMC |
| p_ylxmbm | string | 对应的医保项目编码 | 为空时，需要到地纬结算平台中维护和医保目录的对应关系。(医保目录可以通过query_ml服务获取) |
| p_bcxm | string | 补充信息 |     |
| p_syz | string | 适应症 |     |
| p_pjcpmc | string | 批件产品名称 |     |
| p_zczh | string | 注册证号 |     |
| p_zcjzrq | date | 注册截止日期 | 日期格式：yyyyMMdd |
| p_cdm | string | 产地  | 具体值调用数据字典接口获取，代码编号：CDM |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |

### 新增或者更新医师信息

**接口名称：add_ys**

**接口作用:** HIS系统新增或者更新一个医师信息，同时调用该服务存入地纬结算系统数据库，如果该医师已经存在则视为更新。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ysbm | string | \*医师编号 |     |
| p_xm | string | \*医师姓名 |     |
| p_xb | string | \*性别 | 1:男，2:女，9:不确定，可调用数据字典接口获取，代码编号：XB |
| p_sfzhm | string | \*身份证号码 |     |
| p_ksbm | string | \*科室编码 |     |
| p_yszt | string | \*医师状态 | 1:正常，2:注销，3:暂停，其他具体值调用数据字典接口获取，代码编号：YSZT |
| p_csrq | date | \*出生日期 |     |
| p_bz | string | 备注  |     |
| p_zw | string | 职务  |     |
| p_zyyszbh | string | 执业医师证编号 |     |
| p_zcyszbh | string | 注册医师证编号 |     |
| p_yszc | string | 医师职称 |     |
| p_zylb | string | 执业类别 | 具体值调用数据字典接口获取，代码编号：YSZYLB |
| p_zyfw | string | 执业范围 | 具体值调用数据字典接口获取，代码编号：YSZYFW |
| p_zydd | string | 执业地点 |     |
| p_bgxx | string | 变更信息 |     |
| p_bcxx | string | 补充信息 |     |
| p_yzrylb | string | 医职人员类别 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |

### 查询医保疾病目录

**接口名称：query_si_sick**

接口作用:查询医保疾病目录。

**接口类型：**查询类

**参数说明:**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ybjb_ds | 数据集 | 医保疾病信息 |     |

ybjb_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| jbbm | string | 疾病编码 |     |
| jbmc | string | 疾病名称 |     |
| py  | string | 疾病名称拼音 |     |
| mzdblb | string | 门诊大病类别 | mzdblb不为空时，为门诊大病； mzdblb为空时，为普通疾病, 其他具体值调用数据字典接口获取，代码编号：MZDBLB |
| sbjgbh | string | 社保机构编号 |     |
| xzfw | string | 险种范围 |     |
| zxbz | string | 注销标志 | 1:注销,0或空:未注销，可调用数据字典接口获取，代码编号：ZXBZ |
| fwfw | string | 服务范围 |     |
| bz  | string | 备注  |     |

### 查询医保手术目录

**接口名称：query_operation**

接口作用:查询医保手术目录。

**接口类型：**查询类

**参数说明:**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ssxm_ds | 数据集 | 医保手术项目信息 |     |

ssxm_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ssbm | string | 手术编码 |     |
| ssmc | string | 手术名称 |     |
| py  | string | 手术名称拼音 |     |
| zxbz | string | 注销标志 | 1:注销,0或空:未注销，可调用数据字典接口获取，代码编号：ZXBZ |
| ybbs | string | 医保标识 | 淄博：医保标识为1时可用 |

### 查询医院医师信息

**接口名称：**query_ys

**接口作用:** 查询医院医师信息服务。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ys_ds | 数据集 | 医师信息 |     |

ys_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ysbm | string | 医师编号 |     |
| xm  | string | 医师姓名 |     |
| xb  | string | 性别  | 1:男，2:女，9:不确定，可调用数据字典接口获取，代码编号：XB |
| sfzhm | string | 身份证号码 |     |
| ksbm | string | 科室编码 |     |
| yszt | string | 医师状态 | 1:正常，2:注销，3:暂停，其他具体值调用数据字典接口获取，代码编号：YSZT |
| csrq | date | 出生日期 |     |
| bz  | string | 备注  |     |
| zw  | string | 职务  |     |
| zyyszbh | string | 执业医师证编号 |     |
| zcyszbh | string | 注册医师证编号 |     |
| yszc | string | 医师职称 |     |
| zylb | string | 执业类别 | 具体值调用数据字典接口获取，代码编号：YSZYLB |
| zyfw | string | 执业范围 | 具体值调用数据字典接口获取，代码编号：YSZYFW |
| zydd | string | 执业地点 |     |
| yzrylb | string | 医职人员类别 |     |

### 增量查询下载医院项目目录

**接口名称：query_yyxm_info_by_sxh**

**接口作用:** 增量查询下载医院项目目录。

**接口类型：**查询类

参数说明:

**交易说明:** 定点机构可通过调用该交易下载增量或全部的目录信息，开始流水号传0就可以下载中心保存的所有的本医院的目录信息。如果记录较多会影响交易结算速度，所以医院端最好在业务量少的时候调用该交易。

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |
| p_sxh | double | 顺序号 | 医院端目前本地数据库中目录最大的流水号。长度为20 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| yyxm_ds | 数据集 | 医院项目信息 |     |
| sfjxxz | string | 是否还有需更新的记录 | 1有，0无 |
| sl  | integer | 本次下载的记录条数 |     |
| xj_ds | 数据集 | 限价信息 |     |

yyxm_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| sxh | double | 记录顺序号 |     |
| yyxmbm | string | 医院项目编码 |     |
| yyxmmc | string | 医院项目名称 |     |
| mzjsxmbh | string | 门诊结算项目编号 |     |
| zyjsxmbm | string | 住院结算项目编号 |     |
| zfbl | number | 自付比例 | 自负比例的百分数,比如自负比例为15%,则该数值为15 |
| sm  | string | 自付比例说明 |     |
| ylxmbm | string | 医疗项目编码 |     |
| gg  | string | 规格  | 包装单位 |
| dw  | string | 单位  |     |
| ckj | number | 参考价 |     |
| jxm | string | 剂型  | 具体值调用数据字典接口获取，代码编号：JXMC |
| scqy | string | 生产企业 |     |
| cfybz | string | 处方药标志 | 1:是,0或空:否 |
| gmpbz | string | GMP标志 | 1:是,0或空:否 |
| zxgg | string | 最小规格 |     |
| bzsl | number | 包含数量 |     |
| gxsj | string | 更新时间 |     |
| qsrq | date | 起始日期 | 自付比例的起始日期 |
| zzrq | date | 终止日期 | 自付比例的终止日期 |
| spbz | string | 审批标志 | ‘0’：尚未审批；‘1’:审批通过；‘2’：审批未通过, 可调用数据字典接口获取，代码编号：SPBZ |
| xzbz | string | 险种标志 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |
| ypbz | string | 药品标志 | 1：药品, 其他具体值调用数据字典接口获取，代码编号：YPBZ |
| rqlb | string | 人群类别 | 原社保机构类型，A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| bzgg | string | 包装规格 |     |
| yltclb | string | 医疗统筹类别 | 自付比例的医疗统筹类别 |
| dj  | number | 单价  |     |

xj_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ylxmbm | string | 医疗项目编码 |     |
| rqlb | string | 人群类别 | A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| yltclb | string | 医疗统筹类别 | 4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| dyrylb | string | 待遇人员类别 | 具体值调用数据字典接口获取，代码编号：DYRYLB |
| qsrq | date | 起始日期 |     |
| zzrq | date | 终止日期 |     |
| xj  | number | 限价  |     |
| bz  | string | 备注  |     |
| yybm | string | 医院编码 |     |
| yyjb | string | 医院级别 |     |
| yljgxz | string | 医疗机构性质 |     |

### 增量查询下载医保核心端目录

**接口名称：query_ml_by_sxh**

**接口作用:** 增量查询下载医保核心端目录和自付比例信息。

**接口类型：**查询类

参数说明:

**交易说明:** 定点机构可通过调用该交易下载增量或全部的目录信息，开始流水号传0就可以下载中心所有的医保目录信息。如果记录较多会影响交易结算速度，所以医院端最好在业务量少的时候调用该交易。

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |
| p_sxh | double | 顺序号 | 医院端目前本地数据库中目录最大的流水号。长度为20。上传本顺序号时，请取要下载的社保局的最大顺序号，不要取全部数据的最大顺序号 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |     |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |     |
| ylxm_ds | 数据集 | 医疗项目 |     |     |
| sxzfbl_ds | 数据集 | 首先自付比例 |     |     |
| sfjxxz | string | 是否还有需更新的记录 | 1有，0无 |
| sl  | integer | 本次下载的记录条数 | 指医疗项目数据集数量，不是首先自付比例数据集数量 |
| xj_ds | 数据集 | 限价信息 |     |

ylxm_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| sxh | double | 记录顺序号 |     |
| ylxmbm | string | 医疗项目编码 |     |
| ylxmbzmc | string | 医疗项目标准名称 |     |
| py  | string | 拼音  |     |
| syz | string | 适用症 |     |
| jj  | string | 禁忌  |     |
| gg  | string | 规格  | 包装单位 |
| dw  | string | 单位  |     |
| ckj | number | 参考价 |     |
| jxm | string | 剂型码 | 具体值调用数据字典接口获取，代码编号：JXMC |
| zxbz | string | 注销标志 | 1:注销,0或空:未注销，可调用数据字典接口获取，代码编号：ZXBZ |
| scqy | string | 生产企业 |     |
| cdm | string | 产地码 | C:进口GMP达标,D,进口非GMP达标,G:国产,H:合资,M:合资GMP达标, 其他具体值调用数据字典接口获取，代码编号：CDM |
| cfybz | string | 处方药标志 | 1:是,0或空:否 |
| gmpbz | string | GMP标志 | 1:是,0或空:否 |
| zxgg | string | 最小规格 |     |
| gxsj | string | 更新时间 |     |
| ypbz | string | 药品标志 | 1：药品, 其他具体值调用数据字典接口获取，代码编号：YPBZ |
| jsxmbh | string | 结算项目编号 |     |
| zczh | string | 注册证号 |     |
| pzwh | string | 批准文号 |     |
| bzgg | string | 包装规格 |     |
| lstd_fil_no | string | 上市备案号 |     |

sxzfbl_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ylxmbm | string | 医疗项目编码 |     |
| rqlb | string | 人群类别 | 原社保机构类型：A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| yltclb | string | 医疗统筹类别 | 4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| dyrylb | string | 待遇人员类别 | 具体值调用数据字典接口获取，代码编号：DYRYLB |
| qsrq | date | 起始日期 | 自付比例的起始日期 |
| zzrq | date | 终止日期 | 自付比例的终止日期 |
| sxzfbl | number | 首先自付比例 |     |
| sm  | string | 说明  |     |
| xzbz | string | 险种标志 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |

xj_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| **参数名称** | **类型** | **中文名称** | **说明** |
| **ylxmbm** | string | 医疗项目编码 |     |
| **rqlb** | string | 人群类别 | 原社保机构类型：A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| **yltclb** | string | 医疗统筹类别 | 4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| **dyrylb** | string | 待遇人员类别 | 具体值调用数据字典接口获取，代码编号：DYRYLB |
| **qsrq** | date | 起始日期 |     |
| **zzrq** | date | 终止日期 |     |
| **xj** | number | 限价  |     |
| **bz** | string | 备注  |     |
| **yybm** | string | 医院编码 |     |
| **yyjb** | string | 医院级别 |     |
| **yljgxz** | string | 医疗机构性质 |     |

### 定额疾病目录下载

**接口名称：**query_jbml_ssbm

**接口作用:** 下载医保中心的定额疾病目录（滨州使用）

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |
| p_rqlb | string | \*人群类别 | 要下载目录的人群类别(A:职工；B：居民) |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| jbml_ds | 数据集 | 就医信息 |     |

jbml_ds 为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| jbbm | string | 疾病编码 |     |
| ssbm | string | 手术编码 |     |
| ssmc | string | 手术名称 |     |
| lclj | string | 临床路径 |     |
| yybm | string | 医院编码 |     |
| yyxz | string | 医院性质 |     |
| yyjb | string | 医院级别 |     |
| rqlb | string | 人群类别 |     |

### 查询医院医保科室信息

**接口名称：**query_hosp_dept

**接口作用:** 查询医院医保科室服务。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |
| p_ksbm | string | 科室编码 | 传空查询所有 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| dept_ds | 数据集 | 科室信息 |     |

dept_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ksbm | string | 科室编码 |     |
| ksmc | string | 科室名称 |     |
| qsrq | date | 起始日期 | 日期格式：yyyyMMdd |
| zzrq | date | 终止日期 | 日期格式：yyyyMMdd |

### 增量查询下载医保疾病目录

**接口名称：query_si_sick_by_sxh**

**接口作用:** 增量查询下载医保疾病目录。

**接口类型：**查询类

参数说明: 调用接口时需要传入社保机构编号

**交易说明:** 定点机构可通过调用该交易下载增量或全部的目录信息，开始流水号传0就可以下载中心所有的医保目录信息。如果记录较多会影响交易结算速度，所以医院端最好在业务量少的时候调用该交易。

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_filetype | string | 出参返回格式 | p_filetype的值为“excel”、“txt”、或“json”其中一种。<br><br>不传时，默认为“json”。 |
| p_sxh | double | \*顺序号 | 医院端目前本地数据库中目录最大的流水号。长度为20。上传本顺序号时，请取要下载的社保局的最大顺序号，不要取全部数据的最大顺序号,第一次下载p_sxh请上传0。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |     |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |     |
| ybjb_ds | 数据集 | 医保疾病 |     |     |
| sfjxxz | string | 是否还有需更新的记录 | 1有，0无 |
| sl  | integer | 本次下载的记录条数 | 指医疗项目数据集数量，不是首先自付比例数据集数量 |

ybjb_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| sxh | double | 记录顺序号 |     |
| jbbm | string | 疾病编码 |     |
| jbmc | string | 疾病名称 |     |
| py  | string | 疾病名称拼音 |     |
| mzdblb | string | 门诊大病类别 | mzdblb不为空时，为门诊大病； mzdblb为空时，为普通疾病, 其他具体值调用数据字典接口获取，代码编号：MZDBLB |
| sbjgbh | string | 社保机构编号 |     |
| xzfw | string | 险种范围 |     |
| zxbz | string | 注销标志 | 1:注销,0或空:未注销，可调用数据字典接口获取，代码编号：ZXBZ |
| fwfw | string | 服务范围 |     |
| bz  | string | 备注  |     |

## 查询在院病人信息

**接口名称：**query_zybrxx

**接口作用:** 查询正在住院的病人相关信息。

**接口类型：**查询类

参数说明:

**传入参数：无**

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| zybrxx_ds | 数据集 | 医保疾病信息 |     |

zybrxx_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| blh | string | 病历号 |     |
| grbh | string | 社会保障号码 |     |
| sbjgbh | string | 社保机构编号 |     |
| xm  | string | 姓名  |     |
| zyrq | date | 住院日期 |     |
| ryzd | string | 入院诊断 | 入院疾病编码 |

## 查询在院病人费用明细

**接口名称：**query_zybrfy

**接口作用:** 查询在院病人费用明细，根据传入的起始日期和终止日期返回这些天的住院费用总额和费用明细。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |
| p_qsrq | date | \*起始日期 |     |
| p_zzrq | date | \*终止日期 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| brfy_ds | 数据集 | 在院病人费用 |     |
| zje | number | 总金额 |     |

brfy_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| zje | number | 总金额 |     |
| yyxmbm | string | 医院项目编码 |     |
| dj  | number | 单价  |     |
| gg  | string | 规格  |     |
| dw  | string | 单位  |     |
| sl  | number | 数量  |     |
| tcje | number | 纳入统筹金额 |     |
| zfje | number | 统筹外金额 |     |
| zfbl | string | 自负比例 |     |

## 查询药品审批信息

**接口名称：**query_yyxm_spxx

**接口作用:** 查询药品审批信息。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_yyxmbm | string | \*医院项目编码 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | String | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| yyxmbm | String | 医院项目编码 |     |
| ylxmbm | String | 医疗项目编码 |     |
| spbz | String | 审批信息 | 0：尚未审批；1:审批通过；2：审批未通过；-1：未上报对应关系, 可调用数据字典接口获取，代码编号：SPBZ |

## 查询住院审批信息

**接口名称：**query_spxx

**接口作用:** 查询病人住院登记的审批信息。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_zylsh | string | \*住院流水号 | 住院登记时返回的zylsh |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | String | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ryzd | String | 入院诊断 |     |
| bz  | String | 医保审批意见 |     |
| zylsh | String | 住院流水号 |     |
| qrbz | String | 确认标志 | 0:尚未确认；1：联网确认；2：注销；3：不予确认；4：手工确认（不联网），其他具体值调用数据字典接口获取，代码编号：QRBZ |

## 获取补充项目

**接口名称：**get_bcxm

**接口作用:** 获取补充项目信息。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_servicename | string | \*服务名 | 调用**某个**服务时需要补充录入的信息项。 |
| p_yltclb | string | \*医疗统筹类别 | 4：门诊大病，5：意外伤害，6：普通门诊统筹，其他具体值调用数据字典接口获取，代码编号：YLTCLB |
| p_xzbz | string | \*险种标识 | 医疗 C，工伤 D，生育 E，可调用数据字典接口获取，代码编号：XZBZ |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| bcxm_ds | 数据集 | 补充项目信息 |     |

bcxm_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| dmbh | string | 代码编号 |     |
| name | string | 名称  |     |
| notnull | string | 必填  | 该字段为1时，为必填 |
| type | string | 类型  |     |

## 数据字典

**接口名称：**query_si_code

**接口作用:** 获取数据字典。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_dmbh | string | \*代码编号 | 具体值参见各接口入参。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| code_ds | 数据集 | 数据字典 |     |

code_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| code | string | 代码编号 |     |
| content | string | 字典名称 |     |

## 单据打印

### 打印入院通知单

**接口名称：print_rytzd**

**接口作用:** 打印入院通知单。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |
| report | string | 入院通知单 | 返回Base64编码的pdf格式 |

### 打印结算单

**接口名称：print_jsd**

**接口作用:** 打印结算单。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*病人结算号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |
| report | string | 结算单 | 返回Base64编码的pdf格式 |

### 打印出院单

**接口名称：print_cyd**

**接口作用:** 打印出院单。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |
| p_zylsh | string | 住院流水号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |
| report | string | 出院单 | 返回Base64编码的pdf格式 |

### 打印门诊结算发票

**接口名称：print_jsfp**

**接口作用:** 打印门诊结算发票。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*病人结算号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |
| report | string | 门诊结算发票 | 返回Base64编码的pdf格式 |

### 打印住院自费明细单

**接口名称：print_zyzfmxd**

**接口作用:** 打印住院自费明细单。

**接口类型：**查询类

参数说明：

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_zylsh | string | \*住院流水号 |     |
| p_dyxx | string | 打印选项 | 1:只打印非全额统筹项目,2:只打印目录外项目。 不传时默认为“1”。 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |
| report | string | 住院自费明细单 | 返回Base64编码的pdf格式 |

### 打印POS小票

**接口名称：****print_posxp**

**接口作用:** 打印pos小票。

**接口类型：**查询类

参数说明：

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*病人结算号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |
| report | string | 住院自费明细单 | 返回Base64编码的pdf格式 |

## 新增修改或者注销操作人员

**接口名称：add_czy**

**接口作用:** HIS系统新增修改或者注销一个操作员，同时调用该服务存入地纬结算系统数据库，如果该操作人员已经存在则视为更新。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_rybh | string | \*人员编号 | 操作人员登录名 |
| p_xm | string | \*姓名 |     |
| p_xb | string | \*性别 | 1:男，2:女，9:不确定，可调用数据字典接口获取，代码编号：XB |
| p_sfzhm | string | 身份证号码 |     |
| p_password | string | 登录密码 | 8位以内数字或字母；不传或者为空时，默认为666666 |
| p_zxrq | date | 注销日期 |     |
| p_zxbz | string | 注销标志 | 1：已注销；0：正常；不传默认为0：正常 |
| p_campusid | string | 院区编号 | 传入操作员所在院区的院区编号 |
| p_czylx | string | 操作员类型 | 可调用数据字典接口获取，代码编号：CZYLX |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |

## 个账消费明细查询

**接口名称：**query_ gzxfmx

**接口作用:** 查询病人在某个时间段的个账消费明细。仅限济南使用。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_kh | string | **\***卡号 |     |
| p_qsrq | date | \*起始日期 |     |
| p_zzrq | date | \*终止日期 |     |
| p_sfzhm | string | \*身份证号码 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| gzxfmx_ds | 数据集 | 个账消费明细 |     |

gzxfmx \_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| sxh | number | 顺序号 |     |
| jylb | string | 交易类别 |     |
| jye | number | 交易额 |     |
| jysj | date | 交易时间 |     |
| jydd | number | 交易地点 |     |

## 新增修改或者注销医院科室

**接口名称：add_dept**

**接口作用:** HIS系统新增修改或者注销一个医院科室，同时调用该服务存入地纬结算系统数据库，如果该科室已经存在则视为更新。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ksbm | string | \*科室编码 |     |
| p_ksmc | string | \*科室名称 |     |
| p_zxrq | date | 注销日期 |     |
| p_zxbz | string | 注销标志 | 1：已注销；0：正常；不传默认为0：正常，可调用数据字典接口获取，代码编号：ZXBZ |
| p_kslb | string | \*科室类别 | 可调用数据字典接口获取，代码编号：CATY |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败； |

## 加密机认证（无PSAM卡）

**接口名称：**hsm_rz

**接口作用：**无PSAM卡的读卡器或pos机获取卡片信息的认证

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_lx | string | \*业务类型 | 01为基于加密机的读基本信息 ；02基于加密机的通用读卡/写卡； |
| p_rzxx | stting | \*认证信息 | 每种业务类型稍有差异，在下面做详细解释 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultCode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| resulttext | string | 调用结果说明 |     |
| jsjg | String | 计算结果 | 加密机返回的计算信息 |

**业务类型01：**

**调用事例：**

**入参：**

{"p_lx":"01","p_rzxx":"01|370001|0081544B31869B370000067046|01|370001D156000005000412DD7BD172E2|3688D8D42C5D94D3|57CE879B10C47FAE|||"}

**出参：**

{"jsjg":"3ff20b551f828aa57CE879B10C47FAE||","resultcode":0,"resulttext":"调用成功"}

**对入参p_rzxx参数说明：**

业务编号|iReadCardBas_HSM_Step1（此方法是人社38号文基于加密机的读基本信息）的出参pOutInfo。

出参pOutInfo 一代卡二代卡(外部认证过程因子、外部认证鉴别所需的原始信息都为空)：

01|370001|0081544B31869B370000067046|01|370001D156000005000412DD7BD172E2|3688D8D42C5D94D3|57CE879B10C47FAE|||

出参pOutInfo 三代卡：

01|370001|00814A444686603700000323D8|03|370001D1560000050004A06E0BAF274D|3C5CA4D179AA3C00|7D79B86BCFC3174C|9B040E163D209F8C|0C70EA1AE04B7500|

**详细说明：**

业务编号：01

出参pOutInfo:依次为：发卡地区行政区划代码(卡识别码前6位)、卡复位信息(仅取历史字节)、算法标识、卡识别码、内部认证过程因子、内部认证鉴别所需的原始信息、外部认证过程因子、外部认证鉴别所需的原始信息，其中外部认证相关数据项全部不为空或全部为空。**各数据项之间以“|”分割，且最后一个数据项以“|”结尾。**

**对出参jsjg参数说明：**

内部认证鉴别数据|外部认证鉴别数据|

一代卡、二代卡（外部认证数据为空）：

C5B2B30B4C4ED2480123456789111111||

三代卡：

C5B2B30B4C4ED2480123456789111111|AF321F804C4CA2930123456789111111|

**业务类型02：**

**调用事例：**

**入参：**

{"p_lx":"02","p_rzxx":"02|370001|0081544B31869B370000067046|01|004C|3688D8D42C5D94D3|57CE879B10C47FAE|"}

**出参：**

{"jsjg":"037fcccc0f4c37d557CE879B10C47FAE|","resultcode":0,"resulttext":"调用成功"}

**对入参p_rzxx参数说明：**

业务编号|发卡地行政区划代码|卡复位信息|iReadCard_HSM_Step1/ iWriteCard_HSM_Step1此方法是人社38号文“基于加密机的通用读卡/写卡(步骤一)”的出参pOutInfo。

出参pOutInfo(读卡/写卡)：

02|370001|0081544B31869B370000067046|01|004C|3688D8D42C5D94D3|57CE879B10C47FAE|

02|370001|0081544B31869B370000067046|03|RKMFEF0C_370000|3688D8D42C5D94D3|57CE879B10C47FAE|

**详细说明：**

业务编号：02

发卡地行政区划代码：iReadCardBas_HSM_Step1中的出参

卡复位信息：iReadCardBas_HSM_Step1中的出参

出参pOutInfo:依次为：算法标识、外部认证密钥地址、外部认证过程因子、外部认证鉴别所需的原始信息。**各数据项之间以“|”分割，且最后一个数据项以“|”结尾。**

**对出参jsjg参数说明：**

外部认证鉴别数据外部认证鉴别所需的原始信息|

9446307CD2E03B7257CE879B10C47FAE|

## 查询生育备案信息（威海）

**接口名称：**query_sybaxx

**接口作用：**查询生育备案信息

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_grbh | string | \*个人编号 | 社会保障号码或者身份证号； |
| p_jbbm | stting | \*疾病编码 | 病人确诊的疾病编码 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 标识名 | 类型  | 中文名称 | 说明  |
| resultCode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| sydjh | string | 生育登记号 |     |
| sytc | String | 生育胎次 |     |
| ycq | date | 预产期 | 或者为流产日期。 |
| hysj | date | 怀孕时间 |     |
| barq | date | 备案日期 |     |
| lxdh | string | 联系电话 |     |
| syzh | string | 生育证号 |     |

## 查询门诊大病用药范围（聊城）

**接口名称：**query_mzdbyyfw

**接口作用：**查询门诊大病用药范围

**接口类型：**查询类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_grbh | string | \*个人编号 | 社会保障号码或者身份证号； |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| mzdbyyfw_ds | 数据集 | 门诊用药大病范围信息 |     |

mzdbyyfw_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| xm  | string | 姓名  |     |
| yltcdjh | string | 医疗统筹登记号 |     |
| jbbm | string | 疾病编码 |     |
| jbmc | date | 疾病名称 |     |
| yyxmbm | string | 药品编码 |     |
| yyxmmc | string | 药品名称 |     |
| ylxmbm | string | 中心项目编码 |     |
| ylxmmc | string | 中心项目名称 |     |
| bayybm | string | 备案医院编码 |     |
| bayymc | string | 备案医院名称 |     |

## 保存MIS交易信息（实账户地区）

**接口名称：save_misinfo**

**接口作用:** 实账户地区门诊、住院结算后个人账户支付信息保存，用于交易时地纬保存失败、自助机消费个人账户信息调用

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ybjyh | string | \*医保交易号 | 门诊、住院结算时产生返回的jshid |
| p_kh | string | **\***卡号 | 正常卡号； |
| p_jyje | string | \*交易金额 | 个人账户消费金额 |
| p_lsh | string | \*流水号 | 银联交易流水号 |
| p_jylx | string | \*交易类型 | 交易类型（'01'消费'03'退货'） |
| p_jyrq | string | \*交易日期 | 交易日期 yyyymmdd |
| p_jysj | string | \*交易时间 | 交易时间 hhmmss |
| p_jyckh | string | \*交易参考号 |     |
| p_shh | string | \*商户号 |     |
| p_zdh | string | \*终端号 |     |
| p_pch | string | \*批次号 |     |
| p_klx | string | 卡类型 |     |
| p_fkh | string | 发卡行 | 发卡行中文名，银联返回发卡行行号 |
| p_cwsm | string | 错误说明 | 错误说明 |
| p_cgbz | String | \*成功标志 | 调用mis交易是否成功：0成功，1不成功（默认只保存成功的交易） |
| p_yjyckh | String | 原交易参考号 |     |
| p_ylsh | string | 原交易流水号 |     |
| p_yshh | String | 原交易商户号 |     |
| p_yzdh | String | 原交易终端号 |     |
| p_yjyje | string | 原交易金额 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败。 |

## 查询病人费用明细

**接口名称：**query_brfy

**接口作用:** 查询病人费用明细，根据传入的病历号和住院流水号返回这些天的住院费用总额和费用明细。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_blh | string | \*病历号 |     |
| p_zylsh | string | \*住院流水号 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| brfy_ds | 数据集 | 在院病人费用 |     |
| zje | number | 总金额 |     |
| ybfdje | number | 医保负担金额 |     |
| yydfje | number | 医院负担金额 |     |
| brfdje | number | 病人负担金额 |     |
| grzhzf | number | 个人账户支付 |     |
| yljmje | number | 医疗减免金额 |     |
| ylbzje | number | 医疗补助金额 |     |
| brjsrq | date | 病人结算日期 |     |
| yltclb | string | 医疗统筹类别 |     |

brfy_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| zje | number | 总金额 |     |
| yyxmbm | string | 医院项目编码 |     |
| yyxmmc | string | 医院项目名称 |     |
| dj  | number | 单价  |     |
| gg  | string | 规格  |     |
| dw  | string | 单位  |     |
| sl  | number | 数量  |     |
| qetc | number | 全额统筹 |     |
| bftc | number | 部分统筹 |     |
| qezf | number | 全额自负 |     |
| bfzf | number | 部分自负 |     |
| cgxjje | number | 超过限价金额 |     |
| sxzfbl | number | 首先自付比例 |     |
| sxzfje | number | 首先自付金额 |     |
| pdfssj | date | 凭单发生时间 |     |
| fyfssj | date | 费用发生时间 |     |

## 融合支付

### 下单

**接口名称：**create_order

**接口作用**：创建订单

**接口类型**：交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_zje | number | \*交易总金额 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ptddh | string | 平台订单号 |     |
| url | string | 支付页面url | 由调用方打开该地址 |

### 查询订单

**接口名称：**query_order

**接口作用:**

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ptddh | string | \*平台订单号 | 下单时返回的平台订单号 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| skzt | string | 收款状态 | TRADE_NOT_EXIST:订单不存在<br><br>WAIT_TO_PAY:正在收款<br><br>TRADE_SUCCESS:收款成功<br><br>TRADE_FAILED: 收款失败 |
| zfxd_ds | 数据集 | 支付详单 |     |

zfxd_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| zffs | string | 支付方式 | XJZF：现金支付<br><br>ALIPAY：支付宝扫码付<br><br>WXPAY：微信扫码付<br><br>YLPAY：银联扫码付<br><br>POS：POS刷卡支付 |
| jyje | number | 交易金额 |     |
| jysj | string | 交易时间 |     |
| jyzt | string | 交易状态 | WAIT_TO_PAY:正在收款<br><br>TRADE_SUCCESS:收款成功<br><br>TRADE_FAILED:收款失败 |

### 撤销订单

**接口名称：**cancle_order

**接口作用**：

**接口类型**：交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ptddh | string | \*平台订单号 | 下单时返回的平台订单号 |
| p_zje | string | \*撤销交易总金额 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ptcxddh | string | 平台撤销订单号 |     |
| url | string | 撤销页面url | 由调用方打开该地址 |

接口说明：该接口会调出支付页面，请在输入框中扫描二维码进行支付；

### 查询撤销订单

**接口名称：**query_cancle_order

**接口作用:**

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ptcxddh | string | \*平台撤销订单号 | 撤销订单时返回的平台撤销订单号 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| tkzt | string | 退款状态 | TRADE_NOT_EXIST:订单不存在或未产生退款<br><br>WAIT_TO_REFUND:正在退款<br><br>TRADE_REFUND_SUCCESSED:退款成功 |
| tkxd_ds | 数据集 | 退款详单 |     |

tkxd_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| zffs | string | 支付方式 | XJZF：现金支付<br><br>ALIPAY：支付宝扫码付<br><br>WXPAY：微信扫码付<br><br>YLPAY：银联扫码付<br><br>POS：POS刷卡支付 |
| jyje | number | 交易金额 |     |
| jysj | string | 交易时间 |     |
| jyzt | string | 交易状态 | WAIT_TO_REFUND:正在退款<br><br>TRADE_REFUND_SUCCESSED:退款成功<br><br>TRADE_REFUND_FAILED:退款失败 |

### 对账

**接口名称：**query_rhdz

**接口作用:**

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_rq | date | \*日期 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| zf_ds | 数据集 | 支付结果数据集 |     |
| tf_ds | 数据集 | 退费结果数据集 |     |

zf_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ptddh | string | 平台订单号 |     |
| zje | number | 交易金额 |     |
| jysj | string | 交易时间 |     |
| zffs | string | 支付方式 | XJZF：现金支付<br><br>ALIPAY：支付宝扫码付<br><br>WXPAY：微信扫码付<br><br>YLPAY：银联扫码付<br><br>POS：POS刷卡支付 |

tf_ds为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ptcxddh | string | 平台撤销订单号 |     |
| zje | number | 交易金额 |     |
| jysj | string | 交易时间 |     |
| zffs | string | 支付方式 | XJZF：现金支付<br><br>ALIPAY：支付宝扫码付<br><br>WXPAY：微信扫码付<br><br>YLPAY：银联扫码付<br><br>POS：POS刷卡支付 |

## 查询参保人就医信息

**接口名称：**query_cbrjyxx

**接口作用:** 查询参保人在某个时间段的就医信息。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_grbh | string | \*个人编号 |     |
| p_qsrq | date | \*起始日期 |     |
| p_zzrq | date | \*终止日期 |     |
| p_xm | string | 姓名  |     |
| p_yltclb | stiring | 医疗统筹类别 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| jyxx_ds | 数据集 | 就医信息 |     |

jyxx_ds 为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| yybm | string | 医院编码 |     |
| yymc | string | 医院名称 |     |
| jbbm | string | 疾病编码 |     |
| yltclb | string | 医疗统筹类别 | 具体值调用数据字典接口获取，代码编号：YLTCLB |
| yyxmbm | string | 医院项目编码 |     |
| yyxmmc | string | 医院项目名称 |     |
| ylxmbm | string | 医疗项目编码 |     |
| ylxmmc | string | 医疗项目名称 |     |
| jsxmbh | string | 结算项目编码 |     |
| jsxmmc | string | 结算项目名称 |     |
| dw  | string | 单位  |     |
| jxm | string | 剂型名 | 具体值调用数据字典接口获取，代码编号：JXMC |
| spm | string | 商品名 |     |
| gg  | string | 规格  |     |
| brjsrq | date | 病人结算日期 |     |
| dj  | number | 单价  |     |
| sl  | number | 数量  |     |
| zje | number | 总金额 |     |
| yyts | number | 用药天数 |     |
| ysxm | string | 医师姓名 |     |
| yysm | string | 用药说明 |     |
| jsdd | string | 结算地点 |     |
| brjsh | string | 病人结算号 |     |

## 出院补录出院诊断和临床路径

**接口名称：**add_cyxxbl

**接口作用:** 病人出院后，医院his把整理好已出院病人的出院诊断、主要诊疗 方式和临床路径，可批量传输到医保系统。

**接口类型：**交易类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_zylsh | string | \*住院流水号 | 病人的住院流水号（save_zydj返回的zylsh） |
| p_cyzd | string | \*出院诊断 | 传入医保系统中存在的疾病编码 |
| p_ssbm | string | 主要诊疗方式 | 手术编码和出院诊断是有对应关系的，根据上传的出院诊断传手术编码，具体的对应关系会在定额疾病目录下载中有说明 |
| p_lclj | string | 临床路径 | 临床路径和出院诊断是有对应关系的，根据上传的出院诊断传临床路径，具体的对应关系会在定额疾病目录下载中有说明 |
| p_sszlsm | string | 手术治疗说明 | 当上传的ssbm=’QT’的时候，该值不允许为空。 |
| p_dscbz | string | 单双侧标志 | 1:单侧；2：双侧;不区分可以不传 |

返回结果集：无

## 查询慢病病人就医记录接口

**接口名称：**query_bryyqk

**接口作用:** 查询慢病病人的用药记录。

**接口类型：**查询类

参数说明:

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_grbh | string | \*个人编号 |     |
| p_cxlb | string | \*查询类别 | 0:只查询上一次 1:按日期查询，不传默认为0 |
| p_qsrq | date | 起始日期 | 查询类别传1时必传 |
| p_zzrq | date | 终止日期 | 查询类别传1时必传 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| dssick | 数据集 | 疾病信息 |     |
| dsinfo | 数据集 | 详情信息 |     |

dssick为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| jbbm | string | 疾病编码 |     |
| jbmc | string | 疾病名称 |     |
| ejjbmc | string | 疾病细类名称 | 淄博使用 |

dsinfo为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| yymc | string | 医院名称 |     |
| yybm | string | 医院编码 |     |
| sl  | string | 数量  |     |
| jsrq | date | 结算日期 |     |
| ylxmbm | string | 医疗项目编码 |     |
| ylxmmc | string | 医疗项目名称 |     |
| jxm | string | 剂型名 |     |
| gg  | string | 规格  |     |

## 查询医疗政策标识

**接口名称：**query_ylzcbs

**接口作用:** 查询医疗政策标识。

**接口类型：**查询类

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_ylzcbs | string | 医疗政策标识 | 可传入部分字符进行模糊查询或传空查询全部 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |
| ylzcbs_ds | 数据集 | 医疗政策标识 |     |

**ylzcbs_ds**为数据集，其中包括返回的参数:

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| ylzcbs | string | 医疗政策标识 |     |
| ylzcbsmc | string | 医疗政策标识名称 |     |

## 移动支付2.0门诊结算推送至诊间

**接口名称：settle_mz_xstz**

**接口作用：**移动支付2.0门诊结算推送至诊间系统，方便从诊间查询数据

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*结算号 | 移动支付中心（2207）门诊结算接口返回的医药机构结算 ID（medins_setl_i<br><br>d） |
| p_grbh | string | \*个人编号 | 身份证号 |
| p_zje | string | \*总金额 | 本次结算总金额 |
| p_jbbm | string | \*疾病编码 | 本次结算使用疾病 |
| p_ksbm | string | 科室编码 |     |
| p_ysbm | string | 医师编码 |     |
| p_rqlb | string | \*人群类别 | A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB |
| p_dwmc | string | 单位名称 |     |
| p_xm | string | \*姓名 |     |
| p_xb | string | \*性别 |     |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |

## 移动支付2.0门诊结算撤销推送至诊间

**接口名称：destroy_mz_xstz**

**接口作用：**移动支付2.0门诊结算撤销推送至诊间系统，方便从诊间查询数据

**接口类型：**交易类

**参数说明：**

**传入参数：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| p_jshid | string | \*结算号 | 结算时传入的结算号 |
| p_tfsj | date | \*退费时间 | 精确到秒 |

**返回结果集：**

|     |     |     |     |
| --- | --- | --- | --- |
| 参数名称 | 类型  | 中文名称 | 说明  |
| resultcode | string | 执行代码 | 0为成功，其他为失败；执行成功时才返回下面的数据。 |

# 开发建议

在新的接口文档中，与原接口的主要变化如下：

1.  在3.3.1门诊预结算服务中，当调用门诊预结算服务时，结算平台返回结果后，需要HIS开发界面，显示预结算信息(界面如下图)。

# 异常处理

## 异常处理方式

- 1.  **通用询问服务：ask_for_si**

ask_for_si服务仅用来处理服务调用异常。只有在his调用结算平台的服务却没有收到明确的返回结果（比如：调用超时），为了确认医保中心服务是否调用成功的情况下，才需要调用询问服务。若服务返回结果集正常，无需调用询问服务。

两种服务调用异常情况：

1、没有收到平台返回的json串

2、已收到平台返回的json串，但是resultCode=-6

调用询问服务后，如果上次服务调用成功， 平台会返回原服务所有返回结果集.此时success_flag是1，his不需要处理异常。如果上次服务调用失败，此时success_flag是0，这说明上次医保服务调用失败。建议his回滚本次事务或者重新发起一次新的服务调用（使用新的hisjyh）。

例如，调用ask_for_si服务询问结算服务时，若返回值中success_flag = 1，这说明上次医保结算已经成功，则平台将返回结算结果集，his可以根据具体情况来处理结算结果。若返回值中success_flag = 0，说明医保结算失败，his可以发起新的结算服务调用。