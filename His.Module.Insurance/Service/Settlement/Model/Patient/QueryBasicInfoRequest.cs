using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Patient;

/// <summary>
/// 查询个人基本信息的请求类
/// </summary>
public class QueryBasicInfoRequest:PatientBaseSettlementDto
{
    /// <summary>
    /// *个人编号（社会保障号码或者身份证号）
    /// </summary>
    public string p_grbh { get; set; }

    /// <summary>
    /// *险种标志（具体值调用数据字典接口获取，代码编号：XZBZ）
    /// </summary>
    public string p_xzbz { get; set; }

    /// <summary>
    /// 姓名（该姓名必须和医保数据库中一致）
    /// </summary>
    public string p_xm { get; set; }

    /// <summary>
    /// 医疗统筹类别（0为仅获取人员基本信息，1为住院，4为门诊大病(特病)，6为普通门诊，不传时，默认值为0,其他具体值调用数据字典接口获取，代码编号：YLTCLB）
    /// </summary>
    public string p_yltclb { get; set; }

    /// <summary>
    /// 日期（住院日期或费用发生时间）
    /// </summary>
    public string p_rq { get; set; }
}