using His.Module.Insurance.Service.Settlement.Dto;

namespace His.Module.Insurance.Service.Settlement.Model.Patient;

public class ReadCardResponse
 : BaseSettlementResponse
{
    /// <summary>
    /// 个人编号（原字段名：grbh）
    /// </summary>
    public string grbh { get; set; }

    /// <summary>
    /// 卡号（阳煤地区该字段为空，用二维码读卡时该字段为空）（原字段名：kh）
    /// </summary>
    public string kh { get; set; }

    /// <summary>
    /// 人群类别（A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB）（原字段名：rqlb）
    /// </summary>
    public string rqlb { get; set; }

    /// <summary>
    /// 疾病编码（格式：疾病名称1#m编码1/疾病名称2#m编码2/...）（原字段名：mzdbjbs）
    /// </summary>
    public string mzdbjbs { get; set; }

    /// <summary>
    /// 社保机构编号（原字段名：sbjgbh）
    /// </summary>
    public string sbjgbh { get; set; }

    /// <summary>
    /// 身份证号码（原字段名：sfzhm）
    /// </summary>
    public string sfzhm { get; set; }

    /// <summary>
    /// 姓名（原字段名：xm）
    /// </summary>
    public string xm { get; set; }

    /// <summary>
    /// 账户余额（原字段名：zhye）
    /// </summary>
    public double zhye { get; set; }

    /// <summary>
    /// 灰名单标志（0代表灰名单，1白名单）（原字段名：zfbz）
    /// </summary>
    public string zfbz { get; set; }

    /// <summary>
    /// 灰名单原因（如果是白名单该值为空）（原字段名：zfsm）
    /// </summary>
    public string zfsm { get; set; }

    /// <summary>
    /// 医疗人员类别（汉字）（原字段名：ylrylb）
    /// </summary>
    public string ylrylb { get; set; }

    /// <summary>
    /// 性别（1:男,2:女,9:不确定，可调用数据字典接口获取，代码编号：XB）（原字段名：xb）
    /// </summary>
    public string xb { get; set; }

    /// <summary>
    /// 转出医院名称（如果zcyymc不为''和'*'，则表示本次住院时候市内转院来的）（原字段名：zcyymc）
    /// </summary>
    public string zcyymc { get; set; }

    /// <summary>
    /// 异地标志（1:是,0:否）（原字段名：ydbz）
    /// </summary>
    public string ydbz { get; set; }

    /// <summary>
    /// 是否享受普通门诊统筹（如皋专用）（原字段名：sfxsptmztc）
    /// </summary>
    public string sfxsptmztc { get; set; }

    /// <summary>
    /// 转出出院日期（原字段名：zccyrq）
    /// </summary>
    public string zccyrq { get; set; }

    /// <summary>
    /// 补充人员类别（济南返回代码编号，H3地区返回具体汉字说明）（原字段名：bcrylb）
    /// </summary>
    public string bcrylb { get; set; }

    /// <summary>
    /// 出生日期（原字段名：csrq）
    /// </summary>
    public string csrq { get; set; }

    /// <summary>
    /// 单位名称（原字段名：dwmc）
    /// </summary>
    public string dwmc { get; set; }

    /// <summary>
    /// 门诊定点标志（值为1时，当前医院是该参保人的门诊统筹定点医院。（异地不返回））（原字段名：mzddbz）
    /// </summary>
    public string mzddbz { get; set; }

    /// <summary>
    /// 门诊定点说明（参保人的门诊统筹定点医院说明（异地不返回））（原字段名：mzddsm）
    /// </summary>
    public string mzddsm { get; set; }

    /// <summary>
    /// 门诊大病备注（yltclb=‘4’时，不为空）（原字段名：mzdbbz）
    /// </summary>
    public string mzdbbz { get; set; }

    /// <summary>
    /// 优抚对象标志（济南专用。’1’为优抚对象）（原字段名：yfdxbz）
    /// </summary>
    public string yfdxbz { get; set; }

    /// <summary>
    /// 优抚对象人员类别（济南专用。(汉字说明)）（原字段名：yfdxlb）
    /// </summary>
    public string yfdxlb { get; set; }

    /// <summary>
    /// 医疗人员类别代码（济南专用）（原字段名：ylrylbcode）
    /// </summary>
    public string ylrylbcode { get; set; }

    /// <summary>
    /// 保险编号（磁卡正面的保险编号。阳煤专用）（原字段名：ybbh）
    /// </summary>
    public string ybbh { get; set; }

    /// <summary>
    /// 统计人员类别（与统计人员类别明细字段共同区分人员类别。阳煤专用）（原字段名：tjrylb）
    /// </summary>
    public string tjrylb { get; set; }

    /// <summary>
    /// 统计人员类别明细（与统计人员类别字段共同区分人员类别。阳煤专用）（原字段名：tjrylbmx）
    /// </summary>
    public string tjrylbmx { get; set; }

    /// <summary>
    /// 普通门诊慢性病备案疾病（威海专用：医疗统筹类别为6时，返回普通门诊慢性病备案疾病）（原字段名：ptmzjbs）
    /// </summary>
    public string ptmzjbs { get; set; }

    /// <summary>
    /// 救助人员类别（具体值调用数据字典接口获取，代码编号：JZRYLB）（原字段名：jzrylb）
    /// </summary>
    public string jzrylb { get; set; }

    /// <summary>
    /// 贫困人口标志（1：贫困人口，0：非贫困人口）（原字段名：pkrkbz）
    /// </summary>
    public string pkrkbz { get; set; }

    /// <summary>
    /// 单位性质名称（原字段名：dwxzmc）
    /// </summary>
    public string dwxzmc { get; set; }

    /// <summary>
    /// 有无15(医保参数控制)天内的住院记录（1:有，0 :无）（原字段名：zhzybz）
    /// </summary>
    public string zhzybz { get; set; }

    /// <summary>
    /// 15(医保参数控制)天内的住院记录说明（原字段名：zhzysm）
    /// </summary>
    public string zhzysm { get; set; }

    /// <summary>
    /// psam卡号（忻州省内异地使用）（原字段名：psamkh）
    /// </summary>
    public string psamkh { get; set; }

    /// <summary>
    /// 行政区划（山东省内异地刷卡使用，具体值调用数据字典接口获取，代码编号：YDXZQH）（原字段名：xzqh）
    /// </summary>
    public string xzqh { get; set; }

    /// <summary>
    /// 社保卡异地标志（山东省内异地刷卡使用，1：是，0：否）（原字段名：sbkydbz）
    /// </summary>
    public string sbkydbz { get; set; }

    /// <summary>
    /// 卡芯片账号（原字段名：kyhzh）
    /// </summary>
    public string kyhzh { get; set; }

    /// <summary>
    /// 居民两病备案疾病编码（原字段名：lbjbbm）
    /// </summary>
    public string lbjbbm { get; set; }

    /// <summary>
    /// 令牌（电子医保凭证扫码返回）（原字段名：ectoken）
    /// </summary>
    public string ectoken { get; set; }

    /// <summary>
    /// 工伤检查信息（济南使用）（原字段名：gsjcxx）
    /// </summary>
    public string gsjcxx { get; set; }

    /// <summary>
    /// 识别码（跨省异地返回）（原字段名：sbm）
    /// </summary>
    public string sbm { get; set; }

    /// <summary>
    /// 参保地市编号（省异地用）（原字段名：cbdsbh）
    /// </summary>
    public string cbdsbh { get; set; }

    /// <summary>
    /// 参保机构名称（省异地用）（原字段名：cbjgmc）
    /// </summary>
    public string cbjgmc { get; set; }

    /// <summary>
    /// 门慢的二级代码（聊城使用）（原字段名：mzmxm_ejjbbm）
    /// </summary>
    public string mzmxm_ejjbbm { get; set; }

    /// <summary>
    /// 门慢的二级名称（聊城使用）（原字段名：mzmxm_ejjbmc）
    /// </summary>
    public string mzmxm_ejjbmc { get; set; }

    /// <summary>
    /// 共济人参保地编码（有共济绑定关系时返回，具体值调用数据字典接口获取，代码编号：DSBM）（原字段名：gjcbdbm）
    /// </summary>
    public string gjcbdbm { get; set; }

    /// <summary>
    /// 共济账户余额（有共济绑定关系时返回）（原字段名：gjzhye）
    /// </summary>
    public double gjzhye { get; set; }

    /// <summary>
    /// 人员id（原字段名：ryid）
    /// </summary>
    public string ryid { get; set; }
}
