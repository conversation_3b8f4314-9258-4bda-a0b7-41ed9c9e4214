using His.Module.Insurance.Const;
using His.Module.Insurance.Entity;
using His.Module.Insurance.Service.CatalogManagement.Dto;
using His.Module.Insurance.Service.CatalogManagement.Interface;
namespace His.Module.Insurance.Service.CatalogManagement;

/// <summary>
/// 目录管理服务
/// </summary>
[ApiDescriptionSettings(InsuranceConst.GroupName, Order = 100)]
public class CatalogManagementService(
    ICatalogManagementApi catalogApi,
    SqlSugarRepository<HospitalItemCatalog> hospitalItemRep,
    SqlSugarRepository<InsuranceItemCatalog> insuranceItemRep,
    SqlSugarRepository<InsuranceDictionary> dictionaryRep,
    SqlSugarRepository<InsuranceSickCatalog> sickCatalogRep,
    SqlSugarRepository<InsuranceOperationCatalog> operationCatalogRep,
    SqlSugarRepository<DoctorInfo> doctorInfoRep,
    SqlSugarRepository<LimitPriceInfo> limitPriceRep,
    SqlSugarRepository<FirstSelfPayRatioInfo> firstSelfPayRatioRep)
    : IDynamicApiController, ITransient
{

    #region 自付比例管理

    /// <summary>
    /// 获取医院项目自付比例
    /// </summary>
    [ApiDescriptionSettings(Name = "GetItemSelfPayRatio"), HttpPost]
    [DisplayName("获取医院项目自付比例")]
    public async Task<GetZfblResponse> GetItemSelfPayRatio(GetZfblRequest request)
    {
        return await catalogApi.GetZfbl(request);
    }

    #endregion

    #region 医院项目目录管理

    /// <summary>
    /// 同步医院项目目录
    /// </summary>
    [ApiDescriptionSettings(Name = "SyncHospitalItems"), HttpPost]
    [DisplayName("同步医院项目目录")]
    public async Task<string> SyncHospitalItems(QueryYyxmInfoRequest request)
    {
        var response = await catalogApi.QueryYyxmInfo(request);
        return $"同步完成，共 {response.YyxmDs.Count} 条记录";
    }

    /// <summary>
    /// 增量同步医院项目目录
    /// </summary>
    [ApiDescriptionSettings(Name = "IncrementalSyncHospitalItems"), HttpPost]
    [DisplayName("增量同步医院项目目录")]
    public async Task<string> IncrementalSyncHospitalItems()
    {
        var request = new QueryYyxmInfoBySxhRequest
        {
            p_filetype = "json"
        };
        var response = await catalogApi.QueryYyxmInfoBySxh(request);

        return $"增量同步完成，共 {response.yyxm_ds.Count} 条记录，" +
               $"是否还有更多记录：{(response.sfjxxz == "1" ? "是" : "否")}";
    }

    /// <summary>
    /// 完整增量同步医院项目目录（循环直到全部同步完成）
    /// </summary>
    [ApiDescriptionSettings(Name = "FullIncrementalSyncHospitalItems"), HttpPost]
    [DisplayName("完整增量同步医院项目目录")]
    public async Task<string> FullIncrementalSyncHospitalItems()
    {
        var totalCount = 0;
        var batchCount = 0;
        var hasMore = true;

        while (hasMore)
        {
            var request = new QueryYyxmInfoBySxhRequest
            {
                p_filetype = "json"
            };
            var response = await catalogApi.QueryYyxmInfoBySxh(request);

            totalCount += response.yyxm_ds.Count;
            batchCount++;

            hasMore = response.sfjxxz == "1" && response.yyxm_ds.Count > 0;

            // 防止无限循环
            if (batchCount > 100) break;
        }

        return $"完整增量同步完成，共执行 {batchCount} 批次，同步 {totalCount} 条记录";
    }

    /// <summary>
    /// 新增医院项目
    /// </summary>
    [ApiDescriptionSettings(Name = "AddHospitalItem"), HttpPost]
    [DisplayName("新增医院项目")]
    public async Task<AddYyxmResponse> AddHospitalItem(AddYyxmRequest request)
    {
        return await catalogApi.AddYyxm(request);
    }

    /// <summary>
    /// 获取本地医院项目目录
    /// </summary>
    [ApiDescriptionSettings(Name = "GetLocalHospitalItems"), HttpGet]
    [DisplayName("获取本地医院项目目录")]
    public async Task<SqlSugarPagedList<HospitalItemCatalog>> GetLocalHospitalItems(
        [FromQuery] HospitalItemQueryInput input)
    {
        return await hospitalItemRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                x => x.Yyxmbm.Contains(input.Keyword) ||
                     x.Yyxmmc.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.YpBz), x => x.Ypbz == input.YpBz)
            .OrderBy(x => x.Yyxmbm)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 根据编码获取医院项目
    /// </summary>
    [ApiDescriptionSettings(Name = "GetHospitalItemByCode"), HttpGet]
    [DisplayName("根据编码获取医院项目")]
    public async Task<HospitalItemCatalog?> GetHospitalItemByCode(string yyxmBm)
    {
        return await hospitalItemRep.GetFirstAsync(x => x.Yyxmbm == yyxmBm);
    }

    #endregion

    #region 医保项目目录管理

    /// <summary>
    /// 同步医保项目目录
    /// </summary>
    [ApiDescriptionSettings(Name = "SyncInsuranceItems"), HttpPost]
    [DisplayName("同步医保项目目录")]
    public async Task<string> SyncInsuranceItems()
    {
        var request = new QueryMlRequest
        {
            p_filetype = "json"
        };
        var response = await catalogApi.QueryMl(request);
        return $"同步完成，共 {response.ylxm_ds.Count} 条记录";
    }

    /// <summary>
    /// 增量同步医保项目目录
    /// </summary>
    [ApiDescriptionSettings(Name = "IncrementalSyncInsuranceItems"), HttpPost]
    [DisplayName("增量同步医保项目目录")]
    public async Task<string> IncrementalSyncInsuranceItems()
    {
        var request = new QueryMlBySxhRequest
        {
            p_filetype = "json"
        };
        var response = await catalogApi.QueryMlBySxh(request);

        return $"增量同步完成，共 {response.ylxm_ds.Count} 条记录，" +
               $"是否还有更多记录：{(response.sfjxxz == "1" ? "是" : "否")}";
    }

    /// <summary>
    /// 获取本地医保项目目录
    /// </summary>
    [ApiDescriptionSettings(Name = "GetLocalInsuranceItems"), HttpGet]
    [DisplayName("获取本地医保项目目录")]
    public async Task<SqlSugarPagedList<InsuranceItemCatalog>> GetLocalInsuranceItems(
        [FromQuery] InsuranceItemQueryInput input)
    {
        return await insuranceItemRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                x => x.YlxmBm.Contains(input.Keyword) ||
                     x.YlxmBzmc.Contains(input.Keyword) ||
                     x.Py != null && x.Py.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.YpBz), x => x.YpBz == input.YpBz)
            .WhereIF(!string.IsNullOrWhiteSpace(input.ZxBz), x => x.ZxBz == input.ZxBz)
            .OrderBy(x => x.YlxmBm)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    #endregion

    #region 疾病目录管理

    /// <summary>
    /// 同步疾病目录
    /// </summary>
    [ApiDescriptionSettings(Name = "SyncSickCatalog"), HttpPost]
    [DisplayName("同步疾病目录")]
    public async Task<string> SyncSickCatalog()
    {
        var request = new QuerySiSickRequest
        {
            FileType = "json"
        };
        var response = await catalogApi.QuerySiSick(request);
        return $"同步完成，共 {response.YbjbDs.Count} 条记录";
    }

    /// <summary>
    /// 增量同步疾病目录
    /// </summary>
    [ApiDescriptionSettings(Name = "IncrementalSyncSickCatalog"), HttpPost]
    [DisplayName("增量同步疾病目录")]
    public async Task<string> IncrementalSyncSickCatalog()
    {
        var request = new QuerySiSickBySxhRequest
        {
            FileType = "json"
        };
        var response = await catalogApi.QuerySiSickBySxh(request);

        return $"增量同步完成，共 {response.YbjbDs.Count} 条记录，" +
               $"是否还有更多记录：{(response.SfjXxz == "1" ? "是" : "否")}";
    }

    /// <summary>
    /// 获取本地疾病目录
    /// </summary>
    [ApiDescriptionSettings(Name = "GetLocalSickCatalog"), HttpGet]
    [DisplayName("获取本地疾病目录")]
    public async Task<SqlSugarPagedList<InsuranceSickCatalog>> GetLocalSickCatalog(
        [FromQuery] SickCatalogQueryInput input)
    {
        return await sickCatalogRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                x => x.JbBm.Contains(input.Keyword) ||
                     x.JbMc.Contains(input.Keyword) ||
                     x.Py != null && x.Py.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.MzdbLb), x => x.MzdbLb == input.MzdbLb)
            .OrderBy(x => x.JbBm)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    #endregion

    #region 手术目录管理

    /// <summary>
    /// 同步手术目录
    /// </summary>
    [ApiDescriptionSettings(Name = "SyncOperationCatalog"), HttpPost]
    [DisplayName("同步手术目录")]
    public async Task<string> SyncOperationCatalog()
    {
        var request = new QueryOperationRequest
        {
            FileType = "json"
        };
        var response = await catalogApi.QueryOperation(request);
        return $"同步完成，共 {response.SsxmDs.Count} 条记录";
    }

    /// <summary>
    /// 获取本地手术目录
    /// </summary>
    [ApiDescriptionSettings(Name = "GetLocalOperationCatalog"), HttpGet]
    [DisplayName("获取本地手术目录")]
    public async Task<SqlSugarPagedList<InsuranceOperationCatalog>> GetLocalOperationCatalog(
        [FromQuery] OperationCatalogQueryInput input)
    {
        return await operationCatalogRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                x => x.SsBm.Contains(input.Keyword) ||
                     x.SsMc.Contains(input.Keyword) ||
                     x.Py != null && x.Py.Contains(input.Keyword))
            .OrderBy(x => x.SsBm)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    #endregion

    #region 数据字典管理

    /// <summary>
    /// 同步数据字典
    /// </summary>
    [ApiDescriptionSettings(Name = "SyncDictionary"), HttpPost]
    [DisplayName("同步数据字典")]
    public async Task<string> SyncDictionary(string dmbh)
    {
        var request = new QuerySiCodeRequest
        {
            DmBh = dmbh
        };
        var response = await catalogApi.QuerySiCode(request);

        return $"同步数据字典 {dmbh} 完成，共 {response.CodeDs.Count} 条记录";
    }

    /// <summary>
    /// 批量同步常用数据字典
    /// </summary>
    [ApiDescriptionSettings(Name = "SyncCommonDictionaries"), HttpPost]
    [DisplayName("批量同步常用数据字典")]
    public async Task<string> SyncCommonDictionaries()
    {
        var commonCodes = new[]
        {
            "XZBZ",
            "YLTCLB",
            "RQLB",
            "YPBZ",
            "XB",
            "ZXBZ",
            "SPBZ"
        };
        var results = new List<string>();

        foreach (var code in commonCodes)
            try
            {
                var result = await SyncDictionary(code);
                results.Add(result);
            }
            catch (Exception ex)
            {
                results.Add($"同步 {code} 失败: {ex.Message}");
            }

        return string.Join("\n", results);
    }

    /// <summary>
    /// 获取数据字典
    /// </summary>
    [ApiDescriptionSettings(Name = "GetDictionary"), HttpGet]
    [DisplayName("获取数据字典")]
    public async Task<List<InsuranceDictionary>> GetDictionary(string dmbh)
    {
        return await dictionaryRep.AsQueryable()
            .Where(x => x.DmBh == dmbh)
            .OrderBy(x => x.Code)
            .ToListAsync();
    }

    /// <summary>
    /// 获取所有数据字典分类
    /// </summary>
    [ApiDescriptionSettings(Name = "GetDictionaryCategories"), HttpGet]
    [DisplayName("获取所有数据字典分类")]
    public async Task<List<string>> GetDictionaryCategories()
    {
        return await dictionaryRep.AsQueryable()
            .GroupBy(x => x.DmBh)
            .Select(x => x.DmBh)
            .ToListAsync();
    }

    /// <summary>
    /// 分页获取数据字典
    /// </summary>
    [ApiDescriptionSettings(Name = "GetDictionaryPaged"), HttpGet]
    [DisplayName("分页获取数据字典")]
    public async Task<SqlSugarPagedList<InsuranceDictionary>> GetDictionaryPaged(
        [FromQuery] DictionaryQueryInput input)
    {
        return await dictionaryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.DmBh), x => x.DmBh == input.DmBh)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                x => x.Code.Contains(input.Keyword) || x.Content.Contains(input.Keyword))
            .OrderBy(x => x.DmBh)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    #endregion

    #region 综合管理

    /// <summary>
    /// 同步所有目录数据
    /// </summary>
    [ApiDescriptionSettings(Name = "SyncAllCatalogs"), HttpPost]
    [DisplayName("同步所有目录数据")]
    public async Task<string> SyncAllCatalogs()
    {
        var results = new List<string>();

        try
        {
            // 1. 同步数据字典
            var dictResult = await SyncCommonDictionaries();
            results.Add($"数据字典同步结果：\n{dictResult}");

            // 2. 同步医院项目目录
            var hospitalResult = await SyncHospitalItems(new QueryYyxmInfoRequest
            {
                p_filetype = "json"
            });
            results.Add($"医院项目目录：{hospitalResult}");

            // 3. 同步医保项目目录
            var insuranceResult = await SyncInsuranceItems();
            results.Add($"医保项目目录：{insuranceResult}");

            // 4. 同步疾病目录
            var sickResult = await SyncSickCatalog();
            results.Add($"疾病目录：{sickResult}");

            // 5. 同步手术目录
            var operationResult = await SyncOperationCatalog();
            results.Add($"手术目录：{operationResult}");

            return string.Join("\n\n", results);
        }
        catch (Exception ex)
        {
            results.Add($"同步过程中发生异常：{ex.Message}");
            return string.Join("\n\n", results);
        }
    }

    /// <summary>
    /// 获取同步状态统计
    /// </summary>
    [ApiDescriptionSettings(Name = "GetSyncStatus"), HttpGet]
    [DisplayName("获取同步状态统计")]
    public async Task<CatalogSyncStatusDto> GetSyncStatus()
    {
        var hospitalCount = await hospitalItemRep.AsQueryable().CountAsync();
        var insuranceCount = await insuranceItemRep.AsQueryable().CountAsync();
        var sickCount = await sickCatalogRep.AsQueryable().CountAsync();
        var operationCount = await operationCatalogRep.AsQueryable().CountAsync();
        var dictionaryCount = await dictionaryRep.AsQueryable().CountAsync();

        var lastHospitalSync = await hospitalItemRep.AsQueryable()
            .MaxAsync(x => x.LastSyncTime);
        var lastInsuranceSync = await insuranceItemRep.AsQueryable()
            .MaxAsync(x => x.LastSyncTime);
        var lastSickSync = await sickCatalogRep.AsQueryable()
            .MaxAsync(x => x.LastSyncTime);
        var lastOperationSync = await operationCatalogRep.AsQueryable()
            .MaxAsync(x => x.LastSyncTime);
        var lastDictionarySync = await dictionaryRep.AsQueryable()
            .MaxAsync(x => x.LastSyncTime);

        return new CatalogSyncStatusDto
        {
            HospitalItemCount = hospitalCount,
            InsuranceItemCount = insuranceCount,
            SickCatalogCount = sickCount,
            OperationCatalogCount = operationCount,
            DictionaryCount = dictionaryCount,
            LastHospitalSync = lastHospitalSync,
            LastInsuranceSync = lastInsuranceSync,
            LastSickSync = lastSickSync,
            LastOperationSync = lastOperationSync,
            LastDictionarySync = lastDictionarySync
        };
    }

    #endregion

    #region 医师信息管理

    /// <summary>
    /// 新增或更新医师信息
    /// </summary>
    [ApiDescriptionSettings(Name = "AddDoctorInfo")][HttpPost]
    [DisplayName("新增或更新医师信息")]
    public async Task<AddYsResponse> AddDoctorInfo(AddYsRequest request)
    {
        return await catalogApi.AddYs(request);
    }

    /// <summary>
    /// 查询医院医师信息
    /// </summary>
    [ApiDescriptionSettings(Name = "QueryDoctorInfo")][HttpPost]
    [DisplayName("查询医院医师信息")]
    public async Task<QueryYsResponse> QueryDoctorInfo(QueryYsRequest request)
    {
        return await catalogApi.QueryYs(request);
    }

    /// <summary>
    /// 获取本地医师信息
    /// </summary>
    [ApiDescriptionSettings(Name = "GetLocalDoctorInfo")][HttpGet]
    [DisplayName("获取本地医师信息")]
    public async Task<SqlSugarPagedList<DoctorInfo>> GetLocalDoctorInfo(
        [FromQuery] DoctorQueryInput input)
    {
        return await doctorInfoRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                x => x.YsBm.Contains(input.Keyword) ||
                     x.YsXm.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.KsBm), x => x.KsBm == input.KsBm)
            .OrderBy(x => x.YsBm)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    #endregion

    #region 限价信息管理

    /// <summary>
    /// 获取本地限价信息
    /// </summary>
    [ApiDescriptionSettings(Name = "GetLocalLimitPriceInfo")][HttpGet]
    [DisplayName("获取本地限价信息")]
    public async Task<SqlSugarPagedList<LimitPriceInfo>> GetLocalLimitPriceInfo(
        [FromQuery] LimitPriceQueryInput input)
    {
        return await limitPriceRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.YlxmBm), x => x.YlxmBm == input.YlxmBm)
            .WhereIF(!string.IsNullOrWhiteSpace(input.RqLb), x => x.RqLb == input.RqLb)
            .OrderBy(x => x.YlxmBm)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    #endregion

    #region 首先自付比例信息管理

    /// <summary>
    /// 获取本地首先自付比例信息
    /// </summary>
    [ApiDescriptionSettings(Name = "GetLocalFirstSelfPayRatioInfo")][HttpGet]
    [DisplayName("获取本地首先自付比例信息")]
    public async Task<SqlSugarPagedList<FirstSelfPayRatioInfo>> GetLocalFirstSelfPayRatioInfo(
        [FromQuery] FirstSelfPayRatioQueryInput input)
    {
        return await firstSelfPayRatioRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.YlxmBm), x => x.YlxmBm == input.YlxmBm)
            .WhereIF(!string.IsNullOrWhiteSpace(input.RqLb), x => x.RqLb == input.RqLb)
            .OrderBy(x => x.YlxmBm)
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    #endregion
}
