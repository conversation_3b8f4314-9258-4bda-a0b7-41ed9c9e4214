using His.Module.Insurance.Entity;
using His.Module.Insurance.Service.CatalogManagement.Dto;
using His.Module.Insurance.Service.CatalogManagement.Interface;
using His.Module.Insurance.Service.Settlement.Dto;
using Microsoft.Extensions.Logging;
namespace His.Module.Insurance.Service.CatalogManagement;

/// <summary>
/// 目录管理API基础类
/// </summary>
public abstract class CatalogManagementApiBase(
    SqlSugarRepository<HospitalItemCatalog> hospitalItemRep,
    SqlSugarRepository<InsuranceItemCatalog> insuranceItemRep,
    SqlSugarRepository<InsuranceDictionary> dictionaryRep,
    SqlSugarRepository<InsuranceSickCatalog> sickCatalogRep,
    SqlSugarRepository<InsuranceOperationCatalog> operationCatalogRep,
    SqlSugarRepository<DoctorInfo> doctorInfoRep,
    SqlSugarRepository<LimitPriceInfo> limitPriceRep,
    SqlSugarRepository<FirstSelfPayRatioInfo> firstSelfPayRatioRep,
    ILoggerFactory loggerFactory)
    : ICatalogManagementApi
{
    protected readonly ILogger Logger = loggerFactory.CreateLogger(CommonConst.SysLogCategoryName);

    #region 抽象方法 - 由具体实现类提供

    /// <summary>
    /// 调用远程API接口
    /// </summary>
    protected abstract Task<T> CallRemoteApi<T>(string methodName, object requestData) where T : BaseSettlementResponse, new();

    #endregion

    #region 接口实现

    public virtual async Task<GetZfblResponse> GetZfbl(GetZfblRequest request)
    {
        try
        {
            var result = await CallRemoteApi<GetZfblResponse>("get_zfbl", request);
            Logger.LogInformation("获取自付比例成功，项目编码：{RequestYyxmBm}", request.p_yyxmbm);
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "获取自付比例异常，项目编码：{RequestYyxmBm}", request.p_yyxmbm);
            throw;
        }
    }

    public virtual async Task<QueryYyxmInfoResponse> QueryYyxmInfo(QueryYyxmInfoRequest request)
    {
        try
        {

            var result = await CallRemoteApi<QueryYyxmInfoResponse>("query_yyxm_info", request);

            if (result?.YyxmDs?.Any() == true)
            {
                await SaveHospitalItems(result.YyxmDs);
                Logger.LogInformation("查询医院项目目录成功，共 {YyxmDsCount} 条记录", result.YyxmDs.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "查询医院项目目录异常");
            throw;
        }
    }

    public virtual async Task<QueryYyxmInfoBySxhResponse> QueryYyxmInfoBySxh(QueryYyxmInfoBySxhRequest request)
    {
        try
        {
            // 如果没有指定序号，获取本地最大序号
            if (!request.p_sxh.HasValue)
                request.p_sxh = await hospitalItemRep.AsQueryable()
                    .MaxAsync(x => x.Sxh) ?? 0;


            var result = await CallRemoteApi<QueryYyxmInfoBySxhResponse>("query_yyxm_info_by_sxh", request);

            if (result?.yyxm_ds?.Any() == true)
            {
                await SaveHospitalItems(result.yyxm_ds);
                Logger.LogInformation("增量查询医院项目目录成功，共 {YyxmDsCount} 条记录", result.yyxm_ds.Count);
            }

            // 保存限价信息
            if (result?.xj_ds?.Any() == true)
            {
                await SaveLimitPriceItems(result.xj_ds);
                Logger.LogInformation("保存限价信息成功，共 {XjDsCount} 条记录", result.xj_ds.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "增量查询医院项目目录异常");
            throw;
        }
    }

    public virtual async Task<AddYyxmResponse> AddYyxm(AddYyxmRequest request)
    {
        try
        {
            var result = await CallRemoteApi<AddYyxmResponse>("add_yyxm_info_all", request);
            Logger.LogInformation("新增医院项目成功，项目编码：{RequestYyxmBm}", request.p_yyxmbm);
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "新增医院项目异常，项目编码：{RequestYyxmBm}", request.p_yyxmbm);
            throw;
        }
    }

    public virtual async Task<QueryMlResponse> QueryMl(QueryMlRequest request)
    {
        try
        {
            var result = await CallRemoteApi<QueryMlResponse>("query_ml", request);

            if (result?.ylxm_ds?.Any() == true)
            {
                await SaveInsuranceItems(result.ylxm_ds);
                Logger.LogInformation("查询医保目录成功，共 {YlxmDsCount} 条记录", result.ylxm_ds.Count);
            }

            // 保存首先自付比例信息
            if (result?.sxzfbl_ds?.Any() == true)
            {
                await SaveFirstSelfPayRatioItems(result.sxzfbl_ds);
                Logger.LogInformation("保存首先自付比例信息成功，共 {SxzfblDsCount} 条记录", result.sxzfbl_ds.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "查询医保目录异常");
            throw;
        }
    }



    public virtual async Task<QuerySiSickResponse> QuerySiSick(QuerySiSickRequest request)
    {
        try
        {
            var requestData = new
            {
                p_filetype = request.FileType
            };

            var result = await CallRemoteApi<QuerySiSickResponse>("query_si_sick", requestData);

            if (result?.YbjbDs?.Any() == true)
            {
                await SaveSickItems(result.YbjbDs);
                Logger.LogInformation("查询疾病目录成功，共 {YbjbDsCount} 条记录", result.YbjbDs.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "查询疾病目录异常");
            throw;
        }
    }

    public virtual async Task<QuerySiSickBySxhResponse> QuerySiSickBySxh(QuerySiSickBySxhRequest request)
    {
        try
        {
            // 如果没有指定序号，获取本地最大序号
            if (!request.Sxh.HasValue)
                request.Sxh = await sickCatalogRep.AsQueryable()
                    .MaxAsync(x => x.Sxh) ?? 0;

            var requestData = new
            {
                p_filetype = request.FileType, p_sxh = request.Sxh
            };

            var result = await CallRemoteApi<QuerySiSickBySxhResponse>("query_si_sick_by_sxh", requestData);

            if (result?.YbjbDs?.Any() == true)
            {
                await SaveSickItems(result.YbjbDs);
                Logger.LogInformation("增量查询疾病目录成功，共 {YbjbDsCount} 条记录", result.YbjbDs.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "增量查询疾病目录异常");
            throw;
        }
    }

    public virtual async Task<QueryOperationResponse> QueryOperation(QueryOperationRequest request)
    {
        try
        {
            var requestData = new
            {
                p_filetype = request.FileType
            };

            var result = await CallRemoteApi<QueryOperationResponse>("query_operation", requestData);

            if (result?.SsxmDs?.Any() == true)
            {
                await SaveOperationItems(result.SsxmDs);
                Logger.LogInformation("查询手术目录成功，共 {SsxmDsCount} 条记录", result.SsxmDs.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "查询手术目录异常");
            throw;
        }
    }



    public virtual async Task<QueryMlBySxhResponse> QueryMlBySxh(QueryMlBySxhRequest request)
    {
        try
        {
            // 如果没有指定序号，获取本地最大序号
            if (!request.p_sxh.HasValue)
                request.p_sxh = await insuranceItemRep.AsQueryable()
                    .MaxAsync(x => x.Sxh) ?? 0;

            var result = await CallRemoteApi<QueryMlBySxhResponse>("query_ml_by_sxh", request);

            if (result?.ylxm_ds?.Any() == true)
            {
                await SaveInsuranceItems(result.ylxm_ds);
                Logger.LogInformation("增量查询医保目录成功，共 {YlxmDsCount} 条记录", result.ylxm_ds.Count);
            }

            // 保存首先自付比例信息
            if (result?.sxzfbl_ds?.Any() == true)
            {
                await SaveFirstSelfPayRatioItems(result.sxzfbl_ds);
                Logger.LogInformation("保存首先自付比例信息成功，共 {SxzfblDsCount} 条记录", result.sxzfbl_ds.Count);
            }

            // 保存限价信息
            if (result?.xj_ds?.Any() == true)
            {
                await SaveLimitPriceItems(result.xj_ds);
                Logger.LogInformation("保存限价信息成功，共 {XjDsCount} 条记录", result.xj_ds.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "增量查询医保目录异常");
            throw;
        }
    }

    public virtual async Task<AddYsResponse> AddYs(AddYsRequest request)
    {
        try
        {
            var result = await CallRemoteApi<AddYsResponse>("add_ys", request);
            Logger.LogInformation("新增医师信息成功，医师编码：{RequestYsBm}", request.p_ysbm);
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "新增医师信息异常，医师编码：{RequestYsBm}", request.p_ysbm);
            throw;
        }
    }

    public virtual async Task<QueryYsResponse> QueryYs(QueryYsRequest request)
    {
        try
        {
            var result = await CallRemoteApi<QueryYsResponse>("query_ys", request);

            if (result?.ys_ds?.Any() == true)
            {
                await SaveDoctorItems(result.ys_ds);
                Logger.LogInformation("查询医师信息成功，共 {YsDsCount} 条记录", result.ys_ds.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "查询医师信息异常");
            throw;
        }
    }

    public virtual async Task<QueryHospDeptResponse> QueryHospDept(QueryHospDeptRequest request)
    {
        try
        {
            var requestData = new
            {
                p_filetype = request.FileType, p_ksbm = request.KsBm
            };

            var result = await CallRemoteApi<QueryHospDeptResponse>("query_hosp_dept", requestData);
            Logger.LogInformation("查询科室信息成功，共 {DeptDsCount} 条记录", result?.DeptDs?.Count ?? 0);
            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "查询科室信息异常");
            throw;
        }
    }

    public virtual async Task<QuerySiCodeResponse> QuerySiCode(QuerySiCodeRequest request)
    {
        try
        {
            var requestData = new
            {
                p_dmbh = request.DmBh
            };

            var result = await CallRemoteApi<QuerySiCodeResponse>("query_si_code", requestData);

            if (result?.CodeDs?.Any() == true)
            {
                await SaveDictionaryItems(request.DmBh, result.CodeDs);
                Logger.LogInformation("查询数据字典成功，编号：{RequestDmBh}，共 {CodeDsCount} 条记录", request.DmBh, result.CodeDs.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "查询数据字典异常，编号：{RequestDmBh}", request.DmBh);
            throw;
        }
    }

    #endregion

    #region 数据保存方法

    /// <summary>
    /// 保存医院项目到本地数据库
    /// </summary>
    protected virtual async Task SaveHospitalItems(List<YyxmInfo> items)
    {
        if (items?.Any() != true) return;

        // 手动映射，确保属性名称正确匹配
        var entities = items.Select(item => new HospitalItemCatalog
        {
            Yyxmbm = item.yyxmbm,
            Yyxmmc = item.yyxmmc,
            Mzjsxmbh = item.mzjsxmbh,
            Zyjsxmbh = item.zyjsxmbh,
            Zfbl = item.zfbl,
            Sm = item.sm,
            Ylxmbm = item.ylxmbm,
            Gg = item.gg,
            Dw = item.dw,
            Ckj = item.ckj,
            Jxm = item.jxm,
            Scqy = item.scqy,
            Cfybz = item.cfybz,
            Gmpbz = item.gmpbz,
            Zxgg = item.zxgg,
            Bzsl = item.bzsl,
            Gxsj = item.gxsj,
            Qsrq = item.qsrq,
            Zzrq = item.zzrq,
            Spbz = item.spbz,
            Xzbz = item.xzbz,
            Ypbz = item.ypbz,
            Rqlb = item.rqlb,
            Bzgg = item.bzgg,
            Yltclb = item.yltclb,
            Dj = item.dj,
            Sxh = item.sxh,
            LastSyncTime = DateTime.Now
        }).ToList();

        try
        {
            // 使用Upsert操作，存在则更新，不存在则插入
            await hospitalItemRep.Context.Storageable(entities)
                .WhereColumns(x => x.Yyxmbm)
                .ExecuteCommandAsync();

            Logger.LogInformation("保存医院项目目录 {EntitiesCount} 条记录到本地数据库", entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存医院项目目录到本地数据库失败，共 {EntitiesCount} 条记录", entities.Count);
            throw;
        }
    }

    /// <summary>
    /// 保存医保项目到本地数据库
    /// </summary>
    protected virtual async Task SaveInsuranceItems(List<YlxmInfo> items)
    {
        if (items?.Any() != true) return;

        // 手动映射，确保属性名称正确匹配
        var entities = items.Select(item => new InsuranceItemCatalog
        {
            YlxmBm = item.ylxmbm,
            YlxmBzmc = item.ylxmbzmc,
            Py = item.py,
            Syz = item.syz,
            Jj = item.jj,
            Gg = item.gg,
            Dw = item.dw,
            Ckj = item.ckj,
            Jxm = item.jxm,
            ZxBz = item.zxbz,
            Scqy = item.scqy,
            Cdm = item.cdm,
            CfyBz = item.cfybz,
            GmpBz = item.gmpbz,
            ZxGg = item.zxgg,
            Gxsj = item.gxsj,
            YpBz = item.ypbz,
            JsXmbh = item.jsxmbh,
            Zczh = item.zczh,
            Pzwh = item.pzwh,
            Bzgg = item.bzgg,
            LstdFilNo = item.lstd_fil_no,
            Sxh = item.sxh,
            LastSyncTime = DateTime.Now
        }).ToList();

        try
        {
            // 使用Upsert操作，存在则更新，不存在则插入
            await insuranceItemRep.Context.Storageable(entities)
                .WhereColumns(x => x.YlxmBm)
                .ExecuteCommandAsync();

            Logger.LogInformation("保存医保项目目录 {EntitiesCount} 条记录到本地数据库", entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存医保项目目录到本地数据库失败，共 {EntitiesCount} 条记录", entities.Count);
            throw;
        }
    }

    /// <summary>
    /// 保存疾病信息到本地数据库
    /// </summary>
    protected virtual async Task SaveSickItems(List<SiSickInfo> items)
    {
        if (items?.Any() != true) return;

        var entities = items.Select(item => new InsuranceSickCatalog
        {
            JbBm = item.JbBm,
            JbMc = item.JbMc,
            Py = item.Py,
            MzdbLb = item.MzdbLb,
            SbjgBh = item.SbjgBh,
            ZxBz = item.ZxBz,
            Sxh = item.Sxh,
            LastSyncTime = DateTime.Now
        }).ToList();

        try
        {
            // 使用Upsert操作，存在则更新，不存在则插入
            await sickCatalogRep.Context.Storageable(entities)
                .WhereColumns(x => x.JbBm)
                .ExecuteCommandAsync();

            Logger.LogInformation("保存疾病目录 {EntitiesCount} 条记录到本地数据库", entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存疾病目录到本地数据库失败，共 {EntitiesCount} 条记录", entities.Count);
            throw;
        }
    }

    /// <summary>
    /// 保存手术信息到本地数据库
    /// </summary>
    protected virtual async Task SaveOperationItems(List<OperationInfo> items)
    {
        if (items?.Any() != true) return;

        var entities = items.Select(item => new InsuranceOperationCatalog
        {
            SsBm = item.SsBm,
            SsMc = item.SsMc,
            Py = item.Py,
            ZxBz = item.ZxBz,
            LastSyncTime = DateTime.Now
        }).ToList();

        try
        {
            // 使用Upsert操作，存在则更新，不存在则插入
            await operationCatalogRep.Context.Storageable(entities)
                .WhereColumns(x => x.SsBm)
                .ExecuteCommandAsync();

            Logger.LogInformation("保存手术目录 {EntitiesCount} 条记录到本地数据库", entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存手术目录到本地数据库失败，共 {EntitiesCount} 条记录", entities.Count);
            throw;
        }
    }

    /// <summary>
    /// 保存医师信息到本地数据库
    /// </summary>
    protected virtual async Task SaveDoctorItems(List<YsInfo> items)
    {
        if (items?.Any() != true) return;

        var entities = items.Select(item => new DoctorInfo
        {
            YsBm = item.ysbm,
            YsXm = item.ysxm,
            YsXb = item.ysxb,
            YsSfzh = item.yssfzh,
            YsZyzzh = item.yszyzzh,
            YsJb = item.ysjb,
            YsLb = item.yslb,
            KsBm = item.ksbm,
            KsMc = item.ksmc,
            YsZt = item.yszt,
            Bz = item.bz,
            Sxh = item.sxh,
            LastSyncTime = DateTime.Now
        }).ToList();

        try
        {
            // 使用Upsert操作，存在则更新，不存在则插入
            await doctorInfoRep.Context.Storageable(entities)
                .WhereColumns(x => x.YsBm)
                .ExecuteCommandAsync();

            Logger.LogInformation("保存医师信息 {EntitiesCount} 条记录到本地数据库", entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存医师信息到本地数据库失败，共 {EntitiesCount} 条记录", entities.Count);
            throw;
        }
    }

    /// <summary>
    /// 保存限价信息到本地数据库
    /// </summary>
    protected virtual async Task SaveLimitPriceItems(List<XjInfo> items)
    {
        if (items?.Any() != true) return;

        // 手动映射，因为属性名称不匹配
        var entities = items.Select(item => new LimitPriceInfo
        {
            YlxmBm = item.ylxmbm,
            RqLb = item.rqlb,
            YltcLb = item.yltclb,
            DyryLb = item.dyrylb,
            Qsrq = item.qsrq,
            Zzrq = item.zzrq,
            Xj = item.xj,
            Bz = item.bz,
            YyBm = item.yybm,
            YyJb = item.yyjb,
            YljgXz = item.yljgxz,
            LastSyncTime = DateTime.Now
        }).ToList();

        try
        {
            // 使用Upsert操作，存在则更新，不存在则插入
            // 使用复合键：医疗项目编码+人群类别+医疗统筹类别+待遇人员类别+医院编码
            await limitPriceRep.Context.Storageable(entities)
                .WhereColumns(x => new
                {
                    x.YlxmBm,
                    x.RqLb,
                    x.YltcLb,
                    x.DyryLb,
                    x.YyBm
                })
                .ExecuteCommandAsync();

            Logger.LogInformation("保存限价信息 {EntitiesCount} 条记录到本地数据库", entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存限价信息到本地数据库失败，共 {EntitiesCount} 条记录", entities.Count);
            throw;
        }
    }

    /// <summary>
    /// 保存首先自付比例信息到本地数据库
    /// </summary>
    protected virtual async Task SaveFirstSelfPayRatioItems(List<SxzfblInfo> items)
    {
        if (items?.Any() != true) return;

        // 手动映射，因为属性名称不匹配
        var entities = items.Select(item => new FirstSelfPayRatioInfo
        {
            YlxmBm = item.ylxmbm,
            RqLb = item.rqlb,
            YltcLb = item.yltclb,
            DyryLb = item.dyrylb,
            Qsrq = item.qsrq,
            Zzrq = item.zzrq,
            SxzfBl = item.sxzfbl,
            Sm = item.sm,
            XzBz = item.xzbz,
            LastSyncTime = DateTime.Now
        }).ToList();

        try
        {
            // 使用Upsert操作，存在则更新，不存在则插入
            await firstSelfPayRatioRep.Context.Storageable(entities)
                .WhereColumns(x => new
                {
                    x.YlxmBm,
                    x.RqLb,
                    x.YltcLb,
                    x.DyryLb,
                    x.XzBz
                })
                .ExecuteCommandAsync();

            Logger.LogInformation("保存首先自付比例信息 {EntitiesCount} 条记录到本地数据库", entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存首先自付比例信息到本地数据库失败，共 {EntitiesCount} 条记录", entities.Count);
            throw;
        }
    }

    /// <summary>
    /// 保存数据字典到本地数据库
    /// </summary>
    protected virtual async Task SaveDictionaryItems(string dmBh, List<SiCodeInfo> items)
    {
        if (items?.Any() != true) return;

        try
        {
            // 先删除该编号下的旧数据
            await dictionaryRep.DeleteAsync(x => x.DmBh == dmBh);

            var entities = items.Select(item => new InsuranceDictionary
            {
                DmBh = dmBh, Code = item.Code, Content = item.Content, LastSyncTime = DateTime.Now
            }).ToList();

            await dictionaryRep.InsertRangeAsync(entities);

            Logger.LogInformation("保存数据字典 {DmBh} 共 {EntitiesCount} 条记录到本地数据库", dmBh, entities.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "保存数据字典 {DmBh} 到本地数据库失败，共 {ItemsCount} 条记录", dmBh, items.Count);
            throw;
        }
    }

    #endregion
}
