using Furion.DataValidation;
using His.Module.Patient.Api.Enum;
namespace His.Module.Patient.Service;

/// <summary>
/// 就诊卡管理分页查询输入参数
/// </summary>
public class MedicalCardInfoInput : BasePageInput
{
    /// <summary>
    /// 就诊卡号
    /// </summary>
    public string CardNo { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public BusinessTypeEnum? BusinessType { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    public string IdCardNo { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// 就诊卡管理增加输入参数
/// </summary>
public class AddMedicalCardInfoInput : PatientInfo
{
    /// <summary>
	/// 就诊卡号
	/// </summary>

    public string CardNo { get; set; }

    /// <summary>
	/// 患者ID
	/// </summary>
	public long PatientId { get; set; }

    /// <summary>
    /// 业务类型
    /// </summary>
    public BusinessTypeEnum BusinessType { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    public string UseDepts { get; set; }

    /// <summary>
    /// 充值方式
    /// </summary>
    public string ChargeModes { get; set; }

    /// <summary>
    /// 余额
    /// </summary>
    public decimal? Balance { get; set; }

    /// <summary>
    /// 卡状态
    /// </summary>
    public CardStatusEnum Status { get; set; }

    /// <summary>
	/// 备注
	/// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// 就诊卡管理删除输入参数
/// </summary>
public class DeleteMedicalCardInfoInput : BaseIdInput
{
}

/// <summary>
/// 就诊卡管理更新输入参数
/// </summary>
public class UpdateMedicalCardInfoInput : AddMedicalCardInfoInput
{
}

/// <summary>
/// 就诊卡管理挂失输入参数
/// </summary>
public class LossMedicalCardInfoInput : DeleteMedicalCardInfoInput
{
}

/// <summary>
/// 就诊卡管理退卡输入参数
/// </summary>
public class CardRefundMedicalCardInfoInput : DeleteMedicalCardInfoInput
{
    /// <summary>
    /// 退费方式Id
    /// </summary>
    public long? PayMethodId { get; set; }
}

/// <summary>
/// 就诊卡管理恢复输入参数
/// </summary>
public class RestoreMedicalCardInfoInput : DeleteMedicalCardInfoInput
{
}

/// <summary>
/// 就诊卡管理卡充值输入参数
/// </summary>
public class CardRechargeInput : DeleteMedicalCardInfoInput
{
    /// <summary>
    /// 支付类型
    /// </summary>
    [Required(ErrorMessage = "支付方式不能为空")]
    public long? PayMethodId { get; set; }

    /// <summary>
    /// 支付金额
    /// </summary>
    [Required(ErrorMessage = "支付金额不能为空")]
    [DataValidation(ValidationTypes.PositiveNumber, ErrorMessage = "支付金额必须大于0")]
    public decimal? PayAmount { get; set; }
}