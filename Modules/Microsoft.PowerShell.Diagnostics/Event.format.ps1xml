<?xml version="1.0" encoding="utf-8" ?>
<Configuration>
    <ViewDefinitions>
    <View>
            <Name>Default</Name>
            <ViewSelectedBy>
                <TypeName>System.Diagnostics.Eventing.Reader.EventLogRecord</TypeName>
            </ViewSelectedBy>
            <GroupBy>
                <PropertyName>ProviderName</PropertyName>
                <Label>ProviderName</Label>
            </GroupBy>
            <TableControl>
                <TableHeaders>
                    <TableColumnHeader>
                        <Width>25</Width>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Width>8</Width>
                        <Alignment>right</Alignment>
                    </TableColumnHeader>
                    <TableColumnHeader>
                        <Width>16</Width>
                    </TableColumnHeader>
                    <TableColumnHeader>
                    </TableColumnHeader>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>TimeCreated</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Id</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>LevelDisplayName</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>Message</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                </TableRowEntries>
            </TableControl>
        </View>

        <View>
            <Name>Default</Name>
            <ViewSelectedBy>
                <TypeName>System.Diagnostics.Eventing.Reader.EventLogConfiguration</TypeName>
            </ViewSelectedBy>

            <TableControl>
                <TableHeaders>
                    <TableColumnHeader>
                        <Label>LogMode</Label>
                        <Width>9</Width>
                    </TableColumnHeader>
                    <TableColumnHeader>
                         <Label>MaximumSizeInBytes</Label>
                        <Width>18</Width>
                        <Alignment>right</Alignment>
                    </TableColumnHeader>
                    <TableColumnHeader>
                         <Label>RecordCount</Label>
                        <Width>11</Width>
                        <Alignment>right</Alignment>
                    </TableColumnHeader>
                    <TableColumnHeader/>
                </TableHeaders>
                <TableRowEntries>
                    <TableRowEntry>
                        <TableColumnItems>
                            <TableColumnItem>
                                <PropertyName>LogMode</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>MaximumSizeInBytes</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>RecordCount</PropertyName>
                            </TableColumnItem>
                            <TableColumnItem>
                                <PropertyName>LogName</PropertyName>
                            </TableColumnItem>
                        </TableColumnItems>
                    </TableRowEntry>
                 </TableRowEntries>
            </TableControl>
        </View>
        <View>
            <Name>Default</Name>
            <ViewSelectedBy>
                <TypeName>System.Diagnostics.Eventing.Reader.ProviderMetadata</TypeName>
            </ViewSelectedBy>
             <ListControl>
                <ListEntries>
                    <ListEntry>
                        <ListItems>
                            <ListItem>
                                <PropertyName>Name</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>LogLinks</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>Opcodes</PropertyName>
                            </ListItem>
                            <ListItem>
                                <PropertyName>Tasks</PropertyName>
                             </ListItem>
                        </ListItems>
                    </ListEntry>
                </ListEntries>
            </ListControl>
        </View>

    </ViewDefinitions>
</Configuration>

<!-- SIG # Begin signature block -->
<!-- MIIoVQYJKoZIhvcNAQcCoIIoRjCCKEICAQExDzANBglghkgBZQMEAgEFADB5Bgor -->
<!-- BgEEAYI3AgEEoGswaTA0BgorBgEEAYI3AgEeMCYCAwEAAAQQH8w7YFlLCE63JNLG -->
<!-- KX7zUQIBAAIBAAIBAAIBAAIBADAxMA0GCWCGSAFlAwQCAQUABCCQOOVGkrUrTX6O -->
<!-- sUpWPoTmkoUJS/z8ib4FwlQ+vIuDu6CCDYUwggYDMIID66ADAgECAhMzAAAEA73V -->
<!-- lV0POxitAAAAAAQDMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01pY3Jvc29mdCBDb2RlIFNpZ25p -->
<!-- bmcgUENBIDIwMTEwHhcNMjQwOTEyMjAxMTEzWhcNMjUwOTExMjAxMTEzWjB0MQsw -->
<!-- CQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9u -->
<!-- ZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMR4wHAYDVQQDExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB -->
<!-- AQCfdGddwIOnbRYUyg03O3iz19XXZPmuhEmW/5uyEN+8mgxl+HJGeLGBR8YButGV -->
<!-- LVK38RxcVcPYyFGQXcKcxgih4w4y4zJi3GvawLYHlsNExQwz+v0jgY/aejBS2EJY -->
<!-- oUhLVE+UzRihV8ooxoftsmKLb2xb7BoFS6UAo3Zz4afnOdqI7FGoi7g4vx/0MIdi -->
<!-- kwTn5N56TdIv3mwfkZCFmrsKpN0zR8HD8WYsvH3xKkG7u/xdqmhPPqMmnI2jOFw/ -->
<!-- /n2aL8W7i1Pasja8PnRXH/QaVH0M1nanL+LI9TsMb/enWfXOW65Gne5cqMN9Uofv -->
<!-- ENtdwwEmJ3bZrcI9u4LZAkujAgMBAAGjggGCMIIBfjAfBgNVHSUEGDAWBgorBgEE -->
<!-- AYI3TAgBBggrBgEFBQcDAzAdBgNVHQ4EFgQU6m4qAkpz4641iK2irF8eWsSBcBkw -->
<!-- VAYDVR0RBE0wS6RJMEcxLTArBgNVBAsTJE1pY3Jvc29mdCBJcmVsYW5kIE9wZXJh -->
<!-- dGlvbnMgTGltaXRlZDEWMBQGA1UEBRMNMjMwMDEyKzUwMjkyNjAfBgNVHSMEGDAW -->
<!-- gBRIbmTlUAXTgqoXNzcitW2oynUClTBUBgNVHR8ETTBLMEmgR6BFhkNodHRwOi8v -->
<!-- d3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NybC9NaWNDb2RTaWdQQ0EyMDExXzIw -->
<!-- MTEtMDctMDguY3JsMGEGCCsGAQUFBwEBBFUwUzBRBggrBgEFBQcwAoZFaHR0cDov -->
<!-- L3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9jZXJ0cy9NaWNDb2RTaWdQQ0EyMDEx -->
<!-- XzIwMTEtMDctMDguY3J0MAwGA1UdEwEB/wQCMAAwDQYJKoZIhvcNAQELBQADggIB -->
<!-- AFFo/6E4LX51IqFuoKvUsi80QytGI5ASQ9zsPpBa0z78hutiJd6w154JkcIx/f7r -->
<!-- EBK4NhD4DIFNfRiVdI7EacEs7OAS6QHF7Nt+eFRNOTtgHb9PExRy4EI/jnMwzQJV -->
<!-- NokTxu2WgHr/fBsWs6G9AcIgvHjWNN3qRSrhsgEdqHc0bRDUf8UILAdEZOMBvKLC -->
<!-- rmf+kJPEvPldgK7hFO/L9kmcVe67BnKejDKO73Sa56AJOhM7CkeATrJFxO9GLXos -->
<!-- oKvrwBvynxAg18W+pagTAkJefzneuWSmniTurPCUE2JnvW7DalvONDOtG01sIVAB -->
<!-- +ahO2wcUPa2Zm9AiDVBWTMz9XUoKMcvngi2oqbsDLhbK+pYrRUgRpNt0y1sxZsXO -->
<!-- raGRF8lM2cWvtEkV5UL+TQM1ppv5unDHkW8JS+QnfPbB8dZVRyRmMQ4aY/tx5x5+ -->
<!-- sX6semJ//FbiclSMxSI+zINu1jYerdUwuCi+P6p7SmQmClhDM+6Q+btE2FtpsU0W -->
<!-- +r6RdYFf/P+nK6j2otl9Nvr3tWLu+WXmz8MGM+18ynJ+lYbSmFWcAj7SYziAfT0s -->
<!-- IwlQRFkyC71tsIZUhBHtxPliGUu362lIO0Lpe0DOrg8lspnEWOkHnCT5JEnWCbzu -->
<!-- iVt8RX1IV07uIveNZuOBWLVCzWJjEGa+HhaEtavjy6i7MIIHejCCBWKgAwIBAgIK -->
<!-- YQ6Q0gAAAAAAAzANBgkqhkiG9w0BAQsFADCBiDELMAkGA1UEBhMCVVMxEzARBgNV -->
<!-- BAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAcBgNVBAoTFU1pY3Jv -->
<!-- c29mdCBDb3Jwb3JhdGlvbjEyMDAGA1UEAxMpTWljcm9zb2Z0IFJvb3QgQ2VydGlm -->
<!-- aWNhdGUgQXV0aG9yaXR5IDIwMTEwHhcNMTEwNzA4MjA1OTA5WhcNMjYwNzA4MjEw -->
<!-- OTA5WjB+MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UE -->
<!-- BxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMSgwJgYD -->
<!-- VQQDEx9NaWNyb3NvZnQgQ29kZSBTaWduaW5nIFBDQSAyMDExMIICIjANBgkqhkiG -->
<!-- 9w0BAQEFAAOCAg8AMIICCgKCAgEAq/D6chAcLq3YbqqCEE00uvK2WCGfQhsqa+la -->
<!-- UKq4BjgaBEm6f8MMHt03a8YS2AvwOMKZBrDIOdUBFDFC04kNeWSHfpRgJGyvnkmc -->
<!-- 6Whe0t+bU7IKLMOv2akrrnoJr9eWWcpgGgXpZnboMlImEi/nqwhQz7NEt13YxC4D -->
<!-- dato88tt8zpcoRb0RrrgOGSsbmQ1eKagYw8t00CT+OPeBw3VXHmlSSnnDb6gE3e+ -->
<!-- lD3v++MrWhAfTVYoonpy4BI6t0le2O3tQ5GD2Xuye4Yb2T6xjF3oiU+EGvKhL1nk -->
<!-- kDstrjNYxbc+/jLTswM9sbKvkjh+0p2ALPVOVpEhNSXDOW5kf1O6nA+tGSOEy/S6 -->
<!-- A4aN91/w0FK/jJSHvMAhdCVfGCi2zCcoOCWYOUo2z3yxkq4cI6epZuxhH2rhKEmd -->
<!-- X4jiJV3TIUs+UsS1Vz8kA/DRelsv1SPjcF0PUUZ3s/gA4bysAoJf28AVs70b1FVL -->
<!-- 5zmhD+kjSbwYuER8ReTBw3J64HLnJN+/RpnF78IcV9uDjexNSTCnq47f7Fufr/zd -->
<!-- sGbiwZeBe+3W7UvnSSmnEyimp31ngOaKYnhfsi+E11ecXL93KCjx7W3DKI8sj0A3 -->
<!-- T8HhhUSJxAlMxdSlQy90lfdu+HggWCwTXWCVmj5PM4TasIgX3p5O9JawvEagbJjS -->
<!-- 4NaIjAsCAwEAAaOCAe0wggHpMBAGCSsGAQQBgjcVAQQDAgEAMB0GA1UdDgQWBBRI -->
<!-- bmTlUAXTgqoXNzcitW2oynUClTAZBgkrBgEEAYI3FAIEDB4KAFMAdQBiAEMAQTAL -->
<!-- BgNVHQ8EBAMCAYYwDwYDVR0TAQH/BAUwAwEB/zAfBgNVHSMEGDAWgBRyLToCMZBD -->
<!-- uRQFTuHqp8cx0SOJNDBaBgNVHR8EUzBRME+gTaBLhklodHRwOi8vY3JsLm1pY3Jv -->
<!-- c29mdC5jb20vcGtpL2NybC9wcm9kdWN0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf -->
<!-- MDNfMjIuY3JsMF4GCCsGAQUFBwEBBFIwUDBOBggrBgEFBQcwAoZCaHR0cDovL3d3 -->
<!-- dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNSb29DZXJBdXQyMDExXzIwMTFf -->
<!-- MDNfMjIuY3J0MIGfBgNVHSAEgZcwgZQwgZEGCSsGAQQBgjcuAzCBgzA/BggrBgEF -->
<!-- BQcCARYzaHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraW9wcy9kb2NzL3ByaW1h -->
<!-- cnljcHMuaHRtMEAGCCsGAQUFBwICMDQeMiAdAEwAZQBnAGEAbABfAHAAbwBsAGkA -->
<!-- YwB5AF8AcwB0AGEAdABlAG0AZQBuAHQALiAdMA0GCSqGSIb3DQEBCwUAA4ICAQBn -->
<!-- 8oalmOBUeRou09h0ZyKbC5YR4WOSmUKWfdJ5DJDBZV8uLD74w3LRbYP+vj/oCso7 -->
<!-- v0epo/Np22O/IjWll11lhJB9i0ZQVdgMknzSGksc8zxCi1LQsP1r4z4HLimb5j0b -->
<!-- pdS1HXeUOeLpZMlEPXh6I/MTfaaQdION9MsmAkYqwooQu6SpBQyb7Wj6aC6VoCo/ -->
<!-- KmtYSWMfCWluWpiW5IP0wI/zRive/DvQvTXvbiWu5a8n7dDd8w6vmSiXmE0OPQvy -->
<!-- CInWH8MyGOLwxS3OW560STkKxgrCxq2u5bLZ2xWIUUVYODJxJxp/sfQn+N4sOiBp -->
<!-- mLJZiWhub6e3dMNABQamASooPoI/E01mC8CzTfXhj38cbxV9Rad25UAqZaPDXVJi -->
<!-- hsMdYzaXht/a8/jyFqGaJ+HNpZfQ7l1jQeNbB5yHPgZ3BtEGsXUfFL5hYbXw3MYb -->
<!-- BL7fQccOKO7eZS/sl/ahXJbYANahRr1Z85elCUtIEJmAH9AAKcWxm6U/RXceNcbS -->
<!-- oqKfenoi+kiVH6v7RyOA9Z74v2u3S5fi63V4GuzqN5l5GEv/1rMjaHXmr/r8i+sL -->
<!-- gOppO6/8MO0ETI7f33VtY5E90Z1WTk+/gFcioXgRMiF670EKsT/7qMykXcGhiJtX -->
<!-- cVZOSEXAQsmbdlsKgEhr/Xmfwb1tbWrJUnMTDXpQzTGCGiYwghoiAgEBMIGVMH4x -->
<!-- CzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRt -->
<!-- b25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xKDAmBgNVBAMTH01p -->
<!-- Y3Jvc29mdCBDb2RlIFNpZ25pbmcgUENBIDIwMTECEzMAAAQDvdWVXQ87GK0AAAAA -->
<!-- BAMwDQYJYIZIAWUDBAIBBQCgga4wGQYJKoZIhvcNAQkDMQwGCisGAQQBgjcCAQQw -->
<!-- HAYKKwYBBAGCNwIBCzEOMAwGCisGAQQBgjcCARUwLwYJKoZIhvcNAQkEMSIEINt2 -->
<!-- db++5K27wSMoRg/0xrtc9Hy4c7PP8gMfCKSJKmQhMEIGCisGAQQBgjcCAQwxNDAy -->
<!-- oBSAEgBNAGkAYwByAG8AcwBvAGYAdKEagBhodHRwOi8vd3d3Lm1pY3Jvc29mdC5j -->
<!-- b20wDQYJKoZIhvcNAQEBBQAEggEAVBREansKky40WsaqgDicX32bk9hlvIxaSIvA -->
<!-- AYZvQ2ymByjjaBdXC9/pPZZFRq5+AhdnQuQ9tjOzuynAHB2gIs55t9zgMkggnZv3 -->
<!-- IbHYm2FVHyUolKpKrUFYE0gWzrJ8deaCcGGjEn5+Zg34oq8vaZEvzj1TqF2LXA8b -->
<!-- 1B1gg+fGgaLdshZlCA4NjEtey1YzF38IlcYL/Jraj9IKfogqXuvNPjFgpdZCpOWb -->
<!-- wi5CRP61behGodWp1iIH2z3FzH1nj6rN5Q0DUAZRYKSXbaq1+3aGKKs2DllFGSaM -->
<!-- 97a0hkx5dpv+KFO09St7UBMe5unYhMy/7yxSMAiPFwk9evKEhaGCF7AwghesBgor -->
<!-- BgEEAYI3AwMBMYIXnDCCF5gGCSqGSIb3DQEHAqCCF4kwgheFAgEDMQ8wDQYJYIZI -->
<!-- AWUDBAIBBQAwggFaBgsqhkiG9w0BCRABBKCCAUkEggFFMIIBQQIBAQYKKwYBBAGE -->
<!-- WQoDATAxMA0GCWCGSAFlAwQCAQUABCC1l68HfoOASIyMJTQPFmOpp5cohifOsw5G -->
<!-- 16YbnvB2YQIGZ2LQmtIPGBMyMDI1MDEyMTE5MjkzOS4wNjdaMASAAgH0oIHZpIHW -->
<!-- MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH -->
<!-- UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL -->
<!-- EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT -->
<!-- Hm5TaGllbGQgVFNTIEVTTjo0MDFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z -->
<!-- b2Z0IFRpbWUtU3RhbXAgU2VydmljZaCCEf4wggcoMIIFEKADAgECAhMzAAAB/tCo -->
<!-- wns0IQsBAAEAAAH+MA0GCSqGSIb3DQEBCwUAMHwxCzAJBgNVBAYTAlVTMRMwEQYD -->
<!-- VQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNy -->
<!-- b3NvZnQgQ29ycG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1w -->
<!-- IFBDQSAyMDEwMB4XDTI0MDcyNTE4MzExOFoXDTI1MTAyMjE4MzExOFowgdMxCzAJ -->
<!-- BgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5ndG9uMRAwDgYDVQQHEwdSZWRtb25k -->
<!-- MR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9yYXRpb24xLTArBgNVBAsTJE1pY3Jv -->
<!-- c29mdCBJcmVsYW5kIE9wZXJhdGlvbnMgTGltaXRlZDEnMCUGA1UECxMeblNoaWVs -->
<!-- ZCBUU1MgRVNOOjQwMUEtMDVFMC1EOTQ3MSUwIwYDVQQDExxNaWNyb3NvZnQgVGlt -->
<!-- ZS1TdGFtcCBTZXJ2aWNlMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA -->
<!-- vLwhFxWlqA43olsE4PCegZ4mSfsH2YTSKEYv8Gn3362Bmaycdf5T3tQxpP3NWm62 -->
<!-- YHUieIQXw+0u4qlay4AN3IonI+47Npi9fo52xdAXMX0pGrc0eqW8RWN3bfzXPKv0 -->
<!-- 7O18i2HjDyLuywYyKA9FmWbePjahf9Mwd8QgygkPtwDrVQGLyOkyM3VTiHKqhGu9 -->
<!-- BCGVRdHW9lmPMrrUlPWiYV9LVCB5VYd+AEUtdfqAdqlzVxA53EgxSqhp6JbfEKnT -->
<!-- dcfP6T8Mir0HrwTTtV2h2yDBtjXbQIaqycKOb633GfRkn216LODBg37P/xwhodXT -->
<!-- 81ZC2aHN7exEDmmbiWssjGvFJkli2g6dt01eShOiGmhbonr0qXXcBeqNb6QoF8jX -->
<!-- /uDVtY9pvL4j8aEWS49hKUH0mzsCucIrwUS+x8MuT0uf7VXCFNFbiCUNRTofxJ3B -->
<!-- 454eGJhL0fwUTRbgyCbpLgKMKDiCRub65DhaeDvUAAJT93KSCoeFCoklPavbgQya -->
<!-- hGZDL/vWAVjX5b8Jzhly9gGCdK/qi6i+cxZ0S8x6B2yjPbZfdBVfH/NBp/1Ln7xb -->
<!-- eOETAOn7OT9D3UGt0q+KiWgY42HnLjyhl1bAu5HfgryAO3DCaIdV2tjvkJay2qOn -->
<!-- F7Dgj8a60KQT9QgfJfwXnr3ZKibYMjaUbCNIDnxz2ykCAwEAAaOCAUkwggFFMB0G -->
<!-- A1UdDgQWBBRvznuJ9SU2g5l/5/b+5CBibbHF3TAfBgNVHSMEGDAWgBSfpxVdAF5i -->
<!-- XYP05dJlpxtTNRnpcjBfBgNVHR8EWDBWMFSgUqBQhk5odHRwOi8vd3d3Lm1pY3Jv -->
<!-- c29mdC5jb20vcGtpb3BzL2NybC9NaWNyb3NvZnQlMjBUaW1lLVN0YW1wJTIwUENB -->
<!-- JTIwMjAxMCgxKS5jcmwwbAYIKwYBBQUHAQEEYDBeMFwGCCsGAQUFBzAChlBodHRw -->
<!-- Oi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3BzL2NlcnRzL01pY3Jvc29mdCUyMFRp -->
<!-- bWUtU3RhbXAlMjBQQ0ElMjAyMDEwKDEpLmNydDAMBgNVHRMBAf8EAjAAMBYGA1Ud -->
<!-- JQEB/wQMMAoGCCsGAQUFBwMIMA4GA1UdDwEB/wQEAwIHgDANBgkqhkiG9w0BAQsF -->
<!-- AAOCAgEAiT4NUvO2lw+0dDMtsBuxmX2o3lVQqnQkuITAGIGCgI+sl7ZqZOTDd8Lq -->
<!-- xsH4GWCPTztc3tr8AgBvsYIzWjFwioCjCQODq1oBMWNzEsKzckHxAzYo5Sze7OPk -->
<!-- MA3DAxVq4SSR8y+TRC2GcOd0JReZ1lPlhlPl9XI+z8OgtOPmQnLLiP9qzpTHwFze -->
<!-- +sbqSn8cekduMZdLyHJk3Niw3AnglU/WTzGsQAdch9SVV4LHifUnmwTf0i07iKtT -->
<!-- lNkq3bx1iyWg7N7jGZABRWT2mX+YAVHlK27t9n+WtYbn6cOJNX6LsH8xPVBRYAIR -->
<!-- VkWsMyEAdoP9dqfaZzwXGmjuVQ931NhzHjjG+Efw118DXjk3Vq3qUI1re34zMMTR -->
<!-- zZZEw82FupF3viXNR3DVOlS9JH4x5emfINa1uuSac6F4CeJCD1GakfS7D5ayNsaZ -->
<!-- 2e+sBUh62KVTlhEsQRHZRwCTxbix1Y4iJw+PDNLc0Hf19qX2XiX0u2SM9CWTTjsz -->
<!-- 9SvCjIKSxCZFCNv/zpKIlsHx7hQNQHSMbKh0/wwn86uiIALEjazUszE0+X6rcObD -->
<!-- fU4h/O/0vmbF3BMR+45rAZMAETJsRDPxHJCo/5XGhWdg/LoJ5XWBrODL44YNrN7F -->
<!-- RnHEAAr06sflqZ8eeV3FuDKdP5h19WUnGWwO1H/ZjUzOoVGiV3gwggdxMIIFWaAD -->
<!-- AgECAhMzAAAAFcXna54Cm0mZAAAAAAAVMA0GCSqGSIb3DQEBCwUAMIGIMQswCQYD -->
<!-- VQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEe -->
<!-- MBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMTIwMAYDVQQDEylNaWNyb3Nv -->
<!-- ZnQgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkgMjAxMDAeFw0yMTA5MzAxODIy -->
<!-- MjVaFw0zMDA5MzAxODMyMjVaMHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNo -->
<!-- aW5ndG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29y -->
<!-- cG9yYXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEw -->
<!-- MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5OGmTOe0ciELeaLL1yR5 -->
<!-- vQ7VgtP97pwHB9KpbE51yMo1V/YBf2xK4OK9uT4XYDP/XE/HZveVU3Fa4n5KWv64 -->
<!-- NmeFRiMMtY0Tz3cywBAY6GB9alKDRLemjkZrBxTzxXb1hlDcwUTIcVxRMTegCjhu -->
<!-- je3XD9gmU3w5YQJ6xKr9cmmvHaus9ja+NSZk2pg7uhp7M62AW36MEBydUv626GIl -->
<!-- 3GoPz130/o5Tz9bshVZN7928jaTjkY+yOSxRnOlwaQ3KNi1wjjHINSi947SHJMPg -->
<!-- yY9+tVSP3PoFVZhtaDuaRr3tpK56KTesy+uDRedGbsoy1cCGMFxPLOJiss254o2I -->
<!-- 5JasAUq7vnGpF1tnYN74kpEeHT39IM9zfUGaRnXNxF803RKJ1v2lIH1+/NmeRd+2 -->
<!-- ci/bfV+AutuqfjbsNkz2K26oElHovwUDo9Fzpk03dJQcNIIP8BDyt0cY7afomXw/ -->
<!-- TNuvXsLz1dhzPUNOwTM5TI4CvEJoLhDqhFFG4tG9ahhaYQFzymeiXtcodgLiMxhy -->
<!-- 16cg8ML6EgrXY28MyTZki1ugpoMhXV8wdJGUlNi5UPkLiWHzNgY1GIRH29wb0f2y -->
<!-- 1BzFa/ZcUlFdEtsluq9QBXpsxREdcu+N+VLEhReTwDwV2xo3xwgVGD94q0W29R6H -->
<!-- XtqPnhZyacaue7e3PmriLq0CAwEAAaOCAd0wggHZMBIGCSsGAQQBgjcVAQQFAgMB -->
<!-- AAEwIwYJKwYBBAGCNxUCBBYEFCqnUv5kxJq+gpE8RjUpzxD/LwTuMB0GA1UdDgQW -->
<!-- BBSfpxVdAF5iXYP05dJlpxtTNRnpcjBcBgNVHSAEVTBTMFEGDCsGAQQBgjdMg30B -->
<!-- ATBBMD8GCCsGAQUFBwIBFjNodHRwOi8vd3d3Lm1pY3Jvc29mdC5jb20vcGtpb3Bz -->
<!-- L0RvY3MvUmVwb3NpdG9yeS5odG0wEwYDVR0lBAwwCgYIKwYBBQUHAwgwGQYJKwYB -->
<!-- BAGCNxQCBAweCgBTAHUAYgBDAEEwCwYDVR0PBAQDAgGGMA8GA1UdEwEB/wQFMAMB -->
<!-- Af8wHwYDVR0jBBgwFoAU1fZWy4/oolxiaNE9lJBb186aGMQwVgYDVR0fBE8wTTBL -->
<!-- oEmgR4ZFaHR0cDovL2NybC5taWNyb3NvZnQuY29tL3BraS9jcmwvcHJvZHVjdHMv -->
<!-- TWljUm9vQ2VyQXV0XzIwMTAtMDYtMjMuY3JsMFoGCCsGAQUFBwEBBE4wTDBKBggr -->
<!-- BgEFBQcwAoY+aHR0cDovL3d3dy5taWNyb3NvZnQuY29tL3BraS9jZXJ0cy9NaWNS -->
<!-- b29DZXJBdXRfMjAxMC0wNi0yMy5jcnQwDQYJKoZIhvcNAQELBQADggIBAJ1Vffwq -->
<!-- reEsH2cBMSRb4Z5yS/ypb+pcFLY+TkdkeLEGk5c9MTO1OdfCcTY/2mRsfNB1OW27 -->
<!-- DzHkwo/7bNGhlBgi7ulmZzpTTd2YurYeeNg2LpypglYAA7AFvonoaeC6Ce5732pv -->
<!-- vinLbtg/SHUB2RjebYIM9W0jVOR4U3UkV7ndn/OOPcbzaN9l9qRWqveVtihVJ9Ak -->
<!-- vUCgvxm2EhIRXT0n4ECWOKz3+SmJw7wXsFSFQrP8DJ6LGYnn8AtqgcKBGUIZUnWK -->
<!-- NsIdw2FzLixre24/LAl4FOmRsqlb30mjdAy87JGA0j3mSj5mO0+7hvoyGtmW9I/2 -->
<!-- kQH2zsZ0/fZMcm8Qq3UwxTSwethQ/gpY3UA8x1RtnWN0SCyxTkctwRQEcb9k+SS+ -->
<!-- c23Kjgm9swFXSVRk2XPXfx5bRAGOWhmRaw2fpCjcZxkoJLo4S5pu+yFUa2pFEUep -->
<!-- 8beuyOiJXk+d0tBMdrVXVAmxaQFEfnyhYWxz/gq77EFmPWn9y8FBSX5+k77L+Dvk -->
<!-- txW/tM4+pTFRhLy/AsGConsXHRWJjXD+57XQKBqJC4822rpM+Zv/Cuk0+CQ1Zyvg -->
<!-- DbjmjJnW4SLq8CdCPSWU5nR0W2rRnj7tfqAxM328y+l7vzhwRNGQ8cirOoo6CGJ/ -->
<!-- 2XBjU02N7oJtpQUQwXEGahC0HVUzWLOhcGbyoYIDWTCCAkECAQEwggEBoYHZpIHW -->
<!-- MIHTMQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2FzaGluZ3RvbjEQMA4GA1UEBxMH -->
<!-- UmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENvcnBvcmF0aW9uMS0wKwYDVQQL -->
<!-- EyRNaWNyb3NvZnQgSXJlbGFuZCBPcGVyYXRpb25zIExpbWl0ZWQxJzAlBgNVBAsT -->
<!-- Hm5TaGllbGQgVFNTIEVTTjo0MDFBLTA1RTAtRDk0NzElMCMGA1UEAxMcTWljcm9z -->
<!-- b2Z0IFRpbWUtU3RhbXAgU2VydmljZaIjCgEBMAcGBSsOAwIaAxUAhGNHD/a7Q0bQ -->
<!-- LWVG9JuGxgLRXseggYMwgYCkfjB8MQswCQYDVQQGEwJVUzETMBEGA1UECBMKV2Fz -->
<!-- aGluZ3RvbjEQMA4GA1UEBxMHUmVkbW9uZDEeMBwGA1UEChMVTWljcm9zb2Z0IENv -->
<!-- cnBvcmF0aW9uMSYwJAYDVQQDEx1NaWNyb3NvZnQgVGltZS1TdGFtcCBQQ0EgMjAx -->
<!-- MDANBgkqhkiG9w0BAQsFAAIFAOs6IE0wIhgPMjAyNTAxMjExMzMxNTdaGA8yMDI1 -->
<!-- MDEyMjEzMzE1N1owdzA9BgorBgEEAYRZCgQBMS8wLTAKAgUA6zogTQIBADAKAgEA -->
<!-- AgIacAIB/zAHAgEAAgITuDAKAgUA6ztxzQIBADA2BgorBgEEAYRZCgQCMSgwJjAM -->
<!-- BgorBgEEAYRZCgMCoAowCAIBAAIDB6EgoQowCAIBAAIDAYagMA0GCSqGSIb3DQEB -->
<!-- CwUAA4IBAQBRuxp1LbxVMWFgU6aILCRvONxP0N5tCD+tczrfwmkMpKVn1u0Wvdy6 -->
<!-- 4w4r3y5TznkAhYamuuj0OTwVBLdRg0hhkpNIT/w5rKs8UUsb1Pq4yIuCg1QrGZsV -->
<!-- fic0AglNfxC0eS2Kjgba0LRLoOru9tbSKIEVK+CXZTFA9aWtGmwi5ApFRGqqD6DS -->
<!-- /m8UB53Mm4n145VhsJu/xaXQxcxjStpuWzH5e+SYuFNDBradS/bm4MeEUYqj3zT3 -->
<!-- XHvA770pYiaVQzBGA/aTobBPdXbVLHxyXtWnBR8eHoocjNs2iuub064rKAYYcERn -->
<!-- yNym3NP4TRbSO51ZMUzfPP2mwFh8565eMYIEDTCCBAkCAQEwgZMwfDELMAkGA1UE -->
<!-- BhMCVVMxEzARBgNVBAgTCldhc2hpbmd0b24xEDAOBgNVBAcTB1JlZG1vbmQxHjAc -->
<!-- BgNVBAoTFU1pY3Jvc29mdCBDb3Jwb3JhdGlvbjEmMCQGA1UEAxMdTWljcm9zb2Z0 -->
<!-- IFRpbWUtU3RhbXAgUENBIDIwMTACEzMAAAH+0KjCezQhCwEAAQAAAf4wDQYJYIZI -->
<!-- AWUDBAIBBQCgggFKMBoGCSqGSIb3DQEJAzENBgsqhkiG9w0BCRABBDAvBgkqhkiG -->
<!-- 9w0BCQQxIgQgindWVbeJkfayO/xoQ2PdUWsWkzLgN6TX/wlH2u6GuDkwgfoGCyqG -->
<!-- SIb3DQEJEAIvMYHqMIHnMIHkMIG9BCARhczd/FPInxjR92m2hPWqc+vGOG1+/I0W -->
<!-- tkCstyh0eTCBmDCBgKR+MHwxCzAJBgNVBAYTAlVTMRMwEQYDVQQIEwpXYXNoaW5n -->
<!-- dG9uMRAwDgYDVQQHEwdSZWRtb25kMR4wHAYDVQQKExVNaWNyb3NvZnQgQ29ycG9y -->
<!-- YXRpb24xJjAkBgNVBAMTHU1pY3Jvc29mdCBUaW1lLVN0YW1wIFBDQSAyMDEwAhMz -->
<!-- AAAB/tCowns0IQsBAAEAAAH+MCIEIEXY8/UT82+uzBFrNwA3r0/i3TvbPuM56ZXD -->
<!-- iW1KTXhJMA0GCSqGSIb3DQEBCwUABIICAH7I/MM1tLOYVi8N1XRMaq14hZldz9mY -->
<!-- ZPO7HahPOVPsfdxRPXvcNZC6wRXmTz4xMytb90RANGtzRXLfA2yVnDusU+ENyvG6 -->
<!-- 7eTag8jyQZ9rQ2VouRNpva9dHVhHi+uuly5YVGj0au/0MdaK3PHnGED1mbzUUd+Y -->
<!-- BAxRmeFNkozQHAlocvqcDAyQ4qkWWD+AvFqOjdLCzxcmaBE70Xmq5qSAStF+yvnX -->
<!-- aHqZRWk29Uj4kznlnX4V6FwfZX9I96yb0jTFrn81LGG+qqT6c2zisyo2TRozJeXV -->
<!-- guqjuvrQde1GVxk23dF37nuk8WpjmW/ZnbtzE9uSk4ss76fK9oHNW+o+jJNGZXZB -->
<!-- QocoQWFg0Ho6bpssz4r1J1sFRHwcWFYWFjeLC1kbbQhHR9tRMXTRRNmPThXEvbjb -->
<!-- KDpXj/Af6BeC1HpctlA109iteSJHLXuJtisdjXLdu/lKMzv36ERyf8kEyDMOxbij -->
<!-- H0u+WPBTdnF+kKKG/pRHmkOaJdbeqX1a4Hy72hyQUNeWyXyV9LYWyRm+2CTusTkF -->
<!-- Yk7m8cFC7zJpnjNirrI9GEiFQtH431UOyWMy+rMy5yXCWonnqVHsx9X35xQKrAX0 -->
<!-- Zyo5VPtpAF8N6QjOR9VX7ZK4r1zPy7NBYrYtMiiVmFWHxpg7E3njc+L01okHvxp1 -->
<!-- vGNjbfGA9epO -->
<!-- SIG # End signature block -->
