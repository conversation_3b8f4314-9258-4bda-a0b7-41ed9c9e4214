using Admin.NET.Core;
namespace His.Module.MedicalTech.Entity;

/// <summary>
/// 检验模板表
/// </summary>
[Tenant("1300000000009")]
[SugarTable("template_lab_test", "检验模板表")]
public class TemplateLabTest : EntityTenant
{
    /// <summary>
    /// 检验名称
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "template_name", ColumnDescription = "检验名称", Length = 64)]
    public virtual string TemplateName { get; set; }
    
    /// <summary>
    /// 1 全院/ 2 科室模板/3 个人模板
    /// </summary>
    [SugarColumn(ColumnName = "template_scope", ColumnDescription = "1 全院/ 2 科室模板/3 个人模板")]
    public virtual int? TemplateScope { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 创建者部门Id
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建者部门Id")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建者部门名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建者部门名称", Length = 64)]
    public virtual string? CreateOrgName { get; set; }
    
}
