using Furion.DatabaseAccessor;
using His.Module.MedicalTech.Api.Examination;
using His.Module.MedicalTech.Api.Examination.Dto;
using His.Module.MedicalTech.Enum;
using His.Module.OutpatientDoctor.Api.Charge;
using His.Module.OutpatientDoctor.Api.Charge.Dto;
using His.Module.Shared.Api.Api.ChargeItem;
using Yitter.IdGenerator;
namespace His.Module.MedicalTech;

/// <summary>
/// 检查服务 🧩
/// </summary>
[ApiDescriptionSettings(MedicalTechConst.GroupName, Order = 100)]
public class ExaminationService(
    SqlSugarRepository<Examination> examinationRep,
    IChargeItemApi chargeItemApi,
    SqlSugarRepository<ExaminationDetails> examinationDetailsRep,
    SqlSugarRepository<ExaminationPackageItem> examinationPackageItemRep,
    IChargeApi chargeApi)
    : IDynamicApiController, ITransient, IExaminationApi
{

    /// <summary>
    /// 分页查询检查 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询检查")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<ExaminationOutput>> Page(PageExaminationInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = examinationRep.AsQueryable()
            .Includes(de => de.ExaminationDetails, pack => pack.ExaminationPackageItem)
            .Where(u => u.RegisterId == input.RegisterId)
            .Where(u => u.Flag == input.Flag)
            .Where(u => u.BillingTime >= DateTime.Today)
            .Select(u => new ExaminationOutput()
            {
                ExaminationDetails = u.ExaminationDetails,
            }, true);
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取检查详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取检查详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Examination> Detail([FromQuery] QueryByIdExaminationInput input)
    {
        return await examinationRep.AsQueryable()
            .Includes(de => de.ExaminationDetails, pack => pack.ExaminationPackageItem)
            .FirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加检查 ➕
    /// </summary>
    /// <param name="input">输入参数，包含检查和检查明细信息</param>
    /// <returns></returns>
    [DisplayName("增加检查")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost, UnitOfWork]
    public async Task Add(AddExaminationInput input)
    {
        // 将输入参数转换为检查实体
        var examination = input.Adapt<Examination>();
        // 将输入的检查明细转换为实体列表
        var detailsList = input.ExaminationDetails.Adapt<List<ExaminationDetails>>();

        // 验证检查明细中执行科室是否一致
        if (detailsList.Count > 1)
        {
            var firstDeptId = detailsList.First().ExecuteDeptId; // 获取第一个明细的执行科室ID
            if (detailsList.Any(detail => detail.ExecuteDeptId != firstDeptId))
            {
                throw Oops.Oh(MedicalTechErrorCodeEnum.MT0003);
            }
        }

        // 设置检查的基本信息
        examination.Id = YitIdHelper.NextId();
        examination.ApplyNo = await examinationRep.Context.Ado.GetStringAsync(
            "SELECT LPAD(CAST(NEXTVAL('apply_no_seq') As varchar), 8, '0')");
        examination.Status = 1; // 设置状态为未收费
        examination.BillingTime = DateTime.Now;
        examination.BillingDeptId = long.Parse(App.User.FindFirst(ClaimConst.OrgId)?.Value ?? "0");
        examination.BillingDeptName = App.User.FindFirst(ClaimConst.OrgName)?.Value;
        examination.BillingDoctorId = long.Parse(App.User.FindFirst(ClaimConst.UserId)?.Value ?? "0");
        examination.BillingDoctorName = App.User.FindFirst(ClaimConst.RealName)?.Value;
        examination.ExecuteDeptId = detailsList.First().ExecuteDeptId;
        examination.ExecuteDeptName = detailsList.First().ExecuteDeptName;

        // 批量获取所有关联的收费项目详细信息，优化数据库查询，避免N+1问题
        var chargeItemDetails = await chargeItemApi.GetDetails([.. detailsList.Select(detail => (long)detail.ItemId)]);

        foreach (var detail in detailsList)
        {
            // 根据 ItemId 匹配对应的收费项目
            var chargeItem = chargeItemDetails.FirstOrDefault(item => item.Id == detail.ItemId);
            if (chargeItem == null) continue;

            // 填充收费项目基础信息
            detail.Id = YitIdHelper.NextId();
            detail.ExaminationId = examination.Id;
            detail.ApplyNo = examination.ApplyNo;
            detail.ItemId = chargeItem.Id;
            detail.ItemCode = chargeItem.Code;
            detail.ItemName = chargeItem.Name;
            detail.Unit = chargeItem.Unit;
            detail.Price = chargeItem.Price;
            detail.Amount = detail.Quantity * chargeItem.Price;
            detail.ChargeCategoryId = chargeItem.ChargeCategoryId;
            detail.IsPackage = (int?)chargeItem.Package;

            // 如果是套餐项目，处理套餐子项
            if (chargeItem.Package == YesNoEnum.Y)
            {
                var packageItems = chargeItem.ChargeItemPacks.Select(packageItem => new ExaminationPackageItem
                {
                    ExaminationId = examination.Id,
                    ApplyNo = examination.ApplyNo,
                    ExaminationDetailsId = detail.Id,
                    ItemId = packageItem.Id,
                    ItemCode = packageItem.Code,
                    ItemName = packageItem.Name,
                    Unit = packageItem.Unit,
                    Price = packageItem.Price,
                    Quantity = detail.Quantity * packageItem.Quantity,
                    Amount = detail.Quantity * packageItem.Quantity * packageItem.Price,
                    ChargeCategoryId = packageItem.ChargeCategoryId,
                }).ToList();

                // 批量插入套餐子项记录
                await examinationPackageItemRep.InsertRangeAsync(packageItems);
            }
        }

        // 批量插入检查明细和检查记录
        await examinationDetailsRep.InsertRangeAsync(detailsList);
        await examinationRep.InsertAsync(examination);
    }

    /// <summary>
    /// 更新检查 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新检查")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost, UnitOfWork]
    public async Task Update(UpdateExaminationInput input)
    {
        var record = await examinationRep.GetFirstAsync(u => u.Id == input.Id);
        if (record.Status != 1)
        {
            throw new Exception("当前状态无法修改申请单");
        }
        // 将输入参数转换为检查实体
        var examination = input.Adapt<Examination>();
        // 将输入的检查明细转换为实体列表
        var detailsList = input.ExaminationDetails.Adapt<List<ExaminationDetails>>();// 批量获取所有关联的收费项目详细信息，优化数据库查询，避免N+1问题

        var chargeItemDetails = await chargeItemApi.GetDetails([.. detailsList.Select(detail => (long)detail.ItemId)]);

        foreach (var detail in detailsList)
        {
            if (detail.Id.Equals(0L))
            {
                // 根据 ItemId 匹配对应的收费项目
                var chargeItem = chargeItemDetails.FirstOrDefault(item => item.Id == detail.ItemId);
                if (chargeItem == null) continue;

                // 填充收费项目基础信息
                detail.Id = YitIdHelper.NextId();
                detail.ExaminationId = examination.Id;
                detail.ApplyNo = examination.ApplyNo;
                detail.ItemId = chargeItem.Id;
                detail.ItemCode = chargeItem.Code;
                detail.ItemName = chargeItem.Name;
                detail.Unit = chargeItem.Unit;
                detail.Price = chargeItem.Price;
                detail.Amount = detail.Quantity * chargeItem.Price;
                detail.ChargeCategoryId = chargeItem.ChargeCategoryId;
                detail.IsPackage = (int?)chargeItem.Package;

                // 如果是套餐项目，处理套餐子项
                if (chargeItem.Package == YesNoEnum.Y)
                {
                    var packageItems = chargeItem.ChargeItemPacks.Select(packageItem => new ExaminationPackageItem
                    {
                        ExaminationId = examination.Id,
                        ApplyNo = examination.ApplyNo,
                        ExaminationDetailsId = detail.Id,
                        ItemId = packageItem.Id,
                        ItemCode = packageItem.Code,
                        ItemName = packageItem.Name,
                        Unit = packageItem.Unit,
                        Price = packageItem.Price,
                        Quantity = packageItem.Quantity,
                        Amount = packageItem.Quantity * packageItem.Price,
                        ChargeCategoryId = packageItem.ChargeCategoryId,
                    }).ToList();

                    // 批量插入套餐子项记录
                    await examinationPackageItemRep.InsertRangeAsync(packageItems);
                }
                await examinationDetailsRep.InsertAsync(detail);
            }
            else
            {
                detail.Amount = detail.Quantity * detail.Price;
                await examinationDetailsRep.AsUpdateable(detail)
                    .UpdateColumns(u => new
                    {
                        u.Quantity,
                        u.Amount,
                    })
                    .ExecuteCommandAsync();
            }
        }
        await examinationRep.AsUpdateable(examination)
            .UpdateColumns(u => new
            {
                u.CheckObjective,
                u.ClinicalDiagnosis,
                u.MedicalRecordSummary
            })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除检查 ❌
    /// </summary>
    /// <param name="input">包含要删除的检查记录的输入参数</param>
    /// <returns></returns>
    [DisplayName("删除检查")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost, UnitOfWork]
    public async Task Delete(DeleteExaminationInput input)
    {
        // 获取检查记录，如果不存在则抛出异常
        var entity = await examinationRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (entity.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0002);
        }
        // 假删除检查记录
        await examinationRep.FakeDeleteAsync(entity);

        // 获取并假删除检查明细记录
        var detailsList = await examinationDetailsRep.GetListAsync(u => u.ExaminationId == input.Id);
        await examinationDetailsRep.FakeDeleteAsync(detailsList);

        // 获取并假删除检查套餐项目记录
        var packageItemsList = await examinationPackageItemRep.GetListAsync(u => u.ExaminationId == input.Id);
        await examinationPackageItemRep.FakeDeleteAsync(packageItemsList);

        // 如果需要真删除，可以使用以下代码代替假删除
        // await _examinationRep.DeleteAsync(entity);
    }

    /// <summary>
    /// 根据检查明细ID删除明细 ❌
    /// </summary>
    /// <param name="input">包含要删除的检查明细ID的输入参数</param>
    /// <returns></returns>
    [DisplayName("删除检查明细")]
    [ApiDescriptionSettings(Name = "DeleteDetail"), HttpPost, UnitOfWork]
    public async Task DeleteDetail(DeleteExaminationDetailInput input)
    {
        // 获取检查明细记录，如果不存在则抛出异常
        var detail = await examinationDetailsRep.GetFirstAsync(u => u.Id == input.Id)
                     ?? throw Oops.Oh(ErrorCodeEnum.D1002);

        var mainRecord = await examinationRep.GetFirstAsync(u => u.Id == detail.ExaminationId);
        if (mainRecord.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0002);
        }
        // 假删除检查明细记录
        await examinationDetailsRep.FakeDeleteAsync(detail);

        // 如果需要真删除，可以使用以下代码代替假删除
        // await _examinationDetailsRep.DeleteAsync(detail);
    }

    /// <summary>
    /// 设置申请单状态 🔄
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task SetStatus(SetExaminationStatusInput input)
    {
        var entity = await examinationRep.GetFirstAsync(u => u.Id == input.Id)
                     ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (!MedicalTechConst.ApplyStatusValidTransitions.TryGetValue((int)entity.Status, out var allowed)
            || !allowed.Contains((int)input.Status))
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0004);
        }
        await examinationRep.UpdateAsync(u => new Examination
        {
            Status = input.Status,
        }, u => u.Id == entity.Id);
    }

    /// <summary>
    /// 检查收费 💰
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("检查收费")]
    [ApiDescriptionSettings(Name = "Charge"), HttpPost, UnitOfWork]
    public async Task Charge(ChargeExaminationInput input)
    {
        // 查询主记录及其明细
        var main = await examinationRep.AsQueryable()
                       .Includes(de => de.ExaminationDetails, pack => pack.ExaminationPackageItem)
                       .FirstAsync(u => u.Id == input.Id)
                   ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (main.Status != 1)
        {
            throw Oops.Oh(MedicalTechErrorCodeEnum.MT0006);
        }

        // 构建 DTO 对象
        var dto = main.Adapt<OutpatientChargeDto>();
        dto.BillingType = "Dispose";
        dto.BillingId = main.Id;
        dto.BillingNo = main.ApplyNo;
        dto.TotalAmount = main.ExaminationDetails?.Sum(u => u.Amount);
        dto.InvoiceNumber = input.InvoiceNumber;
        // 构建收费明细
        dto.Details = main.ExaminationDetails.SelectMany(detail =>
        {
            if (detail.IsPackage == 1)
            {
                // 套餐项目
                return detail.ExaminationPackageItem.Select(item => new OutpatientChargeDetailDto
                {
                    ItemId = item.ItemId,
                    ItemCode = item.ItemCode,
                    ItemName = item.ItemName,
                    Unit = item.Unit,
                    Price = item.Price,
                    Quantity = item.Quantity,
                    Amount = item.Amount,
                    BillingId = detail.Id,
                    BillingNo = main.ApplyNo,
                    BillingTime = main.BillingTime,
                    BillingDeptId = main.BillingDeptId,
                    BillingDoctorId = main.BillingDoctorId,
                    ExecuteDeptId = main.ExecuteDeptId,
                    BillingDetailId = item.Id,
                    BillingDoctorName = main.BillingDoctorName,
                    ChargeCategoryId = detail.ChargeCategoryId,
                    PackId = detail.ItemId,
                    PackCode = detail.ItemCode,
                    PackName = detail.ItemName,
                    PackNumber = (short?)(item.Quantity / detail.Quantity),
                    PackPrice = detail.Price,
                });
            }
            else
            {
                // 非套餐项目
                return
                [
                    new() {
                        ItemId = detail.ItemId,
                        ItemCode = detail.ItemCode,
                        ItemName = detail.ItemName,
                        Unit = detail.Unit,
                        Price = detail.Price,
                        Quantity = detail.Quantity,
                        Amount = detail.Amount,
                        BillingId = detail.Id,
                        BillingNo = detail.ApplyNo,
                        BillingTime = main.BillingTime,
                        BillingDeptId = main.BillingDeptId,
                        BillingDoctorId = main.BillingDoctorId,
                        ExecuteDeptId = detail.ExecuteDeptId,
                        BillingDetailId = detail.Id,
                        BillingDoctorName = main.BillingDoctorName,
                        ChargeCategoryId = detail.ChargeCategoryId,
                    }
                ];
            }
        }).ToList();

        // 调用收费接口
        var result = await chargeApi.Add(dto);

        // 更新主记录状态和收费信息
        await examinationRep.UpdateAsync(u => new Examination
        {
            Status = 2,
            ChargeTime = result.ChargeTime,
            ChargeStaffId = result.ChargeStaffId,
            ChargeStaffName = result.ChargeStaffName,
        }, u => u.Id == input.Id);
    }
    /// <summary>
    /// 检查收费 💰
    /// </summary>
    /// <param name="input"></param>
    [DisplayName("检查收费")]
    [ApiDescriptionSettings(IgnoreApi = true)]
    public async Task Charge(ChargeExaminationDto input)
    {
        await Charge(new ChargeExaminationInput
        {
            Id = input.Id, InvoiceNumber = input.InvoiceNumber
        });
    }
}