using Furion.DataValidation;
namespace His.Module.MedicalTech.Service;

/// <summary>
/// 处置表基础输入参数
/// </summary>
public class DisposeBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>
    public virtual string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    public virtual long? RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public virtual long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    public virtual string PatientName { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public virtual long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    public virtual string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public virtual string? ItemName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 生产厂商
    /// </summary>
    public virtual string? Manufacturer { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    public virtual string? Model { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public virtual decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public virtual decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public virtual decimal? Amount { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public virtual long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    public virtual string? FrequencyName { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public virtual int? Days { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    public virtual string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    public virtual string? NationalstandardCode { get; set; }

    /// <summary>
    /// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    /// </summary>
    public virtual int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    public virtual int? IsPackage { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    public virtual int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public virtual DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public virtual long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    public virtual string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public virtual long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    public virtual string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public virtual DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public virtual long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    public virtual string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    public virtual string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public virtual long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    public virtual string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public virtual long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    public virtual string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public virtual DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    public virtual decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    public virtual int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    public virtual DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>
    public virtual long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>
    public virtual string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    public virtual string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    public virtual long? MedicalAdviceId { get; set; }

    /// <summary>
    /// 处方Id
    /// </summary>
    public virtual long? PrescId { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    public virtual long? ChargeCategoryId { get; set; }
}

/// <summary>
/// 处置表分页查询输入参数
/// </summary>
public class PageDisposeInput : BasePageInput
{
    /// <summary>
    /// 申请单号
    /// </summary>
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required(ErrorMessage = "就诊Id不能为空")]
    public long RegisterId { get; set; }

    /// <summary>
    /// 0 门诊 1住院
    /// </summary>
    [Required(ErrorMessage = "门诊住院标志不能为空")]
    public int Flag { get; set; }
}

/// <summary>
/// 处置表增加输入参数
/// </summary>
public class AddDisposeInput
{
    /// <summary>
    /// 申请单号
    /// </summary>
    [MaxLength(64, ErrorMessage = "申请单号字符长度不能超过64")]
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    [Required(ErrorMessage = "就诊Id不能为空")]
    public long? RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [MaxLength(64, ErrorMessage = "就诊流水号字符长度不能超过64")]
    [Required(ErrorMessage = "就诊号不能为空")]
    public string VisitNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    [Required(ErrorMessage = "门诊号不能为空")]
    public string OutpatientNo { get; set; }

    /// <summary>
    /// 就诊卡号
    /// </summary>
    [Required(ErrorMessage = "就诊卡号不能为空")]
    public string CardNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [Required(ErrorMessage = "患者Id不能为空")]
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string PatientName { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    [Required(ErrorMessage = "项目Id不能为空")]
    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [MaxLength(64, ErrorMessage = "项目编码字符长度不能超过64")]
    public string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "项目名称字符长度不能超过64")]
    public string? ItemName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(64, ErrorMessage = "规格字符长度不能超过64")]
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(64, ErrorMessage = "单位字符长度不能超过64")]
    public string? Unit { get; set; }

    /// <summary>
    /// 生产厂商
    /// </summary>
    [MaxLength(256, ErrorMessage = "生产厂商字符长度不能超过256")]
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    [MaxLength(100, ErrorMessage = "型号字符长度不能超过100")]
    public string? Model { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    [Required(ErrorMessage = "数量不能为空")]
    [DataValidation(ValidationTypes.PositiveNumber, ErrorMessage = "数量必须大于0")]
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "频次名称字符长度不能超过100")]
    public string? FrequencyName { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int? Days { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国标编码字符长度不能超过100")]
    public string? NationalstandardCode { get; set; }

    /// <summary>
    /// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注字符长度不能超过256")]
    public string? Remark { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    public int? IsPackage { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    public int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "开单科室名称字符长度不能超过64")]
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "开单医生名称字符长度不能超过64")]
    public string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    [Required(ErrorMessage = "执行科室不能为空")]
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "执行科室名称字符长度不能超过64")]
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    [MaxLength(100, ErrorMessage = "执行科室地址字符长度不能超过100")]
    public string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "执行医生名称字符长度不能超过64")]
    public string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "收费人员名称字符长度不能超过64")]
    public string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    public decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    public int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    public DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>
    public long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "自付比例审核人员名称字符长度不能超过64")]
    public string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    public string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    public long? MedicalAdviceId { get; set; }

    /// <summary>
    /// 处方Id
    /// </summary>
    public long? PrescId { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    public long? ChargeCategoryId { get; set; }
}

/// <summary>
/// 处置表删除输入参数
/// </summary>
public class DeleteDisposeInput : BaseIdInput
{
}

/// <summary>
/// 处置表更新输入参数
/// </summary>
public class UpdateDisposeInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 申请单号
    /// </summary>
    [MaxLength(64, ErrorMessage = "申请单号字符长度不能超过64")]
    public string ApplyNo { get; set; }

    /// <summary>
    /// 就诊Id
    /// </summary>
    public long? RegisterId { get; set; }

    /// <summary>
    /// 就诊流水号
    /// </summary>
    [MaxLength(64, ErrorMessage = "就诊流水号字符长度不能超过64")]
    public string VisitNo { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [MaxLength(64, ErrorMessage = "患者姓名字符长度不能超过64")]
    public string PatientName { get; set; }

    /// <summary>
    /// 项目Id
    /// </summary>
    public long? ItemId { get; set; }

    /// <summary>
    /// 项目编码
    /// </summary>
    [MaxLength(64, ErrorMessage = "项目编码字符长度不能超过64")]
    public string? ItemCode { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "项目名称字符长度不能超过64")]
    public string? ItemName { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(64, ErrorMessage = "规格字符长度不能超过64")]
    public string? Spec { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(64, ErrorMessage = "单位字符长度不能超过64")]
    public string? Unit { get; set; }

    /// <summary>
    /// 生产厂商
    /// </summary>
    [MaxLength(256, ErrorMessage = "生产厂商字符长度不能超过256")]
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    [MaxLength(100, ErrorMessage = "型号字符长度不能超过100")]
    public string? Model { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 频次Id
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "频次名称字符长度不能超过100")]
    public string? FrequencyName { get; set; }

    /// <summary>
    /// 天数
    /// </summary>
    public int? Days { get; set; }

    /// <summary>
    /// 国家医保编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 国标编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国标编码字符长度不能超过100")]
    public string? NationalstandardCode { get; set; }

    /// <summary>
    /// 状态 1未收费2已收费3执行4审核5已退费 6作废7预扣费
    /// </summary>
    public int? Status { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注字符长度不能超过256")]
    public string? Remark { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    public int? IsPackage { get; set; }

    /// <summary>
    /// 门诊住院标识 0门诊 1住院
    /// </summary>
    public int? Flag { get; set; }

    /// <summary>
    /// 开单时间
    /// </summary>
    public DateTime? BillingTime { get; set; }

    /// <summary>
    /// 开单科室Id
    /// </summary>
    public long? BillingDeptId { get; set; }

    /// <summary>
    /// 开单科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "开单科室名称字符长度不能超过64")]
    public string? BillingDeptName { get; set; }

    /// <summary>
    /// 开单医生Id
    /// </summary>
    public long? BillingDoctorId { get; set; }

    /// <summary>
    /// 开单医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "开单医生名称字符长度不能超过64")]
    public string? BillingDoctorName { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime? ExecuteTime { get; set; }

    /// <summary>
    /// 执行科室Id
    /// </summary>
    public long? ExecuteDeptId { get; set; }

    /// <summary>
    /// 执行科室名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "执行科室名称字符长度不能超过64")]
    public string? ExecuteDeptName { get; set; }

    /// <summary>
    /// 执行科室地址
    /// </summary>
    [MaxLength(100, ErrorMessage = "执行科室地址字符长度不能超过100")]
    public string? ExecuteDeptAddress { get; set; }

    /// <summary>
    /// 执行医生Id
    /// </summary>
    public long? ExecuteDoctorId { get; set; }

    /// <summary>
    /// 执行医生名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "执行医生名称字符长度不能超过64")]
    public string? ExecuteDoctorName { get; set; }

    /// <summary>
    /// 收费人员Id
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费人员名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "收费人员名称字符长度不能超过64")]
    public string? ChargeStaffName { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 自付比例
    /// </summary>
    public decimal? SelfPayRatio { get; set; }

    /// <summary>
    /// 自付比例是否审核 1审核 2不审核
    /// </summary>
    public int? IsRatioAudit { get; set; }

    /// <summary>
    /// 自付比例审核时间
    /// </summary>
    public DateTime? RatioAuditTime { get; set; }

    /// <summary>
    /// 自付比例审核人员Id
    /// </summary>
    public long? RatioAuditStaffId { get; set; }

    /// <summary>
    /// 自付比例审核人员名称
    /// </summary>
    [MaxLength(64, ErrorMessage = "自付比例审核人员名称字符长度不能超过64")]
    public string? RatioAuditStaffName { get; set; }

    /// <summary>
    /// 医生签名
    /// </summary>
    public string? DoctorSign { get; set; }

    /// <summary>
    /// 医嘱Id
    /// </summary>
    public long? MedicalAdviceId { get; set; }

    /// <summary>
    /// 处方Id
    /// </summary>
    public long? PrescId { get; set; }

    /// <summary>
    /// 收费类别Id
    /// </summary>
    public long? ChargeCategoryId { get; set; }
}

/// <summary>
/// 处置表主键查询输入参数
/// </summary>
public class QueryByIdDisposeInput : BaseIdInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataDisposeInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 状态修改输入参数
/// </summary>
public class SetDisposeStatusInput : BaseIdInput
{
    /// <summary>
    /// 状态 字典 ApplyStatus
    /// </summary>
    [Required(ErrorMessage = "状态不能为空")]
    public int Status { get; set; }
}

/// <summary>
/// 处置收费输入参数
/// </summary>
public class ChargeDisposeInput : BaseIdInput
{
    /// <summary>
    /// 发票号
    /// </summary>
    public string? InvoiceNumber { get; set; }
}