global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using Admin.NET.Core;
global using Furion;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using His.Module.MedicalTech.Const;
global using His.Module.MedicalTech.Entity;
global using Mapster;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.Extensions.DependencyInjection;
global using SqlSugar;
