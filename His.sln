
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Application", "Admin.NET.Application\Admin.NET.Application.csproj", "{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Core", "Admin.NET.Core\Admin.NET.Core.csproj", "{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Web.Core", "Admin.NET.Web.Core\Admin.NET.Web.Core.csproj", "{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Web.Entry", "Admin.NET.Web.Entry\Admin.NET.Web.Entry.csproj", "{11EA630B-4600-4236-A117-CE6C6CD67586}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Admin.NET.Test", "Admin.NET.Test\Admin.NET.Test.csproj", "{57350E6A-B5A0-452C-9FD4-C69617C6DA30}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{662E0B8E-F23E-4C7D-80BD-CAA5707503CC}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Plugins", "Plugins", "{76F70D22-8D53-468E-A3B6-1704666A1D71}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.GoView", "Plugins\Admin.NET.Plugin.GoView\Admin.NET.Plugin.GoView.csproj", "{C4A288D5-0FAA-4F43-9072-B97635D7871D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.DingTalk", "Plugins\Admin.NET.Plugin.DingTalk\Admin.NET.Plugin.DingTalk.csproj", "{F6A002AD-CF7F-4771-8597-F12A50A93DAA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.ReZero", "Plugins\Admin.NET.Plugin.ReZero\Admin.NET.Plugin.ReZero.csproj", "{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.ApprovalFlow", "Plugins\Admin.NET.Plugin.ApprovalFlow\Admin.NET.Plugin.ApprovalFlow.csproj", "{902A91A7-5EF0-4A63-BC2C-9B783DC00880}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Admin.NET.Plugin.K3Cloud", "Plugins\Admin.NET.Plugin.K3Cloud\Admin.NET.Plugin.K3Cloud.csproj", "{72EB89AB-15F7-4F85-88DB-7C2EF7C3D588}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Admin.NET.Plugin.WorkWeixin", "Plugins\Admin.NET.Plugin.WorkWeixin\Admin.NET.Plugin.WorkWeixin.csproj", "{BFE4764F-1FF8-47A7-B4AD-085F7D8CD6C4}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Modules", "Modules", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		His.Module.Registration\His.Module.Registration.csproj = His.Module.Registration\His.Module.Registration.csproj
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Patient", "His.Module.Patient\His.Module.Patient.csproj", "{30FB6313-4754-4697-86E4-EC9B9D761667}"
	ProjectSection(ProjectDependencies) = postProject
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC} = {3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Patient.Api", "His.Module.Patient.Api\His.Module.Patient.Api.csproj", "{C516E40E-37E2-4E6E-B1C2-1E40BEB52C29}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Registration.Api", "His.Module.Registration.Api\His.Module.Registration.Api.csproj", "{A847796B-EBE4-4622-8D68-5F75E857A81E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.OutpatientDoctor", "His.Module.OutpatientDoctor\His.Module.OutpatientDoctor.csproj", "{9ACC9FA3-F698-421F-A4DE-F4541BAE27D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Inpatient", "His.Module.Inpatient\His.Module.Inpatient.csproj", "{8C23E920-15A5-4B47-B6DC-81C8C98E5F12}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.InpatientDoctor", "His.Module.InpatientDoctor\His.Module.InpatientDoctor.csproj", "{D0818E77-6DF5-4C0A-A2E0-022B8B78F577}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Pharmacy", "His.Module.Pharmacy\His.Module.Pharmacy.csproj", "{1370382D-6791-4C0C-9F6D-171E75677A3C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.MedicalTech", "His.Module.MedicalTech\His.Module.MedicalTech.csproj", "{65D6DAD2-7C3F-44AA-90BF-832AA77C6181}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Financial", "His.Module.Financial\His.Module.Financial.csproj", "{8818E88B-CCC2-4FB7-88DE-4EDDC9014979}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Report", "His.Module.Report\His.Module.Report.csproj", "{0FBC02F9-D5B7-434C-888E-ED8D684DB23C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Financial.Api", "His.Module.Financial.Api\His.Module.Financial.Api.csproj", "{C921C435-72BE-40C8-98A7-00B839288661}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Nursing", "His.Module.Nursing\His.Module.Nursing.csproj", "{81675AD9-A8A3-43EB-BC2B-1C03DDDF6FE0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Insurance", "His.Module.Insurance\His.Module.Insurance.csproj", "{BACBD8F3-E5C5-4589-83A1-B6D904860D35}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Pharmacy.Api", "His.Module.Pharmacy.Api\His.Module.Pharmacy.Api.csproj", "{ED4542B0-A57A-413E-B0E4-84E62E7D37FF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Shared", "His.Module.Shared\His.Module.Shared.csproj", "{16D2231E-CB0C-421E-9937-902ADED1A433}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Shared.Api", "His.Module.Shared.Api\His.Module.Shared.Api.csproj", "{3293AE7A-F90F-4243-8782-3EA1EC7E7C85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Inpatient.Api", "His.Module.Inpatient.Api\His.Module.Inpatient.Api.csproj", "{23A7631C-AAB6-44C8-8DEF-410FD8CA9102}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.OutpatientDoctor.Api", "His.Module.OutpatientDoctor.Api\His.Module.OutpatientDoctor.Api.csproj", "{98587F9D-C9F5-42E7-A057-CAC6D7D62AD5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.Registration", "His.Module.Registration\His.Module.Registration.csproj", "{8446ED65-D9F4-491C-BADF-83FB1DD2B821}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.InsuranceSettlementForLiaocheng", "His.Module.InsuranceSettlementForLiaocheng\His.Module.InsuranceSettlementForLiaocheng.csproj", "{5ADC9C63-DFFA-4417-AC20-58C60D9C3BEA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "His.Module.MedicalTech.Api", "His.Module.MedicalTech.Api\His.Module.MedicalTech.Api.csproj", "{3AD9CA35-0855-4072-816D-73BE993FF69D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3F5AEC5-ACEE-4109-94E3-3F981DC18268}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AD1A3ED-ED11-479D-BE32-6589D98A9ADC}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A42A864-A69E-40F7-975E-F2FA36E7DFEE}.Release|Any CPU.Build.0 = Release|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11EA630B-4600-4236-A117-CE6C6CD67586}.Release|Any CPU.Build.0 = Release|Any CPU
		{57350E6A-B5A0-452C-9FD4-C69617C6DA30}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57350E6A-B5A0-452C-9FD4-C69617C6DA30}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57350E6A-B5A0-452C-9FD4-C69617C6DA30}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4A288D5-0FAA-4F43-9072-B97635D7871D}.Release|Any CPU.Build.0 = Release|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106}.Release|Any CPU.Build.0 = Release|Any CPU
		{902A91A7-5EF0-4A63-BC2C-9B783DC00880}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{902A91A7-5EF0-4A63-BC2C-9B783DC00880}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{902A91A7-5EF0-4A63-BC2C-9B783DC00880}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{902A91A7-5EF0-4A63-BC2C-9B783DC00880}.Release|Any CPU.Build.0 = Release|Any CPU
		{72EB89AB-15F7-4F85-88DB-7C2EF7C3D588}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{72EB89AB-15F7-4F85-88DB-7C2EF7C3D588}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{72EB89AB-15F7-4F85-88DB-7C2EF7C3D588}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{72EB89AB-15F7-4F85-88DB-7C2EF7C3D588}.Release|Any CPU.Build.0 = Release|Any CPU
		{BFE4764F-1FF8-47A7-B4AD-085F7D8CD6C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BFE4764F-1FF8-47A7-B4AD-085F7D8CD6C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BFE4764F-1FF8-47A7-B4AD-085F7D8CD6C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BFE4764F-1FF8-47A7-B4AD-085F7D8CD6C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{30FB6313-4754-4697-86E4-EC9B9D761667}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30FB6313-4754-4697-86E4-EC9B9D761667}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30FB6313-4754-4697-86E4-EC9B9D761667}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30FB6313-4754-4697-86E4-EC9B9D761667}.Release|Any CPU.Build.0 = Release|Any CPU
		{C516E40E-37E2-4E6E-B1C2-1E40BEB52C29}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C516E40E-37E2-4E6E-B1C2-1E40BEB52C29}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C516E40E-37E2-4E6E-B1C2-1E40BEB52C29}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C516E40E-37E2-4E6E-B1C2-1E40BEB52C29}.Release|Any CPU.Build.0 = Release|Any CPU
		{A847796B-EBE4-4622-8D68-5F75E857A81E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A847796B-EBE4-4622-8D68-5F75E857A81E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A847796B-EBE4-4622-8D68-5F75E857A81E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A847796B-EBE4-4622-8D68-5F75E857A81E}.Release|Any CPU.Build.0 = Release|Any CPU
		{9ACC9FA3-F698-421F-A4DE-F4541BAE27D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9ACC9FA3-F698-421F-A4DE-F4541BAE27D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9ACC9FA3-F698-421F-A4DE-F4541BAE27D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9ACC9FA3-F698-421F-A4DE-F4541BAE27D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{8C23E920-15A5-4B47-B6DC-81C8C98E5F12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8C23E920-15A5-4B47-B6DC-81C8C98E5F12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8C23E920-15A5-4B47-B6DC-81C8C98E5F12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8C23E920-15A5-4B47-B6DC-81C8C98E5F12}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0818E77-6DF5-4C0A-A2E0-022B8B78F577}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0818E77-6DF5-4C0A-A2E0-022B8B78F577}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0818E77-6DF5-4C0A-A2E0-022B8B78F577}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0818E77-6DF5-4C0A-A2E0-022B8B78F577}.Release|Any CPU.Build.0 = Release|Any CPU
		{1370382D-6791-4C0C-9F6D-171E75677A3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1370382D-6791-4C0C-9F6D-171E75677A3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1370382D-6791-4C0C-9F6D-171E75677A3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1370382D-6791-4C0C-9F6D-171E75677A3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{65D6DAD2-7C3F-44AA-90BF-832AA77C6181}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{65D6DAD2-7C3F-44AA-90BF-832AA77C6181}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{65D6DAD2-7C3F-44AA-90BF-832AA77C6181}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{65D6DAD2-7C3F-44AA-90BF-832AA77C6181}.Release|Any CPU.Build.0 = Release|Any CPU
		{8818E88B-CCC2-4FB7-88DE-4EDDC9014979}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8818E88B-CCC2-4FB7-88DE-4EDDC9014979}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8818E88B-CCC2-4FB7-88DE-4EDDC9014979}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8818E88B-CCC2-4FB7-88DE-4EDDC9014979}.Release|Any CPU.Build.0 = Release|Any CPU
		{0FBC02F9-D5B7-434C-888E-ED8D684DB23C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0FBC02F9-D5B7-434C-888E-ED8D684DB23C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0FBC02F9-D5B7-434C-888E-ED8D684DB23C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0FBC02F9-D5B7-434C-888E-ED8D684DB23C}.Release|Any CPU.Build.0 = Release|Any CPU
		{C921C435-72BE-40C8-98A7-00B839288661}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C921C435-72BE-40C8-98A7-00B839288661}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C921C435-72BE-40C8-98A7-00B839288661}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C921C435-72BE-40C8-98A7-00B839288661}.Release|Any CPU.Build.0 = Release|Any CPU
		{81675AD9-A8A3-43EB-BC2B-1C03DDDF6FE0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81675AD9-A8A3-43EB-BC2B-1C03DDDF6FE0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81675AD9-A8A3-43EB-BC2B-1C03DDDF6FE0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81675AD9-A8A3-43EB-BC2B-1C03DDDF6FE0}.Release|Any CPU.Build.0 = Release|Any CPU
		{BACBD8F3-E5C5-4589-83A1-B6D904860D35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BACBD8F3-E5C5-4589-83A1-B6D904860D35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BACBD8F3-E5C5-4589-83A1-B6D904860D35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BACBD8F3-E5C5-4589-83A1-B6D904860D35}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED4542B0-A57A-413E-B0E4-84E62E7D37FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED4542B0-A57A-413E-B0E4-84E62E7D37FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED4542B0-A57A-413E-B0E4-84E62E7D37FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED4542B0-A57A-413E-B0E4-84E62E7D37FF}.Release|Any CPU.Build.0 = Release|Any CPU
		{16D2231E-CB0C-421E-9937-902ADED1A433}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16D2231E-CB0C-421E-9937-902ADED1A433}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16D2231E-CB0C-421E-9937-902ADED1A433}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16D2231E-CB0C-421E-9937-902ADED1A433}.Release|Any CPU.Build.0 = Release|Any CPU
		{3293AE7A-F90F-4243-8782-3EA1EC7E7C85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3293AE7A-F90F-4243-8782-3EA1EC7E7C85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3293AE7A-F90F-4243-8782-3EA1EC7E7C85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3293AE7A-F90F-4243-8782-3EA1EC7E7C85}.Release|Any CPU.Build.0 = Release|Any CPU
	
		{23A7631C-AAB6-44C8-8DEF-410FD8CA9102}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{23A7631C-AAB6-44C8-8DEF-410FD8CA9102}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{23A7631C-AAB6-44C8-8DEF-410FD8CA9102}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{23A7631C-AAB6-44C8-8DEF-410FD8CA9102}.Release|Any CPU.Build.0 = Release|Any CPU
		{98587F9D-C9F5-42E7-A057-CAC6D7D62AD5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98587F9D-C9F5-42E7-A057-CAC6D7D62AD5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98587F9D-C9F5-42E7-A057-CAC6D7D62AD5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{98587F9D-C9F5-42E7-A057-CAC6D7D62AD5}.Release|Any CPU.Build.0 = Release|Any CPU

		{3E0F9A3D-3E90-F324-6830-C2A9F8E60B59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E0F9A3D-3E90-F324-6830-C2A9F8E60B59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E0F9A3D-3E90-F324-6830-C2A9F8E60B59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E0F9A3D-3E90-F324-6830-C2A9F8E60B59}.Release|Any CPU.Build.0 = Release|Any CPU
		{8446ED65-D9F4-491C-BADF-83FB1DD2B821}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8446ED65-D9F4-491C-BADF-83FB1DD2B821}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8446ED65-D9F4-491C-BADF-83FB1DD2B821}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8446ED65-D9F4-491C-BADF-83FB1DD2B821}.Release|Any CPU.Build.0 = Release|Any CPU
		{5ADC9C63-DFFA-4417-AC20-58C60D9C3BEA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5ADC9C63-DFFA-4417-AC20-58C60D9C3BEA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5ADC9C63-DFFA-4417-AC20-58C60D9C3BEA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5ADC9C63-DFFA-4417-AC20-58C60D9C3BEA}.Release|Any CPU.Build.0 = Release|Any CPU
		{3AD9CA35-0855-4072-816D-73BE993FF69D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3AD9CA35-0855-4072-816D-73BE993FF69D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3AD9CA35-0855-4072-816D-73BE993FF69D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3AD9CA35-0855-4072-816D-73BE993FF69D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{C4A288D5-0FAA-4F43-9072-B97635D7871D} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{F6A002AD-CF7F-4771-8597-F12A50A93DAA} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{04AB2E76-DE8B-4EFD-9F48-F8D4C0993106} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{902A91A7-5EF0-4A63-BC2C-9B783DC00880} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{72EB89AB-15F7-4F85-88DB-7C2EF7C3D588} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{BFE4764F-1FF8-47A7-B4AD-085F7D8CD6C4} = {76F70D22-8D53-468E-A3B6-1704666A1D71}
		{30FB6313-4754-4697-86E4-EC9B9D761667} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{C516E40E-37E2-4E6E-B1C2-1E40BEB52C29} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{A847796B-EBE4-4622-8D68-5F75E857A81E} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{9ACC9FA3-F698-421F-A4DE-F4541BAE27D3} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{8C23E920-15A5-4B47-B6DC-81C8C98E5F12} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{D0818E77-6DF5-4C0A-A2E0-022B8B78F577} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{1370382D-6791-4C0C-9F6D-171E75677A3C} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{65D6DAD2-7C3F-44AA-90BF-832AA77C6181} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{8818E88B-CCC2-4FB7-88DE-4EDDC9014979} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0FBC02F9-D5B7-434C-888E-ED8D684DB23C} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{C921C435-72BE-40C8-98A7-00B839288661} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{81675AD9-A8A3-43EB-BC2B-1C03DDDF6FE0} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{BACBD8F3-E5C5-4589-83A1-B6D904860D35} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{ED4542B0-A57A-413E-B0E4-84E62E7D37FF} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{16D2231E-CB0C-421E-9937-902ADED1A433} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3293AE7A-F90F-4243-8782-3EA1EC7E7C85} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{23A7631C-AAB6-44C8-8DEF-410FD8CA9102} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{98587F9D-C9F5-42E7-A057-CAC6D7D62AD5} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3E0F9A3D-3E90-F324-6830-C2A9F8E60B59} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{8446ED65-D9F4-491C-BADF-83FB1DD2B821} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{5ADC9C63-DFFA-4417-AC20-58C60D9C3BEA} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3AD9CA35-0855-4072-816D-73BE993FF69D} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5CD801D7-984A-4F5C-8FA2-211B7A5EA9F3}
	EndGlobalSection
EndGlobal
