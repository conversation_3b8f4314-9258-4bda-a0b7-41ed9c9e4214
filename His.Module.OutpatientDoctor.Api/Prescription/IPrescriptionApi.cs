using His.Module.OutpatientDoctor.Api.Prescription.Dto;

namespace His.Module.OutpatientDoctor.Api.Prescription;

public interface IPrescriptionApi
{  
    //public Task<Boolean> PrescriptionSendDrug(List<PrescriptionSendDrugDto> dto);
    public Task<bool> PrescriptionSendDrug(PrescriptionSendDrugDto dto);
    /// <summary>
    /// 退药
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    public Task<bool> PrescriptionRefundDrug(PrescriptionRefundDrugDto dto);
    public Task<PrescriptionMainDto> GetPrescription(long prescriptionId);
  
}       