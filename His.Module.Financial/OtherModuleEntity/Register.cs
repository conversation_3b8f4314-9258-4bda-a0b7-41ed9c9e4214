namespace His.Module.Financial.OtherModuleEntity;

/// <summary>
/// 挂号记录表
/// </summary>
[Tenant("1300000000005")]
[SugarTable("register", "挂号记录表")]
public class Register : EntityTenantBaseData
{
    /// <summary>
    /// 就诊号
    /// </summary>
    [SugarColumn(ColumnName = "visit_no", ColumnDescription = "就诊号", Length = 64)]
    public virtual string VisitNo { get; set; }

    /// <summary>
    /// 卡号
    /// </summary>
    [SugarColumn(ColumnName = "card_no", ColumnDescription = "卡号", Length = 64)]
    public virtual string CardNo { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    [SugarColumn(ColumnName = "id_card_no", ColumnDescription = "身份证号", Length = 64)]
    public virtual string IdCardNo { get; set; }

    /// <summary>
    /// 就诊次数
    /// </summary>
    [SugarColumn(ColumnName = "visit_num", ColumnDescription = "就诊次数")]
    public virtual int? VisitNum { get; set; }

    /// <summary>
    /// 时间段id
    /// </summary>
    [SugarColumn(ColumnName = "time_period_id", ColumnDescription = "时间段id")]
    public virtual long? TimePeriodId { get; set; }

    /// <summary>
    /// 号别id
    /// </summary>
    [SugarColumn(ColumnName = "reg_category_id", ColumnDescription = "号别id")]
    public virtual long? RegCategoryId { get; set; }

    /// <summary>
    /// 患者Id
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者Id")]
    public virtual long PatientId { get; set; }

    /// <summary>
    /// 患者姓名
    /// </summary>
    [SugarColumn(ColumnName = "patient_name", ColumnDescription = "患者姓名", Length = 64)]
    public virtual string PatientName { get; set; }

    /// <summary>
    /// 姓名拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "姓名拼音码", Length = 32)]
    public virtual string PinyinCode { get; set; }

    /// <summary>
    /// 姓名五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "姓名五笔码", Length = 32)]
    public virtual string WubiCode { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [SugarColumn(ColumnName = "sex", ColumnDescription = "性别")]
    public virtual GenderEnum Sex { get; set; }

    /// <summary>
    /// 年龄
    /// </summary>
    [SugarColumn(ColumnName = "age", ColumnDescription = "年龄")]
    public virtual int? Age { get; set; }

    /// <summary>
    /// 年龄单位
    /// </summary>
    [SugarColumn(ColumnName = "age_unit", ColumnDescription = "年龄单位", Length = 32)]
    public virtual string AgeUnit { get; set; }

    /// <summary>
    /// 出生日期
    /// </summary>
    [SugarColumn(ColumnName = "birthday", ColumnDescription = "出生日期")]
    public virtual DateTime? Birthday { get; set; }

    /// <summary>
    /// 证件类型
    /// </summary>
    [SugarColumn(ColumnName = "card_type", ColumnDescription = "证件类型", Length = 32)]
    public virtual CardTypeEnum? CardType { get; set; }

    /// <summary>
    /// 身份证号
    /// </summary>
    [SugarColumn(ColumnName = "id_card_num", ColumnDescription = "身份证号", Length = 32)]
    public virtual string IdCardNum { get; set; }

    /// <summary>
    /// 职业
    /// </summary>
    [SugarColumn(ColumnName = "occupation", ColumnDescription = "职业", Length = 32)]
    public virtual string Occupation { get; set; }

    /// <summary>
    /// 费别id
    /// </summary>
    [SugarColumn(ColumnName = "fee_id", ColumnDescription = "费别id")]
    public virtual long? FeeId { get; set; }

    /// <summary>
    /// 保险号码
    /// </summary>
    [SugarColumn(ColumnName = "insurance_num", ColumnDescription = "保险号码", Length = 64)]
    public virtual string InsuranceNum { get; set; }

    /// <summary>
    /// 保险类型
    /// </summary>
    [SugarColumn(ColumnName = "insurance_type", ColumnDescription = "保险类型", Length = 16)]
    public virtual string InsuranceType { get; set; }

    /// <summary>
    /// 合同单位id
    /// </summary>
    [SugarColumn(ColumnName = "contract_unit_id", ColumnDescription = "合同单位id")]
    public virtual long? ContractUnitId { get; set; }

    /// <summary>
    /// 就诊类型 初诊|复诊
    /// </summary>
    [SugarColumn(ColumnName = "visit_type", ColumnDescription = "就诊类型 初诊|复诊")]
    public virtual short? VisitType { get; set; }

    /// <summary>
    /// 就诊科室id
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "就诊科室id")]
    public virtual long? DeptId { get; set; }

    /// <summary>
    /// 医生id
    /// </summary>
    [SugarColumn(ColumnName = "doctor_id", ColumnDescription = "医生id")]
    public virtual long? DoctorId { get; set; }

    /// <summary>
    /// 挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "挂号状态 0-挂号 1-就诊 2-结束就诊 3 转诊 4退号 5医保登记失败")]
    public virtual int? Status { get; set; }

    /// <summary>
    /// 症状
    /// </summary>
    [SugarColumn(ColumnName = "symptom", ColumnDescription = "症状", Length = 128)]
    public virtual string Symptom { get; set; }

    /// <summary>
    /// 挂号费
    /// </summary>
    [SugarColumn(ColumnName = "registration_fee", ColumnDescription = "挂号费", Length = 16, DecimalDigits = 4)]
    public virtual decimal? RegistrationFee { get; set; }

    /// <summary>
    /// 诊疗费
    /// </summary>
    [SugarColumn(ColumnName = "consultation_fee", ColumnDescription = "诊疗费", Length = 16, DecimalDigits = 4)]
    public virtual decimal? ConsultationFee { get; set; }

    /// <summary>
    /// 其他费用
    /// </summary>
    [SugarColumn(ColumnName = "other_fee", ColumnDescription = "其他费用", Length = 16, DecimalDigits = 4)]
    public virtual decimal? OtherFee { get; set; }

    /// <summary>
    /// 实收费用
    /// </summary>
    [SugarColumn(ColumnName = "actual_charge_fee", ColumnDescription = "实收费用", Length = 16, DecimalDigits = 4)]
    public virtual decimal? ActualChargeFee { get; set; }

    /// <summary>
    /// 退号时间
    /// </summary>
    [SugarColumn(ColumnName = "refund_num_time", ColumnDescription = "退号时间")]
    public virtual DateTime? RefundNumTime { get; set; }

    /// <summary>
    /// 退号人id
    /// </summary>
    [SugarColumn(ColumnName = "refund_num_id", ColumnDescription = "退号人id")]
    public virtual long? RefundNumId { get; set; }

    /// <summary>
    /// 就诊卡id
    /// </summary>
    [SugarColumn(ColumnName = "card_id", ColumnDescription = "就诊卡id")]
    public virtual long? CardId { get; set; }

    /// <summary>
    /// 收费主表id
    /// </summary>
    [SugarColumn(ColumnName = "charge_main_id", ColumnDescription = "收费主表id")]
    public virtual long? ChargeMainId { get; set; }

    /// <summary>
    /// 预约流水号
    /// </summary>
    [SugarColumn(ColumnName = "app_serial_num", ColumnDescription = "预约流水号")]
    public virtual long? AppSerialNum { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 256)]
    public virtual string Remark { get; set; }

    /// <summary>
    /// 民族
    /// </summary>
    [SugarColumn(ColumnName = "nation", ColumnDescription = "民族", Length = 32)]
    public virtual string Nation { get; set; }

    /// <summary>
    /// 籍贯省
    /// </summary>
    [SugarColumn(ColumnName = "native_place_province", ColumnDescription = "籍贯省")]
    public virtual int? NativePlaceProvince { get; set; }

    /// <summary>
    /// 籍贯市
    /// </summary>
    [SugarColumn(ColumnName = "native_place_city", ColumnDescription = "籍贯市")]
    public virtual int? NativePlaceCity { get; set; }

    /// <summary>
    /// 籍贯县
    /// </summary>
    [SugarColumn(ColumnName = "native_place_county", ColumnDescription = "籍贯县")]
    public virtual int? NativePlaceCounty { get; set; }

    /// <summary>
    /// 现居住地省
    /// </summary>
    [SugarColumn(ColumnName = "residence_province", ColumnDescription = "现居住地省")]
    public virtual int? ResidenceProvince { get; set; }

    /// <summary>
    /// 现居住地市
    /// </summary>
    [SugarColumn(ColumnName = "residence_city", ColumnDescription = "现居住地市")]
    public virtual int? ResidenceCity { get; set; }

    /// <summary>
    /// 现居住地县
    /// </summary>
    [SugarColumn(ColumnName = "residence_county", ColumnDescription = "现居住地县")]
    public virtual int? ResidenceCounty { get; set; }

    /// <summary>
    /// 联系人姓名
    /// </summary>
    [SugarColumn(ColumnName = "contact_name", ColumnDescription = "联系人姓名", Length = 32)]
    public virtual string ContactName { get; set; }

    /// <summary>
    /// 联系人电话号码
    /// </summary>
    [SugarColumn(ColumnName = "contact_phone", ColumnDescription = "联系人电话号码", Length = 16)]
    public virtual string ContactPhone { get; set; }

    /// <summary>
    /// 医保卡余额
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_card_balance", ColumnDescription = "医保卡余额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? MedInsCardBalance { get; set; }

    /// <summary>
    /// 个人账户信息
    /// </summary>
    [SugarColumn(ColumnName = "personal_account_info", ColumnDescription = "个人账户信息", Length = 0)]
    public virtual string PersonalAccountInfo { get; set; }

    /// <summary>
    /// 医保个人编号
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_personal_num", ColumnDescription = "医保个人编号", Length = 64)]
    public virtual string MedInsPersonalNum { get; set; }

    /// <summary>
    /// 医保类型
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_type", ColumnDescription = "医保类型")]
    public virtual int? MedInsType { get; set; }

    /// <summary>
    /// 医保统筹区号
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_area_code", ColumnDescription = "医保统筹区号", Length = 16)]
    public virtual string MedInsAreaCode { get; set; }

    /// <summary>
    /// 医保就诊编号
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_reg_num", ColumnDescription = "医保就诊编号", Length = 128)]
    public virtual string MedInsRegNum { get; set; }

    /// <summary>
    /// 医保支付方式 0:门诊统筹 1：个人支付
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_pay_type", ColumnDescription = "医保支付方式 0:门诊统筹 1：个人支付", Length = 16)]
    public virtual string MedInsPayType { get; set; }

    /// <summary>
    /// 结算病种id
    /// </summary>
    [SugarColumn(ColumnName = "settle_disease_type_id", ColumnDescription = "结算病种id")]
    public virtual long? SettleDiseaseTypeId { get; set; }

    /// <summary>
    /// 首次就诊科室id
    /// </summary>
    [SugarColumn(ColumnName = "first_dept_id", ColumnDescription = "首次就诊科室id")]
    public virtual long? FirstDeptId { get; set; }

    /// <summary>
    /// 首次就诊医生id
    /// </summary>
    [SugarColumn(ColumnName = "first_doctor_id", ColumnDescription = "首次就诊医生id")]
    public virtual long? FirstDoctorId { get; set; }

    /// <summary>
    /// 是否无卡 0否 1是
    /// </summary>
    [SugarColumn(ColumnName = "is_no_card", ColumnDescription = "是否无卡 0否 1是")]
    public virtual YesNoEnum? IsNoCard { get; set; }

    /// <summary>
    /// 险种标志
    /// </summary>
    [SugarColumn(ColumnName = "insurance_sign", ColumnDescription = "险种标志", Length = 4)]
    public virtual string InsuranceSign { get; set; }

    /// <summary>
    /// 门诊号 第一次就诊的流水号
    /// </summary>
    [SugarColumn(ColumnName = "outpatient_no", ColumnDescription = "门诊号 第一次就诊的流水号")]
    public virtual string? OutpatientNo { get; set; }

    /// <summary>
    /// 就医类别
    /// </summary>
    [SugarColumn(ColumnName = "med_treat_category", ColumnDescription = "就医类别", Length = 16)]
    public virtual string MedTreatCategory { get; set; }

    /// <summary>
    /// 发病日期
    /// </summary>
    [SugarColumn(ColumnName = "onset_date", ColumnDescription = "发病日期")]
    public virtual DateTime? OnsetDate { get; set; }

    /// <summary>
    /// 诊断编码
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_code", ColumnDescription = "诊断编码", Length = 128)]
    public virtual string DiagnosticCode { get; set; }

    /// <summary>
    /// 诊断名称
    /// </summary>
    [SugarColumn(ColumnName = "diagnostic_name", ColumnDescription = "诊断名称", Length = 128)]
    public virtual string DiagnosticName { get; set; }

    /// <summary>
    /// 病种代码
    /// </summary>
    [SugarColumn(ColumnName = "disease_type_code", ColumnDescription = "病种代码", Length = 32)]
    public virtual string DiseaseTypeCode { get; set; }

    /// <summary>
    /// 病种名称
    /// </summary>
    [SugarColumn(ColumnName = "disease_type_name", ColumnDescription = "病种名称", Length = 256)]
    public virtual string DiseaseTypeName { get; set; }

    /// <summary>
    /// 门诊急诊转诊标志
    /// </summary>
    [SugarColumn(ColumnName = "out_eme_referral_sign", ColumnDescription = "门诊急诊转诊标志", Length = 4)]
    public virtual string OutEmeReferralSign { get; set; }

    /// <summary>
    /// 外伤标志
    /// </summary>
    [SugarColumn(ColumnName = "trauma_sign", ColumnDescription = "外伤标志", Length = 4)]
    public virtual string TraumaSign { get; set; }

    /// <summary>
    /// 涉及第三方标志
    /// </summary>
    [SugarColumn(ColumnName = "third_party_sign", ColumnDescription = "涉及第三方标志", Length = 4)]
    public virtual string ThirdPartySign { get; set; }

    /// <summary>
    /// 挂号时间
    /// </summary>
    [SugarColumn(ColumnName = "reg_time", ColumnDescription = "挂号时间")]
    public virtual DateTime? RegTime { get; set; }
    /// <summary>
    /// 看诊时间
    /// </summary>
    [SugarColumn(ColumnName = "see_time", ColumnDescription = "看诊时间")]
    public virtual DateTime? SeeTime { get; set; }

    /// <summary>
    /// 看诊时间
    /// </summary>
    [SugarColumn(ColumnName = "triage_status", ColumnDescription = "分诊状态（0 未分诊 1 已分诊 2 已就诊）", DefaultValue = "0")]
    public virtual int? TriageStatus { get; set; }
}