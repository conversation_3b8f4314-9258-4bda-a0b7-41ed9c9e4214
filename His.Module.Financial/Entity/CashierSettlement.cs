namespace His.Module.Financial.Entity;

/// <summary>
/// 收款员日结记录表
/// </summary>
[Tenant("1300000000010")]
[SugarTable("cashier_settlement", "收款员日结记录表")]
public class CashierSettlement : EntityTenantBaseData
{
    /// <summary>
    /// 收款员ID
    /// </summary>
    [SugarColumn(ColumnName = "cashier_id", ColumnDescription = "收款员ID")]
    public virtual long CashierId { get; set; }

    /// <summary>
    /// 收款员姓名
    /// </summary>
    [SugarColumn(ColumnName = "cashier_name", ColumnDescription = "收款员姓名", Length = 64)]
    public virtual string? CashierName { get; set; }

    /// <summary>
    /// 结算日期
    /// </summary>
    [SugarColumn(ColumnName = "settlement_date", ColumnDescription = "结算日期")]
    public virtual DateTime SettlementDate { get; set; }

    /// <summary>
    /// 结算类型 1=日结 2=月结
    /// </summary>
    [SugarColumn(ColumnName = "settlement_type", ColumnDescription = "结算类型 1=日结 2=月结")]
    public virtual int SettlementType { get; set; }

    /// <summary>
    /// 总收费金额
    /// </summary>
    [SugarColumn(ColumnName = "total_charge_amount", ColumnDescription = "总收费金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal TotalChargeAmount { get; set; }

    /// <summary>
    /// 总退费金额
    /// </summary>
    [SugarColumn(ColumnName = "total_refund_amount", ColumnDescription = "总退费金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal TotalRefundAmount { get; set; }

    /// <summary>
    /// 净收费金额（收费-退费）
    /// </summary>
    [SugarColumn(ColumnName = "net_charge_amount", ColumnDescription = "净收费金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal NetChargeAmount { get; set; }

    /// <summary>
    /// 就诊卡充值金额
    /// </summary>
    [SugarColumn(ColumnName = "total_recharge_amount", ColumnDescription = "就诊卡充值金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal TotalRechargeAmount { get; set; }

    /// <summary>
    /// 就诊卡退卡金额
    /// </summary>
    [SugarColumn(ColumnName = "total_card_refund_amount", ColumnDescription = "就诊卡退卡金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal TotalCardRefundAmount { get; set; }

    /// <summary>
    /// 净充值金额（充值-退卡）
    /// </summary>
    [SugarColumn(ColumnName = "net_recharge_amount", ColumnDescription = "净充值金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal NetRechargeAmount { get; set; }

    /// <summary>
    /// 收费笔数
    /// </summary>
    [SugarColumn(ColumnName = "charge_count", ColumnDescription = "收费笔数")]
    public virtual int ChargeCount { get; set; }

    /// <summary>
    /// 退费笔数
    /// </summary>
    [SugarColumn(ColumnName = "refund_count", ColumnDescription = "退费笔数")]
    public virtual int RefundCount { get; set; }

    /// <summary>
    /// 充值笔数
    /// </summary>
    [SugarColumn(ColumnName = "recharge_count", ColumnDescription = "充值笔数")]
    public virtual int RechargeCount { get; set; }

    /// <summary>
    /// 退卡笔数
    /// </summary>
    [SugarColumn(ColumnName = "card_refund_count", ColumnDescription = "退卡笔数")]
    public virtual int CardRefundCount { get; set; }

    /// <summary>
    /// 退费率（退费金额/收费金额）
    /// </summary>
    [SugarColumn(ColumnName = "refund_rate", ColumnDescription = "退费率", Length = 5, DecimalDigits = 4)]
    public virtual decimal RefundRate { get; set; }

    /// <summary>
    /// 日均收费金额（仅月结使用）
    /// </summary>
    [SugarColumn(ColumnName = "daily_avg_amount", ColumnDescription = "日均收费金额", Length = 16, DecimalDigits = 4)]
    public virtual decimal? DailyAvgAmount { get; set; }

    /// <summary>
    /// 日均收费笔数（仅月结使用）
    /// </summary>
    [SugarColumn(ColumnName = "daily_avg_count", ColumnDescription = "日均收费笔数", Length = 8, DecimalDigits = 2)]
    public virtual decimal? DailyAvgCount { get; set; }

    /// <summary>
    /// 工作天数（仅月结使用）
    /// </summary>
    [SugarColumn(ColumnName = "work_days", ColumnDescription = "工作天数")]
    public virtual int? WorkDays { get; set; }

    /// <summary>
    /// 支付方式统计JSON
    /// </summary>
    [SugarColumn(ColumnName = "pay_method_statistics", ColumnDescription = "支付方式统计JSON", IsJson = true)]
    public virtual string? PayMethodStatistics { get; set; }

    /// <summary>
    /// 开始时间
    /// </summary>
    [SugarColumn(ColumnName = "start_time", ColumnDescription = "开始时间")]
    public virtual DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    [SugarColumn(ColumnName = "end_time", ColumnDescription = "结束时间")]
    public virtual DateTime EndTime { get; set; }

    /// <summary>
    /// 状态 1=正常 2=已撤销 3=需重新计算
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态 1=正常 2=已撤销 3=需重新计算")]
    public virtual int Status { get; set; } = 1;

    /// <summary>
    /// 数据版本号（用于乐观锁）
    /// </summary>
    [SugarColumn(ColumnName = "version", ColumnDescription = "数据版本号")]
    public virtual int Version { get; set; } = 1;

    /// <summary>
    /// 是否有异常数据
    /// </summary>
    [SugarColumn(ColumnName = "has_exception", ColumnDescription = "是否有异常数据")]
    public virtual bool HasException { get; set; } = false;

    /// <summary>
    /// 异常说明
    /// </summary>
    [SugarColumn(ColumnName = "exception_note", ColumnDescription = "异常说明", Length = 1000)]
    public virtual string? ExceptionNote { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 500)]
    public virtual string? Remark { get; set; }
}