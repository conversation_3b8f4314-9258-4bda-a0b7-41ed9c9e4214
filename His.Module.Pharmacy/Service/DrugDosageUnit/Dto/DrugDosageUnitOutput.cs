namespace His.Module.Pharmacy;

/// <summary>
/// 药品剂量单位表输出参数
/// </summary>
public class DrugDosageUnitOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 剂量单位名称
    /// </summary>
    public string? UnitName { get; set; }    
    
    /// <summary>
    /// 剂量单位名称拼音
    /// </summary>
    public string? UnitPinyin { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 药品剂量单位表数据导入模板实体
/// </summary>
public class ExportDrugDosageUnitOutput : ImportDrugDosageUnitInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
