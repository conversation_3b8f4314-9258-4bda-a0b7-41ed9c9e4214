using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品剂量单位表服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugDosageUnitService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugDosageUnit> _drugDosageUnitRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugDosageUnitService(SqlSugarRepository<DrugDosageUnit> drugDosageUnitRep, ISqlSugarClient sqlSugarClient)
    {
        _drugDosageUnitRep = drugDosageUnitRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品剂量单位表 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品剂量单位表")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugDosageUnitOutput>> Page(PageDrugDosageUnitInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugDosageUnitRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.UnitName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.UnitName), u => u.UnitName.Contains(input.UnitName.Trim()))
            .WhereIF(input.Status.HasValue, u => u.Status == (int)input.Status)
            .Select<DrugDosageUnitOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品剂量单位表详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品剂量单位表详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugDosageUnit> Detail([FromQuery] QueryByIdDrugDosageUnitInput input)
    {
        return await _drugDosageUnitRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品剂量单位表 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品剂量单位表")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugDosageUnitInput input)
    {
        var entity = input.Adapt<DrugDosageUnit>();
        return await _drugDosageUnitRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品剂量单位表 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品剂量单位表")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugDosageUnitInput input)
    {
        var entity = input.Adapt<DrugDosageUnit>();
        await _drugDosageUnitRep.AsUpdateable(entity)
        .IgnoreColumns(u => new {
            u.UnitPinyin,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品剂量单位表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品剂量单位表")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugDosageUnitInput input)
    {
        var entity = await _drugDosageUnitRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugDosageUnitRep.FakeDeleteAsync(entity);   //假删除
        //await _drugDosageUnitRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品剂量单位表 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品剂量单位表")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugDosageUnitInput> input)
    {
        var exp = Expressionable.Create<DrugDosageUnit>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugDosageUnitRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugDosageUnitRep.FakeDeleteAsync(list);   //假删除
        //return await _drugDosageUnitRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置药品剂量单位表状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药品剂量单位表状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetDrugDosageUnitStatus(SetDrugDosageUnitStatusInput input)
    {
        await _drugDosageUnitRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 导出药品剂量单位表记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品剂量单位表记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugDosageUnitInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugDosageUnitOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品剂量单位表导出记录");
    }
    
    /// <summary>
    /// 下载药品剂量单位表数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品剂量单位表数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugDosageUnitOutput>(), "药品剂量单位表导入模板");
    }
    
    /// <summary>
    /// 导入药品剂量单位表记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品剂量单位表记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugDosageUnitInput, DrugDosageUnit>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugDosageUnit>>();
                    
                    var storageable = _drugDosageUnitRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.UnitName?.Length > 100, "剂量单位名称长度不能超过100个字符")
                        .SplitError(it => it.Item.UnitPinyin?.Length > 100, "剂量单位名称拼音长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
