using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品产地维护基础输入参数
/// </summary>
public class DrugPlaceBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 产地名称
    /// </summary>
    public virtual string? PlaceName { get; set; }
    
    /// <summary>
    /// 产地名称拼音
    /// </summary>
    public virtual string? PlaceNamePinyin { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品产地维护分页查询输入参数
/// </summary>
public class PageDrugPlaceInput : BasePageInput
{
    /// <summary>
    /// 产地名称
    /// </summary>
    public string? PlaceName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品产地维护增加输入参数
/// </summary>
public class AddDrugPlaceInput
{
    /// <summary>
    /// 产地名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "产地名称字符长度不能超过100")]
    public string? PlaceName { get; set; }
    
    /// <summary>
    /// 产地名称拼音
    /// </summary>
    [MaxLength(100, ErrorMessage = "产地名称拼音字符长度不能超过100")]
    public string? PlaceNamePinyin { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品产地维护删除输入参数
/// </summary>
public class DeleteDrugPlaceInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品产地维护更新输入参数
/// </summary>
public class UpdateDrugPlaceInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 产地名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "产地名称字符长度不能超过100")]
    public string? PlaceName { get; set; }
    
    /// <summary>
    /// 产地名称拼音
    /// </summary>    
    [MaxLength(100, ErrorMessage = "产地名称拼音字符长度不能超过100")]
    public string? PlaceNamePinyin { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
}

/// <summary>
/// 药品产地维护主键查询输入参数
/// </summary>
public class QueryByIdDrugPlaceInput : DeleteDrugPlaceInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetDrugPlaceStatusInput : BaseStatusInput
{
}

/// <summary>
/// 药品产地维护数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportDrugPlaceInput : BaseImportInput
{
    /// <summary>
    /// 产地名称
    /// </summary>
    [ImporterHeader(Name = "产地名称")]
    [ExporterHeader("产地名称", Format = "", Width = 25, IsBold = true)]
    public string? PlaceName { get; set; }
    
    /// <summary>
    /// 产地名称拼音
    /// </summary>
    [ImporterHeader(Name = "产地名称拼音")]
    [ExporterHeader("产地名称拼音", Format = "", Width = 25, IsBold = true)]
    public string? PlaceNamePinyin { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
}
