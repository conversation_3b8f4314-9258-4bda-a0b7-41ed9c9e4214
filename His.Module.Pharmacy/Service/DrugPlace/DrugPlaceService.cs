using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品产地维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugPlaceService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugPlace> _drugPlaceRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugPlaceService(SqlSugarRepository<DrugPlace> drugPlaceRep, ISqlSugarClient sqlSugarClient)
    {
        _drugPlaceRep = drugPlaceRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品产地维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品产地维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugPlaceOutput>> Page(PageDrugPlaceInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugPlaceRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.PlaceName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PlaceName), u => u.PlaceName.Contains(input.PlaceName.Trim()))
            .WhereIF(input.Status.HasValue, u => u.Status ==(int) input.Status)
            .Select<DrugPlaceOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品产地维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品产地维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugPlace> Detail([FromQuery] QueryByIdDrugPlaceInput input)
    {
        return await _drugPlaceRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品产地维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品产地维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugPlaceInput input)
    {
        var entity = input.Adapt<DrugPlace>();
        return await _drugPlaceRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品产地维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品产地维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugPlaceInput input)
    {
        var entity = input.Adapt<DrugPlace>();
        await _drugPlaceRep.AsUpdateable(entity)
        .IgnoreColumns(u => new {
            u.Remark,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品产地维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品产地维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugPlaceInput input)
    {
        var entity = await _drugPlaceRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugPlaceRep.FakeDeleteAsync(entity);   //假删除
        //await _drugPlaceRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品产地维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品产地维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugPlaceInput> input)
    {
        var exp = Expressionable.Create<DrugPlace>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugPlaceRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugPlaceRep.FakeDeleteAsync(list);   //假删除
        //return await _drugPlaceRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置药品产地维护状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药品产地维护状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetDrugPlaceStatus(SetDrugPlaceStatusInput input)
    {
        await _drugPlaceRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 导出药品产地维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品产地维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugPlaceInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugPlaceOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品产地维护导出记录");
    }
    
    /// <summary>
    /// 下载药品产地维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品产地维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugPlaceOutput>(), "药品产地维护导入模板");
    }
    
    /// <summary>
    /// 导入药品产地维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品产地维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugPlaceInput, DrugPlace>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugPlace>>();
                    
                    var storageable = _drugPlaceRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.PlaceName?.Length > 100, "产地名称长度不能超过100个字符")
                        .SplitError(it => it.Item.PlaceNamePinyin?.Length > 100, "产地名称拼音长度不能超过100个字符")
                        .SplitError(it => it.Item.Remark?.Length > 255, "备注长度不能超过255个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
