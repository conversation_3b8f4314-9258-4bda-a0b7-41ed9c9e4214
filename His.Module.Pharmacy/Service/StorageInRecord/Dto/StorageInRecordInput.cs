namespace His.Module.Pharmacy;

/// <summary>
/// 入库管理基础输入参数
/// </summary>
public class StorageInRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    public virtual string? StorageInNo { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public virtual long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商编码
    /// </summary>
    public virtual string? SupplierCode { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    public virtual string? SupplierName { get; set; }
    
    /// <summary>
    /// 入库类型
    /// </summary>
    [Dict("StorageInType", AllowNullValue=true)]
    public virtual string? StorageInType { get; set; }
    
    /// <summary>
    /// 入库日期
    /// </summary>
    public virtual DateTime? StorageInTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    public virtual string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 入库管理分页查询输入参数
/// </summary>
public class PageStorageInRecordInput : BasePageInput
{
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    public string? StorageInNo { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 入库类型
    /// </summary>
    [Dict("StorageInType", AllowNullValue=true)]
    public string? StorageInType { get; set; }
    
    /// <summary>
    /// 入库日期范围
    /// </summary>
     public DateTime?[] StorageInTimeRange { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 入库管理增加输入参数
/// </summary>
public class AddStorageInRecordInput
{
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    [MaxLength(100, ErrorMessage = "入库单号字符长度不能超过100")]
    public string? StorageInNo { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "供应商编码字符长度不能超过100")]
    public string? SupplierCode { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "供应商名称字符长度不能超过100")]
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 入库类型
    /// </summary>
    [Dict("StorageInType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "入库类型字符长度不能超过100")]
    public string? StorageInType { get; set; }
    
    /// <summary>
    /// 入库日期
    /// </summary>
    public DateTime? StorageInTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    [MaxLength(100, ErrorMessage = "发票号字符长度不能超过100")]
    public string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(100, ErrorMessage = "备注字符长度不能超过100")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    
    public List<AddStorageInDetailInput> Details { get; set; }
}

/// <summary>
/// 提交入库单
/// </summary>
public class SubmitStorageInRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 审核入库单入参
/// </summary>
public class AuditStorageInRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long Id { get; set; }

    /// <summary>
    /// 审核意见
    /// </summary>
    // [Required(ErrorMessage = "审核意见不能为空")]
    // [StringLength(500, MinimumLength = 5, ErrorMessage = "审核意见长度在5-500个字符之间")]
    public string AuditOpinion { get; set; }

}

/// <summary>
/// 入库管理删除输入参数
/// </summary>
public class DeleteStorageInRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}
/// <summary>
/// 入库管理更新输入参数
/// </summary>
public class UpdateStorageInRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>    
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "库房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入库单号字符长度不能超过100")]
    public string? StorageInNo { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>    
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "供应商编码字符长度不能超过100")]
    public string? SupplierCode { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "供应商名称字符长度不能超过100")]
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 入库类型
    /// </summary>    
    [Dict("StorageInType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "入库类型字符长度不能超过100")]
    public string? StorageInType { get; set; }
    
    /// <summary>
    /// 入库日期
    /// </summary>    
    public DateTime? StorageInTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>    
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>    
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "发票号字符长度不能超过100")]
    public string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(100, ErrorMessage = "备注字符长度不能超过100")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    public List<AddStorageInDetailInput> Details { get; set; }
}

/// <summary>
/// 入库管理主键查询输入参数
/// </summary>
public class QueryByIdStorageInRecordInput : DeleteStorageInRecordInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataStorageInRecordInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 入库管理数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportStorageInRecordInput : BaseImportInput
{
    /// <summary>
    /// 库房 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房 文本
    /// </summary>
    [ImporterHeader(Name = "库房")]
    [ExporterHeader("库房", Format = "", Width = 25, IsBold = true)]
    public string StorageFkDisplayName { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [ImporterHeader(Name = "库房编码")]
    [ExporterHeader("库房编码", Format = "", Width = 25, IsBold = true)]
    public string? StorageCode { get; set; }
    
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    [ImporterHeader(Name = "入库单号")]
    [ExporterHeader("入库单号", Format = "", Width = 25, IsBold = true)]
    public string? StorageInNo { get; set; }
    
    /// <summary>
    /// 供应商 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商 文本
    /// </summary>
    [ImporterHeader(Name = "供应商")]
    [ExporterHeader("供应商", Format = "", Width = 25, IsBold = true)]
    public string SupplierFkDisplayName { get; set; }
    
    /// <summary>
    /// 供应商编码
    /// </summary>
    [ImporterHeader(Name = "供应商编码")]
    [ExporterHeader("供应商编码", Format = "", Width = 25, IsBold = true)]
    public string? SupplierCode { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    [ImporterHeader(Name = "供应商名称")]
    [ExporterHeader("供应商名称", Format = "", Width = 25, IsBold = true)]
    public string? SupplierName { get; set; }
    
    /// <summary>
    /// 入库类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? StorageInType { get; set; }
    
    /// <summary>
    /// 入库类型 文本
    /// </summary>
    [Dict("StorageInType")]
    [ImporterHeader(Name = "入库类型")]
    [ExporterHeader("入库类型", Format = "", Width = 25, IsBold = true)]
    public string StorageInTypeDictLabel { get; set; }
    
    /// <summary>
    /// 入库日期
    /// </summary>
    [ImporterHeader(Name = "入库日期")]
    [ExporterHeader("入库日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? StorageInTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    [ImporterHeader(Name = "总进价")]
    [ExporterHeader("总进价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [ImporterHeader(Name = "总零售价")]
    [ExporterHeader("总零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 发票号
    /// </summary>
    [ImporterHeader(Name = "发票号")]
    [ExporterHeader("发票号", Format = "", Width = 25, IsBold = true)]
    public string? InvoiceNo { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 状态 文本
    /// </summary>
    [Dict("StorageInOutStatus")]
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public string StatusDictLabel { get; set; }
    
}
