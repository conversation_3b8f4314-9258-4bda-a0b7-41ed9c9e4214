using Admin.NET.Core.Service;
using His.Module.Shared.Service.BasicInfo;
using His.Module.Shared.Service.BasicInfo.Dto;
using Microsoft.AspNetCore.Http;
namespace His.Module.Pharmacy.Service;

/// <summary>
/// 出库管理服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class StorageOutRecordService(
    SqlSugarRepository<StorageOutRecord> storageOutRecordRep,
    SqlSugarRepository<StorageOutDetail> storageOutDetailRep,
    ISqlSugarClient sqlSugarClient,
    SysDictTypeService sysDictTypeService,
    SqlSugarRepository<DrugStorage> drugStorageRep,
    InventoryService inventoryService,
    BasicInfoService basicInfoService,
    UserManager userManager,
    SqlSugarRepository<StorageAuditRecord> storageAuditRecordRep)
    : IDynamicApiController, ITransient
{
    /// <summary>
    /// 分页查询出库管理 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询出库管理")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<StorageOutRecordOutput>> Page(PageStorageOutRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = storageOutRecordRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.StorageCode.Contains(input.Keyword) || u.DrugType.Contains(input.Keyword) ||
                     u.StorageOutNo.Contains(input.Keyword) || u.StorageOutType.Contains(input.Keyword) ||
                     u.TargetUserCode.Contains(input.Keyword) || u.TargetUserName.Contains(input.Keyword) ||
                     u.InvoiceNo.Contains(input.Keyword) || u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageCode),
                u => u.StorageCode.Contains(input.StorageCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.DrugType), u => u.DrugType.Contains(input.DrugType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageOutNo),
                u => u.StorageOutNo.Contains(input.StorageOutNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.StorageOutType),
                u => u.StorageOutType.Contains(input.StorageOutType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TargetUserCode),
                u => u.TargetUserCode.Contains(input.TargetUserCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TargetUserName),
                u => u.TargetUserName.Contains(input.TargetUserName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InvoiceNo), u => u.InvoiceNo.Contains(input.InvoiceNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.StorageId != null, u => u.StorageId == input.StorageId)
            .WhereIF(input.StorageOutTimeRange?.Length == 2,
                u => u.StorageOutTime >= input.StorageOutTimeRange[0] &&
                     u.StorageOutTime <= input.StorageOutTimeRange[1])
            .WhereIF(input.TargetDeptId != null, u => u.TargetDeptId == input.TargetDeptId)
            .WhereIF(input.TargetUserId != null, u => u.TargetUserId == input.TargetUserId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .LeftJoin<DrugStorage>((u, storage) => u.StorageId == storage.Id)
            .LeftJoin<EnterpriseDictionary>((u, storage, supplier) => u.SupplierId == supplier.Id)
            .LeftJoin<DrugStorage>((u, storage, supplier, targetDept) => u.TargetDeptId == targetDept.Id)
            .Select((u, storage, supplier, targetDept) => new StorageOutRecordOutput
            {
                Id = u.Id,
                StorageId = u.StorageId,
                StorageFkDisplayName = $"{storage.StorageName}",
                StorageCode = u.StorageCode,
                DrugType = u.DrugType,
                StorageOutNo = u.StorageOutNo,
                SupplierId = u.SupplierId,
                SupplierFkDisplayName = $"",
                SupplierCode = u.SupplierCode,
                SupplierName = u.SupplierName,
                StorageOutType = u.StorageOutType,
                StorageOutTime = u.StorageOutTime,
                TargetDeptId = u.TargetDeptId,
                TargetDeptFkDisplayName = $"{targetDept.StorageName}",
                TargetDeptCode = u.TargetDeptCode,
                TargetDeptName = u.TargetDeptName,
                TargetUserId = u.TargetUserId,
                TargetUserCode = u.TargetUserCode,
                TargetUserName = u.TargetUserName,
                TotalPurchasePrice = u.TotalPurchasePrice,
                TotalSalePrice = u.TotalSalePrice,
                InvoiceNo = u.InvoiceNo,
                Remark = u.Remark,
                Status = u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取出库管理详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取出库管理详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<StorageOutRecord> Detail([FromQuery] QueryByIdStorageOutRecordInput input)
    {
        return await storageOutRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加出库管理 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加出库管理")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [UnitOfWork]
    public async Task<long> Add(AddStorageOutRecordInput input)
    {
        var entity = input.Adapt<StorageOutRecord>();
        entity.StorageOutTime = DateTime.Now;
        entity.Status = 0;
        entity.StorageOutNo = await storageOutRecordRep.Context.Ado.GetStringAsync(
            "SELECT LPAD(CAST(NEXTVAL('storage_out_record_no_seq')As varchar),7,'0')");
        var storage = await drugStorageRep.GetFirstAsync(u => u.Id == entity.StorageId);
        entity.StorageCode = storage.StorageCode;
        entity.StorageName = storage.StorageName;
        var id = await storageOutRecordRep.InsertAsync(entity) ? entity.Id : 0;

        var details = input.Details.Adapt<List<StorageOutDetail>>();
        await SaveDetail(details, entity);
        await storageOutRecordRep.UpdateAsync(u => new StorageOutRecord
        {
            TotalSalePrice = entity.TotalSalePrice,
        }, u => u.Id == id);
        return id;
    }

    private async Task SaveDetail(List<StorageOutDetail> details, StorageOutRecord entity)
    {
        await storageOutDetailRep.DeleteAsync(u => u.StorageOutId == entity.Id);
        foreach (var item in details)
        {
            item.StorageOutNo = entity.StorageOutNo;
            item.StorageOutId = entity.Id;
            item.Id = 0;
            if (item.Quantity == null || item.Quantity == 0)
            {
                throw Oops.Oh("出库数量不能为0");
            }
        }

        var totalSalePrice = details.Sum(p => p.TotalSalePrice);
        var totalPurchasePrice = details.Sum(p => p.TotalPurchasePrice);
        entity.TotalSalePrice = totalSalePrice;
        entity.TotalPurchasePrice = totalPurchasePrice;
        await storageOutDetailRep.InsertRangeAsync(details);
    }

    /// <summary>
    /// 提交出库单 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("提交出库单")]
    [ApiDescriptionSettings(Name = "Submit"), HttpPost]
    [UnitOfWork]
    public async Task<bool> Submit(SubmitStorageInRecordInput input)
    {
        var record = await storageOutRecordRep.GetFirstAsync(u => u.Id == input.Id)
                     ?? throw Oops.Oh("入库单不存在");
        if (record.Status != 0 && record.Status != 9)
            throw Oops.Oh("只有保存或审核拒绝状态的出库单可以提交");
        // 获取仓库信息并确定是否需要审核
        var storage = await drugStorageRep.GetFirstAsync(u => u.Id == record.StorageId)
                      ?? throw Oops.Oh("关联的药库不存在");
        // 如果需要审核，更改为待审核状态
        if (storage.PurchaseAudit == 1)
            return await storageOutRecordRep.UpdateAsync(
                u => new StorageOutRecord
                {
                    Status = 2
                },
                u => u.Id == input.Id
            );
        // 如果不需要审核，直接更新库存
        var target = await drugStorageRep.GetFirstAsync(u => u.Id == record.TargetDeptId);
        var details = await storageOutDetailRep.GetListAsync(u => u.StorageOutId == input.Id);
        foreach (var detail in details)
            await inventoryService.UpdateInventoryAsync(detail, record, target);
        return await storageOutRecordRep.UpdateAsync(
            u => new StorageOutRecord
            {
                Status = 1, StorageOutTime = DateTime.Now
            },
            u => u.Id == input.Id
        );
    }

    /// <summary>
    /// 审核通过
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Approve")]
    [HttpPost]
    [DisplayName("审核通过")]
    [UnitOfWork]
    public async Task<bool> Approve(AuditStorageInRecordInput input)
    {
        return await ProcessAudit(input.Id, input.AuditOpinion, 3);
    }


    /// <summary>
    /// 审核拒绝
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Reject")]
    [HttpPost]
    [DisplayName("审核拒绝")]
    [UnitOfWork]
    public async Task<bool> Reject(AuditStorageInRecordInput input)
    {
        return await ProcessAudit(input.Id, input.AuditOpinion, 9);
    }

    /// <summary>
    /// 处理审核操作
    /// </summary>
    /// <param name="id">出库单ID</param>
    /// <param name="auditOpinion">审核意见</param>
    /// <param name="auditResult">审核结果</param>
    /// <returns></returns>
    private async Task<bool> ProcessAudit(long id, string auditOpinion, int auditResult)
    {
        var inRecord = await storageOutRecordRep.GetByIdAsync(id);
        if (inRecord == null)
            throw Oops.Oh("出库单记录不存在");

        if (inRecord.Status != 2)
            throw Oops.Oh("只能审核待审核状态的记录");
        switch (auditResult)
        {
            // 如果审核通过，执行库存变更操作
            case 3:
                var target = await drugStorageRep.GetFirstAsync(u => u.Id == inRecord.TargetDeptId);
                var details = await storageOutDetailRep.GetListAsync(u => u.StorageOutId == id);
                foreach (var detail in details)
                    await inventoryService.UpdateInventoryAsync(detail, inRecord, target);
                await storageOutRecordRep.UpdateAsync(
                    u => new StorageOutRecord
                    {
                        Status = auditResult, StorageOutTime = DateTime.Now
                    },
                    u => u.Id == id
                );
                break;
            // 如果审核拒绝，更新入库单状态为审核拒绝
            case 9:
                await storageOutRecordRep.UpdateAsync(
                    u => new StorageOutRecord
                    {
                        Status = auditResult
                    },
                    u => u.Id == id
                );
                break;
        }
        var auditRecord = new StorageAuditRecord
        {
            BusinessType = "StorageOut",
            BusinessId = inRecord.Id,
            BusinessNo = inRecord.StorageOutNo,
            AuditStatus = auditResult,
            AuditOpinion = auditOpinion,
            AuditTime = DateTime.Now,
            AuditorId = userManager.UserId,
            AuditorName = userManager.RealName
        };
        return await storageAuditRecordRep.InsertAsync(auditRecord);
    }

    /// <summary>
    /// 更新出库管理 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新出库管理")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [UnitOfWork]
    public async Task Update(UpdateStorageOutRecordInput input)
    {
        var result = await storageOutRecordRep.GetFirstAsync(u => u.Id == input.Id);
        if (result.Status == 1)
        {
            throw Oops.Oh("当前状态禁止修改");
        }

        var entity = input.Adapt<StorageOutRecord>();
        var details = input.Details.Adapt<List<StorageOutDetail>>();
        await SaveDetail(details, entity);
        await storageOutRecordRep.AsUpdateable(entity)
            .IgnoreColumns(u => new
            {
                u.StorageOutNo,
                u.SupplierId,
                u.SupplierCode,
                u.SupplierName,
                u.TargetDeptCode,
                u.TargetDeptName,
            })
            .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除出库管理 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除出库管理")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [UnitOfWork]
    public async Task Delete(DeleteStorageOutRecordInput input)
    {
        var entity = await storageOutRecordRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);

        if (entity.Status != 0)
        {
            throw Oops.Oh("当前状态禁止删除");
        }

        await storageOutRecordRep.FakeDeleteAsync(entity); //假删除
        var details = await storageOutDetailRep.GetListAsync(u => u.StorageOutId == input.Id);
        await storageOutDetailRep.FakeDeleteAsync(details);
        //await _storageOutRecordRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除出库管理 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除出库管理")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteStorageOutRecordInput> input)
    {
        var exp = Expressionable.Create<StorageOutRecord>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await storageOutRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
        return await storageOutRecordRep.FakeDeleteAsync(list); //假删除

    }

    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns> 
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataStorageOutRecordInput input)
    {
        var storageIdData = await storageOutRecordRep.Context.Queryable<DrugStorage>()
            .Where(u=> u.ParentId.Equals(0L))
            // .InnerJoinIF<StorageOutRecord>(input.FromPage, (u, r) => u.Id == r.StorageId)
            .Select(u => new
            {
                Value = u.Id,
                Label = $"{u.StorageName}",
                u.StorageDrugType
            }).ToListAsync();
        // var supplierIdData = await _storageOutRecordRep.Context.Queryable<EnterpriseDictionary>()
        //     .Where(u => u.EnterpriseType == "supplier")
        //    .Select(u => new {
        //         Value = u.Id,
        //         Label = $"{u.EnterpriseName}"
        //     }).ToListAsync();
        // var targetDeptIdData = await _storageOutRecordRep.Context.Queryable<DrugStorage>()
        //     //.InnerJoinIF<StorageOutRecord>(input.FromPage, (u, r) => u.Id == r.TargetDeptId)
        //     .Where(u=> !u.ParentId.Equals(0L))
        //     .Select(u => new
        //     {
        //         Value = u.Id,
        //         Label = $"{u.StorageName}"
        //     }).ToListAsync();
        // 可选择的出库科室
        var targetDeptFilter = new DepartmentInput
        {
            OrgTypes = input.TargetDeptFilter == "department"
                ? ["701", "801"] // 仅门诊和住院科室
                : ["701", "801", "501", "601"] // 门诊、住院、药库、药房
        };

        var departments = (await basicInfoService.GetDepartments(targetDeptFilter))
            .Select(u => new
            {
                Value = u.Id, Label = u.Name
            })
            .ToList();
        return new Dictionary<string, dynamic>
        {
            { "storageId", storageIdData },
            //  { "supplierId", supplierIdData },
            {
                "targetDeptId", departments
            }
        };
    }

    /// <summary>
    /// 导出出库管理记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出出库管理记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageStorageOutRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportStorageOutRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();

        var drugTypeDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput
            {
                Code = "DrugType"
            }).Result
            .ToDictionary(x => x.Value, x => x.Label);
        var storageOutTypeDictMap = sysDictTypeService
            .GetDataList(new GetDataDictTypeInput { Code = "StorageOutType" }).Result
            .ToDictionary(x => x.Value, x => x.Label);
        var statusDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput
            {
                Code = "StorageInOutStatus"
            })
            .Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e =>
        {
            e.DrugTypeDictLabel = drugTypeDictMap.GetValueOrDefault(e.DrugType ?? "", e.DrugType);
            e.StorageOutTypeDictLabel =
                storageOutTypeDictMap.GetValueOrDefault(e.StorageOutType ?? "", e.StorageOutType);

            // e.StatusDictLabel = statusDictMap.GetValueOrDefault(e.Status ?? "", e.Status);
        });
        return ExcelHelper.ExportTemplate(list, "出库管理导出记录");
    }

    /// <summary>
    /// 下载出库管理数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载出库管理数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportStorageOutRecordOutput>(), "出库管理导入模板", (_, info) =>
        {
            if (nameof(ExportStorageOutRecordOutput.StorageFkDisplayName) == info.Name)
                return storageOutRecordRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}").Distinct()
                    .ToList();
            if (nameof(ExportStorageOutRecordOutput.TargetDeptFkDisplayName) == info.Name)
                return storageOutRecordRep.Context.Queryable<DrugStorage>().Select(u => $"{u.StorageName}").Distinct()
                    .ToList();
            return null;
        });
    }

    /// <summary>
    /// 导入出库管理记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入出库管理记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var drugTypeDictMap = sysDictTypeService.GetDataList(new GetDataDictTypeInput
                {
                    Code = "DrugType"
                }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var storageOutTypeDictMap = sysDictTypeService
                .GetDataList(new GetDataDictTypeInput { Code = "StorageOutType" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var statusDictMap = sysDictTypeService
                .GetDataList(new GetDataDictTypeInput { Code = "StorageInOutStatus" }).Result
                .ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportStorageOutRecordInput, StorageOutRecord>(file,
                (list, markerErrorAction) =>
                {

                    sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 链接 库房
                        var storageIdLabelList = pageItems.Where(x => x.StorageFkDisplayName != null)
                            .Select(x => x.StorageFkDisplayName).Distinct().ToList();
                        if (storageIdLabelList.Any())
                        {
                            var storageIdLinkMap = storageOutRecordRep.Context.Queryable<DrugStorage>()
                                .Where(u => storageIdLabelList.Contains($"{u.StorageName}")).ToList()
                                .ToDictionary(u => $"{u.StorageName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.StorageId = storageIdLinkMap.GetValueOrDefault(e.StorageFkDisplayName ?? "");
                                if (e.StorageId == null) e.Error = "库房链接失败";
                            });
                        }

                        // 链接 目标科室
                        var targetDeptIdLabelList = pageItems.Where(x => x.TargetDeptFkDisplayName != null)
                            .Select(x => x.TargetDeptFkDisplayName).Distinct().ToList();
                        if (targetDeptIdLabelList.Any())
                        {
                            var targetDeptIdLinkMap = storageOutRecordRep.Context.Queryable<DrugStorage>()
                                .Where(u => targetDeptIdLabelList.Contains($"{u.StorageName}")).ToList()
                                .ToDictionary(u => $"{u.StorageName}", u => u.Id);
                            pageItems.ForEach(e =>
                            {
                                e.TargetDeptId = targetDeptIdLinkMap.GetValueOrDefault(e.TargetDeptFkDisplayName ?? "");
                                if (e.TargetDeptId == null) e.Error = "目标科室链接失败";
                            });
                        }

                        // 映射字典值
                        foreach (var item in pageItems)
                        {
                            if (string.IsNullOrWhiteSpace(item.DrugTypeDictLabel)) continue;
                            item.DrugType = drugTypeDictMap.GetValueOrDefault(item.DrugTypeDictLabel);
                            if (item.DrugType == null) item.Error = "药品类型字典映射失败";
                            if (string.IsNullOrWhiteSpace(item.StorageOutTypeDictLabel)) continue;
                            item.StorageOutType = storageOutTypeDictMap.GetValueOrDefault(item.StorageOutTypeDictLabel);
                            if (item.StorageOutType == null) item.Error = "出库类型字典映射失败";
                            //  if (string.IsNullOrWhiteSpace(item.StatusDictLabel)) continue;
                            //  item.Status = statusDictMap.GetValueOrDefault(item.StatusDictLabel);
                            if (item.Status == null) item.Error = "状态字典映射失败";
                        }

                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => { return true; }).Adapt<List<StorageOutRecord>>();

                        var storageable = storageOutRecordRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.StorageCode?.Length > 100, "库房编码长度不能超过100个字符")
                            .SplitError(it => it.Item.DrugType?.Length > 100, "药品类型长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageOutNo?.Length > 100, "出库单号长度不能超过100个字符")
                            .SplitError(it => it.Item.StorageOutType?.Length > 100, "出库类型长度不能超过100个字符")
                            .SplitError(it => it.Item.TargetUserCode?.Length > 100, "领用人编码长度不能超过100个字符")
                            .SplitError(it => it.Item.TargetUserName?.Length > 100, "领用人名称长度不能超过100个字符")
                            .SplitError(it => it.Item.InvoiceNo?.Length > 100, "发票号长度不能超过100个字符")
                            .SplitError(it => it.Item.Remark?.Length > 100, "备注长度不能超过100个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });
  

            return stream;
        }
    }
    
    
    
}