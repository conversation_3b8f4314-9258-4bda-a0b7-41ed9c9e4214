namespace His.Module.Pharmacy;

/// <summary>
/// 采购计划输出参数
/// </summary>
public class DrugPurchasePlanDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 采购计划号
    /// </summary>
    public string? PlanNo { get; set; }
    
    /// <summary>
    /// 采购计划时间
    /// </summary>
    public DateTime? PlanTime { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    public int? Status { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
