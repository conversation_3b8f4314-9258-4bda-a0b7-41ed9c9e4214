using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 特殊处理记基础输入参数
/// </summary>
public class StorageSpecialRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 特殊处理单号
    /// </summary>
    public virtual string? HandleNo { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public virtual long? SupplierId { get; set; }
    
    /// <summary>
    /// 处理类型
    /// </summary>
    [Dict("SpecialHandleType", AllowNullValue=true)]
    public virtual string? HandleType { get; set; }
    
    /// <summary>
    /// 处理时间
    /// </summary>
    public virtual DateTime? HandleTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 特殊处理记分页查询输入参数
/// </summary>
public class PageStorageSpecialRecordInput : BasePageInput
{
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 特殊处理单号
    /// </summary>
    public string? HandleNo { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 处理类型
    /// </summary>
    [Dict("SpecialHandleType", AllowNullValue=true)]
    public string? HandleType { get; set; }
    
    /// <summary>
    /// 处理时间范围
    /// </summary>
     public DateTime?[] HandleTimeRange { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 特殊处理记增加输入参数
/// </summary>
public class AddStorageSpecialRecordInput
{
    /// <summary>
    /// 库房
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 特殊处理单号
    /// </summary>
    [MaxLength(100, ErrorMessage = "特殊处理单号字符长度不能超过100")]
    public string? HandleNo { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 处理类型
    /// </summary>
    [Dict("SpecialHandleType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "处理类型字符长度不能超过100")]
    public string? HandleType { get; set; }
    
    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime? HandleTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(100, ErrorMessage = "备注字符长度不能超过100")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }


    public List<AddStorageSpecialDetailInput> Details { get; set; }

}

/// <summary>
/// 特殊处理记删除输入参数
/// </summary>
public class DeleteStorageSpecialRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

public class SubmitStorageSpecialRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}


/// <summary>
/// 特殊处理记更新输入参数
/// </summary>
public class UpdateStorageSpecialRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 库房
    /// </summary>    
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 特殊处理单号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "特殊处理单号字符长度不能超过100")]
    public string? HandleNo { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 供应商
    /// </summary>    
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 处理类型
    /// </summary>    
    [Dict("SpecialHandleType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "处理类型字符长度不能超过100")]
    public string? HandleType { get; set; }
    
    /// <summary>
    /// 处理时间
    /// </summary>    
    public DateTime? HandleTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>    
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>    
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(100, ErrorMessage = "备注字符长度不能超过100")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    public List<UpdateStorageSpecialDetailInput> Details { get; set; }
    
}

/// <summary>
/// 特殊处理记主键查询输入参数
/// </summary>
public class QueryByIdStorageSpecialRecordInput : DeleteStorageSpecialRecordInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataStorageSpecialRecordInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 特殊处理记数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportStorageSpecialRecordInput : BaseImportInput
{
    /// <summary>
    /// 库房 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 库房 文本
    /// </summary>
    [ImporterHeader(Name = "库房")]
    [ExporterHeader("库房", Format = "", Width = 25, IsBold = true)]
    public string StorageFkDisplayName { get; set; }
    
    /// <summary>
    /// 特殊处理单号
    /// </summary>
    [ImporterHeader(Name = "特殊处理单号")]
    [ExporterHeader("特殊处理单号", Format = "", Width = 25, IsBold = true)]
    public string? HandleNo { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 供应商 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商 文本
    /// </summary>
    [ImporterHeader(Name = "供应商")]
    [ExporterHeader("供应商", Format = "", Width = 25, IsBold = true)]
    public string SupplierFkDisplayName { get; set; }
    
    /// <summary>
    /// 处理类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? HandleType { get; set; }
    
    /// <summary>
    /// 处理类型 文本
    /// </summary>
    [Dict("SpecialHandleType")]
    [ImporterHeader(Name = "处理类型")]
    [ExporterHeader("处理类型", Format = "", Width = 25, IsBold = true)]
    public string HandleTypeDictLabel { get; set; }
    
    /// <summary>
    /// 处理时间
    /// </summary>
    [ImporterHeader(Name = "处理时间")]
    [ExporterHeader("处理时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? HandleTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    [ImporterHeader(Name = "总进价")]
    [ExporterHeader("总进价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [ImporterHeader(Name = "总零售价")]
    [ExporterHeader("总零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 状态 文本
    /// </summary>
    [Dict("StorageInOutStatus")]
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public string StatusDictLabel { get; set; }
    
}
