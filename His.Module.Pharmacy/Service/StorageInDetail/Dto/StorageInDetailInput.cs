using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品入库明细基础输入参数
/// </summary>
public class StorageInDetailBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    public virtual string? StorageInNo { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    public virtual decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public virtual decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public virtual int? Quantity { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public virtual string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public virtual DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 过期日期
    /// </summary>
    public virtual DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    public virtual string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    public virtual string? MedicineCode { get; set; }
    
    /// <summary>
    /// 质量状况
    /// </summary>
    [Dict("StorageInQualityStatus", AllowNullValue=true)]
    public virtual string? QualityStatus { get; set; }
    
    /// <summary>
    /// 验收状态
    /// </summary>
    [Dict("StorageInAcceptanceStatus", AllowNullValue=true)]
    public virtual string? AcceptanceStatus { get; set; }
    
}

/// <summary>
/// 药品入库明细分页查询输入参数
/// </summary>
public class PageStorageInDetailInput : BasePageInput
{
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    public string? StorageInNo { get; set; }
    public long? StorageInId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品入库明细增加输入参数
/// </summary>
public class AddStorageInDetailInput
{
    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    [MaxLength(100, ErrorMessage = "入库单号字符长度不能超过100")]
    public string? StorageInNo { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 过期日期
    /// </summary>
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 质量状况
    /// </summary>
    [Dict("StorageInQualityStatus", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "质量状况字符长度不能超过100")]
    public string? QualityStatus { get; set; }
    
    /// <summary>
    /// 验收状态
    /// </summary>
    [Dict("StorageInAcceptanceStatus", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "验收状态字符长度不能超过100")]
    public string? AcceptanceStatus { get; set; }
    
    /// <summary>
    /// 验收状态
    /// </summary>
  
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 验收状态
    /// </summary>
 
    public virtual string? ManufacturerName { get; set; }
    
}

/// <summary>
/// 药品入库明细删除输入参数
/// </summary>
public class DeleteStorageInDetailInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品入库明细更新输入参数
/// </summary>
public class UpdateStorageInDetailInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>    
    [Dict("DrugType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "入库单号字符长度不能超过100")]
    public string? StorageInNo { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>    
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>    
    [MaxLength(100, ErrorMessage = "规格字符长度不能超过100")]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>    
    [MaxLength(100, ErrorMessage = "单位字符长度不能超过100")]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>    
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>    
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>    
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>    
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>    
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>    
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 过期日期
    /// </summary>    
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "国家医保编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 质量状况
    /// </summary>    
    [Dict("StorageInQualityStatus", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "质量状况字符长度不能超过100")]
    public string? QualityStatus { get; set; }
    
    /// <summary>
    /// 验收状态
    /// </summary>    
    [Dict("StorageInAcceptanceStatus", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "验收状态字符长度不能超过100")]
    public string? AcceptanceStatus { get; set; }
    
}

/// <summary>
/// 药品入库明细主键查询输入参数
/// </summary>
public class QueryByIdStorageInDetailInput : DeleteStorageInDetailInput
{
}

/// <summary>
/// 药品入库明细数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportStorageInDetailInput : BaseImportInput
{
    /// <summary>
    /// 药品类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DrugType { get; set; }
    
    /// <summary>
    /// 药品类型 文本
    /// </summary>
    [Dict("DrugType")]
    [ImporterHeader(Name = "药品类型")]
    [ExporterHeader("药品类型", Format = "", Width = 25, IsBold = true)]
    public string DrugTypeDictLabel { get; set; }
    
    /// <summary>
    /// 入库单号
    /// </summary>
    [ImporterHeader(Name = "入库单号")]
    [ExporterHeader("入库单号", Format = "", Width = 25, IsBold = true)]
    public string? StorageInNo { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [ImporterHeader(Name = "药品ID")]
    [ExporterHeader("药品ID", Format = "", Width = 25, IsBold = true)]
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [ImporterHeader(Name = "药品编码")]
    [ExporterHeader("药品编码", Format = "", Width = 25, IsBold = true)]
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [ImporterHeader(Name = "药品名称")]
    [ExporterHeader("药品名称", Format = "", Width = 25, IsBold = true)]
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [ImporterHeader(Name = "规格")]
    [ExporterHeader("规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [ImporterHeader(Name = "单位")]
    [ExporterHeader("单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }
    
    /// <summary>
    /// 进价
    /// </summary>
    [ImporterHeader(Name = "进价")]
    [ExporterHeader("进价", Format = "", Width = 25, IsBold = true)]
    public decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    [ImporterHeader(Name = "零售价")]
    [ExporterHeader("零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? SalePrice { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    [ImporterHeader(Name = "总进价")]
    [ExporterHeader("总进价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [ImporterHeader(Name = "总零售价")]
    [ExporterHeader("总零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [ImporterHeader(Name = "数量")]
    [ExporterHeader("数量", Format = "", Width = 25, IsBold = true)]
    public int? Quantity { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    [ImporterHeader(Name = "批号")]
    [ExporterHeader("批号", Format = "", Width = 25, IsBold = true)]
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 生产日期
    /// </summary>
    [ImporterHeader(Name = "生产日期")]
    [ExporterHeader("生产日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ProductionDate { get; set; }
    
    /// <summary>
    /// 过期日期
    /// </summary>
    [ImporterHeader(Name = "过期日期")]
    [ExporterHeader("过期日期", Format = "", Width = 25, IsBold = true)]
    public DateTime? ExpirationDate { get; set; }
    
    /// <summary>
    /// 批准文号
    /// </summary>
    [ImporterHeader(Name = "批准文号")]
    [ExporterHeader("批准文号", Format = "", Width = 25, IsBold = true)]
    public string? ApprovalNumber { get; set; }
    
    /// <summary>
    /// 国家医保编码
    /// </summary>
    [ImporterHeader(Name = "国家医保编码")]
    [ExporterHeader("国家医保编码", Format = "", Width = 25, IsBold = true)]
    public string? MedicineCode { get; set; }
    
    /// <summary>
    /// 质量状况 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? QualityStatus { get; set; }
    
    /// <summary>
    /// 质量状况 文本
    /// </summary>
    [Dict("StorageInQualityStatus")]
    [ImporterHeader(Name = "质量状况")]
    [ExporterHeader("质量状况", Format = "", Width = 25, IsBold = true)]
    public string QualityStatusDictLabel { get; set; }
    
    /// <summary>
    /// 验收状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? AcceptanceStatus { get; set; }
    
    /// <summary>
    /// 验收状态 文本
    /// </summary>
    [Dict("StorageInAcceptanceStatus")]
    [ImporterHeader(Name = "验收状态")]
    [ExporterHeader("验收状态", Format = "", Width = 25, IsBold = true)]
    public string AcceptanceStatusDictLabel { get; set; }
    
}
