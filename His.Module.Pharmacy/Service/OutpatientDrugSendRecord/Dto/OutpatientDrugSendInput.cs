using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 门诊发药记录表增加输入参数
/// </summary>
public class AddOutpatientDrugSendInput
{
    /// <summary>
    /// 发药单号
    /// </summary>
    [MaxLength(100, ErrorMessage = "发药单号字符长度不能超过100")]
    public string? SendNo { get; set; }

    /// <summary>
    /// 发药人ID
    /// </summary>
    public long? SendUserId { get; set; }

    /// <summary>
    /// 发药人名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "发药人名称字符长度不能超过100")]
    public string? SendUserName { get; set; }

    /// <summary>
    /// 发药时间
    /// </summary>
    public DateTime? SendTime { get; set; }

    /// <summary>
    /// 审核人ID
    /// </summary>
    public long? AuditUserId { get; set; }

    /// <summary>
    /// 审核人名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "审核人名称字符长度不能超过100")]
    public string? AuditUserName { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 调配人员ID
    /// </summary>
    public long? PickUserId { get; set; }

    /// <summary>
    /// 调配人员名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "调配人员名称字符长度不能超过100")]
    public string? PickUserName { get; set; }

    /// <summary>
    /// 调配时间
    /// </summary>
    public DateTime? PickTime { get; set; }

    /// <summary>
    /// 核对人员ID
    /// </summary>
    public long? CheckUserId { get; set; }

    /// <summary>
    /// 核对人员名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "核对人员名称字符长度不能超过100")]
    public string? CheckUserName { get; set; }

    /// <summary>
    /// 核对时间
    /// </summary>
    public DateTime? CheckTime { get; set; }

    /// <summary>
    /// 药房
    /// </summary>
    public long? StorageId { get; set; }

    /// <summary>
    /// 药房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药房编码字符长度不能超过100")]
    public string? StorageCode { get; set; }

    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }

    /// <summary>
    /// 患者名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "患者名称字符长度不能超过100")]
    public string? PatientName { get; set; }

    /// <summary>
    /// 挂号ID
    /// </summary>
    public long? RegisterId { get; set; }

    /// <summary>
    /// 就诊号
    /// </summary>
    [MaxLength(100, ErrorMessage = "就诊号字符长度不能超过100")]
    public string? VisitNo { get; set; }

    /// <summary>
    /// 卡号
    /// </summary>

    public string? CardNo { get; set; }

    /// <summary>
    /// 门诊号
    /// </summary>
    public string? OutpatientNo { get; set; }


    /// <summary>
    /// 处方ID
    /// </summary>
    public long? PrescriptionId { get; set; }

    /// <summary>
    /// 处方明细ID
    /// </summary>
    public long? PrescriptionDetailId { get; set; }

    /// <summary>
    /// 处方号
    /// </summary>
    [MaxLength(100, ErrorMessage = "处方号字符长度不能超过100")]
    public string? PrescriptionNo { get; set; }

    /// <summary>
    /// 处方时间
    /// </summary>
    public DateTime? PrescriptionTime { get; set; }

    /// <summary>
    /// 处方类型
    /// </summary>
    [MaxLength(100, ErrorMessage = "处方类型字符长度不能超过100")]
    public string? PrescriptionType { get; set; }

    /// <summary>
    /// 库存ID
    /// </summary>
    public long? InventoryId { get; set; }

    /// <summary>
    /// 科室ID
    /// </summary>
    public long? DeptId { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "科室名称字符长度不能超过100")]
    public string? DeptName { get; set; }

    /// <summary>
    /// 医生ID
    /// </summary>
    public long? DoctorId { get; set; }

    /// <summary>
    /// 医生名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "医生名称字符长度不能超过100")]
    public string? DoctorName { get; set; }

    /// <summary>
    /// 发票号
    /// </summary>
    [MaxLength(100, ErrorMessage = "发票号字符长度不能超过100")]
    public string? InvoiceNumber { get; set; }

    /// <summary>
    /// 收费人员ID
    /// </summary>
    public long? ChargeStaffId { get; set; }

    /// <summary>
    /// 收费ID
    /// </summary>
    public long? ChargeId { get; set; }

    /// <summary>
    /// 收费时间
    /// </summary>
    public DateTime? ChargeTime { get; set; }

    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }

    /// <summary>
    /// 药品编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品编码字符长度不能超过100")]
    public string? DrugCode { get; set; }

    /// <summary>
    /// 药品名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品名称字符长度不能超过100")]
    public string? DrugName { get; set; }

    /// <summary>
    /// 药品类型
    /// </summary>
    [Dict("DrugType", AllowNullValue = true)]
    [MaxLength(100, ErrorMessage = "药品类型字符长度不能超过100")]
    public string? DrugType { get; set; }

    /// <summary>
    /// 药品规格
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品规格字符长度不能超过100")]
    public string? Spec { get; set; }

    /// <summary>
    /// 药品单位
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品单位字符长度不能超过100")]
    public string? Unit { get; set; }

    /// <summary>
    /// 发药数量
    /// </summary>
    public decimal? Quantity { get; set; }

    /// <summary>
    /// 单次剂量
    /// </summary>
    public decimal? SingleDose { get; set; }

    /// <summary>
    /// 单次剂量单位
    /// </summary>
    [MaxLength(16, ErrorMessage = "单次剂量单位字符长度不能超过16")]
    public string? SingleDoseUnit { get; set; }

    /// <summary>
    /// 用药途径ID
    /// </summary>
    public long? MedicationRoutesId { get; set; }

    /// <summary>
    /// 用药频次ID
    /// </summary>
    public long? FrequencyId { get; set; }
    
            
    /// <summary>
    /// 用药途径
    /// </summary>
    public string? MedicationRoutesName { get; set; }    
    
    /// <summary>
    /// 用药频次
    /// </summary>
    public string? FrequencyName { get; set; }    
 


    /// <summary>
    /// 用药天数
    /// </summary>
    public Int16? MedicationDays { get; set; }

    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? Price { get; set; }

    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? Amount { get; set; }

    /// <summary>
    /// 草药付数
    /// </summary>
    public int? HerbsQuantity { get; set; }

    /// <summary>
    /// 煎药方法
    /// </summary>
    [Dict("DecoctionMethod", AllowNullValue = true)]
    [MaxLength(128, ErrorMessage = "煎药方法字符长度不能超过128")]
    public string? DecoctionMethod { get; set; }

    /// <summary>
    /// 是否代煎
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? IsDecoction { get; set; }

    /// <summary>
    /// 退药数量
    /// </summary>
    public int? RefundQuantity { get; set; }

    /// <summary>
    /// 总退药金额
    /// </summary>
    public decimal? RefundAmount { get; set; }

    /// <summary>
    /// 批号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批号字符长度不能超过100")]
    public string? BatchNo { get; set; }

    /// <summary>
    /// 生产日期
    /// </summary>
    public DateTime? ProductionDate { get; set; }

    /// <summary>
    /// 有效期
    /// </summary>
    public DateTime? ExpirationDate { get; set; }

    /// <summary>
    /// 批准文号
    /// </summary>
    [MaxLength(100, ErrorMessage = "批准文号字符长度不能超过100")]
    public string? ApprovalNumber { get; set; }

    /// <summary>
    /// 药品通用名编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "药品通用名编码字符长度不能超过100")]
    public string? MedicineCode { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    public long? ManufacturerId { get; set; }

    /// <summary>
    /// 生产厂家名称
    /// </summary>
 
    public string? ManufacturerName { get; set; }
    /// <summary>
    /// 组号
    /// </summary>
    public string? GroupNo { get; set; }
    /// <summary>
    /// 组号标志
    /// </summary>
    public string? GroupFlag { get; set; }
 
 

    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }
}