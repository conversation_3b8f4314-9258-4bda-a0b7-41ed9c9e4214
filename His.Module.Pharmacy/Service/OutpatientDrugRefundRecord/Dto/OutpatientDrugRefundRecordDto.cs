namespace His.Module.Pharmacy;

/// <summary>
/// 门诊退药输出参数
/// </summary>
public class OutpatientDrugRefundRecordDto
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }
    
    /// <summary>
    /// 退药单号
    /// </summary>
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 关联的发药记录ID
    /// </summary>
    public long? SendRecordId { get; set; }
    
    /// <summary>
    /// 退药人ID
    /// </summary>
    public long? RefundUserId { get; set; }
    
    /// <summary>
    /// 退药人名称
    /// </summary>
    public string? RefundUserName { get; set; }
    
    /// <summary>
    /// 退药时间
    /// </summary>
    public DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退药申请id
    /// </summary>
    public long? RefundApplyId { get; set; }
    
    /// <summary>
    /// 审核完成时间
    /// </summary>
    public DateTime? AuditTime { get; set; }
    
    /// <summary>
    /// 退药原因
    /// </summary>
    public string? Reason { get; set; }
    
    /// <summary>
    /// 药房ID
    /// </summary>
    public long? StorageId { get; set; }
    
    /// <summary>
    /// 药房名称
    /// </summary>
    public string? StorageName { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    public long? PatientId { get; set; }
    
    /// <summary>
    /// 患者名称
    /// </summary>
    public string? PatientName { get; set; }
    
    /// <summary>
    /// 就诊号
    /// </summary>
    public string? VisitNo { get; set; }
    
    /// <summary>
    /// 就诊id
    /// </summary>
    public long? VisitId { get; set; }
    
    /// <summary>
    /// 卡号
    /// </summary>
    public string? CardNo { get; set; }
    
    /// <summary>
    /// 卡id
    /// </summary>
    public long? CardId { get; set; }
    
    /// <summary>
    /// 处方ID
    /// </summary>
    public long? PrescriptionId { get; set; }
    
    /// <summary>
    /// 处方明细ID
    /// </summary>
    public long? PrescriptionDetailId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    public long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    public string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    public string? DrugName { get; set; }
    
    /// <summary>
    /// 药品规格
    /// </summary>
    public string? Spec { get; set; }
    
    /// <summary>
    /// 药品单位
    /// </summary>
    public string? Unit { get; set; }
    
    /// <summary>
    /// 退药数量
    /// </summary>
    public decimal? RefundQuantity { get; set; }
    
    /// <summary>
    /// 零售价
    /// </summary>
    public decimal? Price { get; set; }
    
    /// <summary>
    /// 总退药金额
    /// </summary>
    public decimal? RefundAmount { get; set; }
    
    /// <summary>
    /// 批号
    /// </summary>
    public string? BatchNo { get; set; }
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
    
}
