using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品大剂维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugBigFormService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugBigForm> _drugBigFormRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugBigFormService(SqlSugarRepository<DrugBigForm> drugBigFormRep, ISqlSugarClient sqlSugarClient)
    {
        _drugBigFormRep = drugBigFormRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品大剂维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品大剂维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugBigFormOutput>> Page(PageDrugBigFormInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugBigFormRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.BigFormName.Contains(input.Keyword) || u.BigFormNamePinyin.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BigFormName), u => u.BigFormName.Contains(input.BigFormName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.BigFormNamePinyin), u => u.BigFormNamePinyin.Contains(input.BigFormNamePinyin.Trim()))
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<DrugBigFormOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品大剂维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品大剂维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugBigForm> Detail([FromQuery] QueryByIdDrugBigFormInput input)
    {
        return await _drugBigFormRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品大剂维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品大剂维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugBigFormInput input)
    {
        var entity = input.Adapt<DrugBigForm>();
        return await _drugBigFormRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品大剂维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品大剂维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugBigFormInput input)
    {
        var entity = input.Adapt<DrugBigForm>();
        await _drugBigFormRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品大剂维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品大剂维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugBigFormInput input)
    {
        var entity = await _drugBigFormRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugBigFormRep.FakeDeleteAsync(entity);   //假删除
        //await _drugBigFormRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品大剂维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品大剂维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugBigFormInput> input)
    {
        var exp = Expressionable.Create<DrugBigForm>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugBigFormRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugBigFormRep.FakeDeleteAsync(list);   //假删除
        //return await _drugBigFormRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出药品大剂维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品大剂维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugBigFormInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugBigFormOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品大剂维护导出记录");
    }
    
    /// <summary>
    /// 下载药品大剂维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品大剂维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugBigFormOutput>(), "药品大剂维护导入模板");
    }
    
    /// <summary>
    /// 导入药品大剂维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品大剂维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugBigFormInput, DrugBigForm>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugBigForm>>();
                    
                    var storageable = _drugBigFormRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.BigFormName?.Length > 100, "大剂型名称长度不能超过100个字符")
                        .SplitError(it => it.Item.BigFormNamePinyin?.Length > 100, "大剂型名称拼音长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
