using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药品分类维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class DrugCategoryService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<DrugCategory> _drugCategoryRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DrugCategoryService(SqlSugarRepository<DrugCategory> drugCategoryRep, ISqlSugarClient sqlSugarClient)
    {
        _drugCategoryRep = drugCategoryRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药品分类维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药品分类维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<DrugCategoryOutput>> Page(PageDrugCategoryInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _drugCategoryRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.CategoryCode.Contains(input.Keyword) || u.CategoryName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.CategoryCode), u => u.CategoryCode.Contains(input.CategoryCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.CategoryName), u => u.CategoryName.Contains(input.CategoryName.Trim()))
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select<DrugCategoryOutput>();
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药品分类维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药品分类维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<DrugCategory> Detail([FromQuery] QueryByIdDrugCategoryInput input)
    {
        return await _drugCategoryRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药品分类维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药品分类维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddDrugCategoryInput input)
    {
        var entity = input.Adapt<DrugCategory>();
        return await _drugCategoryRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药品分类维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药品分类维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateDrugCategoryInput input)
    {
        var entity = input.Adapt<DrugCategory>();
        await _drugCategoryRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药品分类维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药品分类维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteDrugCategoryInput input)
    {
        var entity = await _drugCategoryRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _drugCategoryRep.FakeDeleteAsync(entity);   //假删除
        //await _drugCategoryRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药品分类维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药品分类维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeleteDrugCategoryInput> input)
    {
        var exp = Expressionable.Create<DrugCategory>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _drugCategoryRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _drugCategoryRep.FakeDeleteAsync(list);   //假删除
        //return await _drugCategoryRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 导出药品分类维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药品分类维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageDrugCategoryInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportDrugCategoryOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药品分类维护导出记录");
    }
    
    /// <summary>
    /// 下载药品分类维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药品分类维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportDrugCategoryOutput>(), "药品分类维护导入模板");
    }
    
    /// <summary>
    /// 导入药品分类维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药品分类维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportDrugCategoryInput, DrugCategory>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<DrugCategory>>();
                    
                    var storageable = _drugCategoryRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.CategoryCode?.Length > 100, "分类编码长度不能超过100个字符")
                        .SplitError(it => it.Item.CategoryName?.Length > 100, "分类名称长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
