using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Pharmacy.Service;

/// <summary>
/// 药理分类维护服务 🧩
/// </summary>
[ApiDescriptionSettings(PharmacyConst.GroupName, Order = 100)]
public class PharmacologicalClassService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<PharmacologicalClass> _pharmacologicalClassRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public PharmacologicalClassService(SqlSugarRepository<PharmacologicalClass> pharmacologicalClassRep, ISqlSugarClient sqlSugarClient)
    {
        _pharmacologicalClassRep = pharmacologicalClassRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询药理分类维护 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询药理分类维护")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<PharmacologicalClassOutput>> Page(PagePharmacologicalClassInput input)
    {
        input.Keyword = input.Keyword?.Trim();
        var query = _pharmacologicalClassRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.ClassCode.Contains(input.Keyword) || u.ClassName.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ClassCode), u => u.ClassCode.Contains(input.ClassCode.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.ClassName), u => u.ClassName.Contains(input.ClassName.Trim()))
            .WhereIF(input.ParentId != null, u => u.ParentId == input.ParentId)
            .WhereIF(input.Status.HasValue, u => u.Status ==(int) input.Status)
            .LeftJoin<PharmacologicalClass>((u, parent) => u.ParentId == parent.Id)
            .Select((u, parent) => new PharmacologicalClassOutput
            {
                Id = u.Id,
                ClassCode = u.ClassCode,
                ClassName = u.ClassName,
                ParentId = u.ParentId,
                ParentFkDisplayName = $"{parent.ClassName}-{parent.ClassCode}",
                Status = (StatusEnum)u.Status,
                CreateTime = u.CreateTime,
                CreateUserId = u.CreateUserId,
                CreateUserName = u.CreateUserName,
                UpdateTime = u.UpdateTime,
                UpdateUserId = u.UpdateUserId,
                UpdateUserName = u.UpdateUserName,
                IsDelete = u.IsDelete,
                TenantId = u.TenantId,
            });
		return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取药理分类维护详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取药理分类维护详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<PharmacologicalClass> Detail([FromQuery] QueryByIdPharmacologicalClassInput input)
    {
        return await _pharmacologicalClassRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加药理分类维护 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加药理分类维护")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddPharmacologicalClassInput input)
    {
        var entity = input.Adapt<PharmacologicalClass>();
        return await _pharmacologicalClassRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新药理分类维护 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新药理分类维护")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdatePharmacologicalClassInput input)
    {
        var entity = input.Adapt<PharmacologicalClass>();
        await _pharmacologicalClassRep.AsUpdateable(entity)
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除药理分类维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除药理分类维护")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeletePharmacologicalClassInput input)
    {
        var entity = await _pharmacologicalClassRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _pharmacologicalClassRep.FakeDeleteAsync(entity);   //假删除
        //await _pharmacologicalClassRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除药理分类维护 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除药理分类维护")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")]List<DeletePharmacologicalClassInput> input)
    {
        var exp = Expressionable.Create<PharmacologicalClass>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _pharmacologicalClassRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();
   
        return await _pharmacologicalClassRep.FakeDeleteAsync(list);   //假删除
        //return await _pharmacologicalClassRep.DeleteAsync(list);   //真删除
    }
    
    /// <summary>
    /// 设置药理分类维护状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置药理分类维护状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetPharmacologicalClassStatus(SetPharmacologicalClassStatusInput input)
    {
        await _pharmacologicalClassRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }
    
    /// <summary>
    /// 获取下拉列表数据 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取下拉列表数据")]
    [ApiDescriptionSettings(Name = "DropdownData"), HttpPost]
    public async Task<Dictionary<string, dynamic>> DropdownData(DropdownDataPharmacologicalClassInput input)
    {
        var parentIdData = await _pharmacologicalClassRep.Context.Queryable<PharmacologicalClass>()
            .InnerJoinIF<PharmacologicalClass>(input.FromPage, (u, r) => u.Id == r.ParentId)
            .Select(u => new {
                Value = u.Id,
                Label = $"{u.ClassName}-{u.ClassCode}"
            }).ToListAsync();
        return new Dictionary<string, dynamic>
        {
            { "parentId", parentIdData },
        };
    }
    
    /// <summary>
    /// 导出药理分类维护记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出药理分类维护记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PagePharmacologicalClassInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportPharmacologicalClassOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "药理分类维护导出记录");
    }
    
    /// <summary>
    /// 下载药理分类维护数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载药理分类维护数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportPharmacologicalClassOutput>(), "药理分类维护导入模板", (_, info) =>
        {
            if (nameof(ExportPharmacologicalClassOutput.ParentFkDisplayName) == info.Name) return _pharmacologicalClassRep.Context.Queryable<PharmacologicalClass>().Select(u => $"{u.ClassName}-{u.ClassCode}").Distinct().ToList();
            return null;
        });
    }
    
    /// <summary>
    /// 导入药理分类维护记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入药理分类维护记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportPharmacologicalClassInput, PharmacologicalClass>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 链接 父级分类ID
                    var parentIdLabelList = pageItems.Where(x => x.ParentFkDisplayName != null).Select(x => x.ParentFkDisplayName).Distinct().ToList();
                    if (parentIdLabelList.Any()) {
                        var parentIdLinkMap = _pharmacologicalClassRep.Context.Queryable<PharmacologicalClass>().Where(u => parentIdLabelList.Contains($"{u.ClassName}-{u.ClassCode}")).ToList().ToDictionary(u => $"{u.ClassName}-{u.ClassCode}", u => u.Id);
                        pageItems.ForEach(e => {
                            e.ParentId = parentIdLinkMap.GetValueOrDefault(e.ParentFkDisplayName ?? "");
                            if (e.ParentId == null) e.Error = "父级分类ID链接失败";
                        });
                    }
                    
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x => {
                        return true;
                    }).Adapt<List<PharmacologicalClass>>();
                    
                    var storageable = _pharmacologicalClassRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.ClassCode?.Length > 100, "药理分类编码长度不能超过100个字符")
                        .SplitError(it => it.Item.ClassName?.Length > 100, "药理分类名称长度不能超过100个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();
                    
                    storageable.BulkCopy();
                    storageable.BulkUpdate();
                    
                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });
            
            return stream;
        }
    }
}
