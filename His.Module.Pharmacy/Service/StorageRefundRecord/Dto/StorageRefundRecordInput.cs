using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品退货基础输入参数
/// </summary>
public class StorageRefundRecordBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 退货单号
    /// </summary>
    public virtual string? RefundNo { get; set; }
    
    /// <summary>
    /// 退货时间
    /// </summary>
    public virtual DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退货类型
    /// </summary>
    [Dict("StorageRefundType", AllowNullValue=true)]
    public virtual string? RefundType { get; set; }
    
    /// <summary>
    /// 退货原因
    /// </summary>
    public virtual string? RefundReason { get; set; }
    
    /// <summary>
    /// 申请科室
    /// </summary>
    public virtual long? ApplyDeptId { get; set; }
    
    /// <summary>
    /// 申请部门编码
    /// </summary>
    public virtual string? ApplyDeptCode { get; set; }
    
    /// <summary>
    /// 申请部门名称
    /// </summary>
    public virtual string? ApplyDeptName { get; set; }
    
    /// <summary>
    /// 目标库房
    /// </summary>
    public virtual long? TargetStorageId { get; set; }
    
    /// <summary>
    /// 目标库房编码
    /// </summary>
    public virtual string? TargetStorageCode { get; set; }
    
    /// <summary>
    /// 目标库房名称
    /// </summary>
    public virtual string? TargetStorageName { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public virtual int? Status { get; set; }
    
}

/// <summary>
/// 药品退货分页查询输入参数
/// </summary>
public class PageStorageRefundRecordInput : BasePageInput
{
    /// <summary>
    /// 退货单号
    /// </summary>
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 退货时间范围
    /// </summary>
     public DateTime?[] RefundTimeRange { get; set; }
    
    /// <summary>
    /// 退货类型
    /// </summary>
    [Dict("StorageRefundType", AllowNullValue=true)]
    public string? RefundType { get; set; }
    
    /// <summary>
    /// 退货原因
    /// </summary>
    public string? RefundReason { get; set; }
    
    /// <summary>
    /// 申请科室
    /// </summary>
    public long? ApplyDeptId { get; set; }
    
    /// <summary>
    /// 申请部门编码
    /// </summary>
    public string? ApplyDeptCode { get; set; }
    
    /// <summary>
    /// 申请部门名称
    /// </summary>
    public string? ApplyDeptName { get; set; }
    
    /// <summary>
    /// 目标库房
    /// </summary>
    public long? TargetStorageId { get; set; }
    
    /// <summary>
    /// 目标库房编码
    /// </summary>
    public string? TargetStorageCode { get; set; }
    
    /// <summary>
    /// 目标库房名称
    /// </summary>
    public string? TargetStorageName { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 药品退货增加输入参数
/// </summary>
public class AddStorageRefundRecordInput
{
    /// <summary>
    /// 退货单号
    /// </summary>
    [MaxLength(100, ErrorMessage = "退货单号字符长度不能超过100")]
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 退货时间
    /// </summary>
    public DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退货类型
    /// </summary>
    [Dict("StorageRefundType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "退货类型字符长度不能超过100")]
    public string? RefundType { get; set; }
    
    /// <summary>
    /// 退货原因
    /// </summary>
    [MaxLength(100, ErrorMessage = "退货原因字符长度不能超过100")]
    public string? RefundReason { get; set; }
    
    /// <summary>
    /// 申请科室
    /// </summary>
    public long? ApplyDeptId { get; set; }
    
    /// <summary>
    /// 申请部门编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "申请部门编码字符长度不能超过100")]
    public string? ApplyDeptCode { get; set; }
    
    /// <summary>
    /// 申请部门名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "申请部门名称字符长度不能超过100")]
    public string? ApplyDeptName { get; set; }
    
    /// <summary>
    /// 目标库房
    /// </summary>
    public long? TargetStorageId { get; set; }
    
    /// <summary>
    /// 目标库房编码
    /// </summary>
    [MaxLength(100, ErrorMessage = "目标库房编码字符长度不能超过100")]
    public string? TargetStorageCode { get; set; }
    
    /// <summary>
    /// 目标库房名称
    /// </summary>
    [MaxLength(100, ErrorMessage = "目标库房名称字符长度不能超过100")]
    public string? TargetStorageName { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    public List<AddStorageRefundDetailInput> Details { get; set; }  
    
}

/// <summary>
/// 药品退货删除输入参数
/// </summary>
public class DeleteStorageRefundRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 药品退货更新输入参数
/// </summary>
public class UpdateStorageRefundRecordInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 退货单号
    /// </summary>    
    [MaxLength(100, ErrorMessage = "退货单号字符长度不能超过100")]
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 退货时间
    /// </summary>    
    public DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退货类型
    /// </summary>    
    [Dict("StorageRefundType", AllowNullValue=true)]
    [MaxLength(100, ErrorMessage = "退货类型字符长度不能超过100")]
    public string? RefundType { get; set; }
    
    /// <summary>
    /// 退货原因
    /// </summary>    
    [MaxLength(100, ErrorMessage = "退货原因字符长度不能超过100")]
    public string? RefundReason { get; set; }
    
    /// <summary>
    /// 申请科室
    /// </summary>    
    public long? ApplyDeptId { get; set; }
    
    /// <summary>
    /// 申请部门编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "申请部门编码字符长度不能超过100")]
    public string? ApplyDeptCode { get; set; }
    
    /// <summary>
    /// 申请部门名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "申请部门名称字符长度不能超过100")]
    public string? ApplyDeptName { get; set; }
    
    /// <summary>
    /// 目标库房
    /// </summary>    
    public long? TargetStorageId { get; set; }
    
    /// <summary>
    /// 目标库房编码
    /// </summary>    
    [MaxLength(100, ErrorMessage = "目标库房编码字符长度不能超过100")]
    public string? TargetStorageCode { get; set; }
    
    /// <summary>
    /// 目标库房名称
    /// </summary>    
    [MaxLength(100, ErrorMessage = "目标库房名称字符长度不能超过100")]
    public string? TargetStorageName { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>    
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict("StorageInOutStatus", AllowNullValue=true)]
    public int? Status { get; set; }
    
    public List<UpdateStorageRefundDetailInput> Details { get; set; }
    
}

public class SubmitStorageRefundRecordInput
{
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 药品退货主键查询输入参数
/// </summary>
public class QueryByIdStorageRefundRecordInput : DeleteStorageRefundRecordInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataStorageRefundRecordInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 药品退货数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportStorageRefundRecordInput : BaseImportInput
{
    /// <summary>
    /// 退货单号
    /// </summary>
    [ImporterHeader(Name = "退货单号")]
    [ExporterHeader("退货单号", Format = "", Width = 25, IsBold = true)]
    public string? RefundNo { get; set; }
    
    /// <summary>
    /// 退货时间
    /// </summary>
    [ImporterHeader(Name = "退货时间")]
    [ExporterHeader("退货时间", Format = "", Width = 25, IsBold = true)]
    public DateTime? RefundTime { get; set; }
    
    /// <summary>
    /// 退货类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? RefundType { get; set; }
    
    /// <summary>
    /// 退货类型 文本
    /// </summary>
    [Dict("")]
    [ImporterHeader(Name = "退货类型")]
    [ExporterHeader("退货类型", Format = "", Width = 25, IsBold = true)]
    public string RefundTypeDictLabel { get; set; }
    
    /// <summary>
    /// 退货原因
    /// </summary>
    [ImporterHeader(Name = "退货原因")]
    [ExporterHeader("退货原因", Format = "", Width = 25, IsBold = true)]
    public string? RefundReason { get; set; }
    
    /// <summary>
    /// 申请科室 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ApplyDeptId { get; set; }
    
    /// <summary>
    /// 申请科室 文本
    /// </summary>
    [ImporterHeader(Name = "申请科室")]
    [ExporterHeader("申请科室", Format = "", Width = 25, IsBold = true)]
    public string ApplyDeptFkDisplayName { get; set; }
    
    /// <summary>
    /// 申请部门编码
    /// </summary>
    [ImporterHeader(Name = "申请部门编码")]
    [ExporterHeader("申请部门编码", Format = "", Width = 25, IsBold = true)]
    public string? ApplyDeptCode { get; set; }
    
    /// <summary>
    /// 申请部门名称
    /// </summary>
    [ImporterHeader(Name = "申请部门名称")]
    [ExporterHeader("申请部门名称", Format = "", Width = 25, IsBold = true)]
    public string? ApplyDeptName { get; set; }
    
    /// <summary>
    /// 目标库房 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? TargetStorageId { get; set; }
    
    /// <summary>
    /// 目标库房 文本
    /// </summary>
    [ImporterHeader(Name = "目标库房")]
    [ExporterHeader("目标库房", Format = "", Width = 25, IsBold = true)]
    public string TargetStorageFkDisplayName { get; set; }
    
    /// <summary>
    /// 目标库房编码
    /// </summary>
    [ImporterHeader(Name = "目标库房编码")]
    [ExporterHeader("目标库房编码", Format = "", Width = 25, IsBold = true)]
    public string? TargetStorageCode { get; set; }
    
    /// <summary>
    /// 目标库房名称
    /// </summary>
    [ImporterHeader(Name = "目标库房名称")]
    [ExporterHeader("目标库房名称", Format = "", Width = 25, IsBold = true)]
    public string? TargetStorageName { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [ImporterHeader(Name = "总零售价")]
    [ExporterHeader("总零售价", Format = "", Width = 25, IsBold = true)]
    public decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 状态 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public int? Status { get; set; }
    
    /// <summary>
    /// 状态 文本
    /// </summary>
    [Dict("StorageInOutStatus")]
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public string StatusDictLabel { get; set; }
    
}
