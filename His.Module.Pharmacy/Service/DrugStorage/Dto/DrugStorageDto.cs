using His.Module.Shared.Api.Enum;

namespace His.Module.Pharmacy;

/// <summary>
/// 药品库房维护输出参数
/// </summary>
public class DrugStorageDto
{
    /// <summary>
    /// 父级库房ID
    /// </summary>
    public string ParentIdFkColumn { get; set; }

    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 库房编码
    /// </summary>
    public string? StorageCode { get; set; }

    /// <summary>
    /// 库房名称
    /// </summary>
    public string? StorageName { get; set; }

    /// <summary>
    /// 存储药品类型
    /// </summary>
    public List<String> StorageDrugType { get; set; }

    /// <summary>
    /// 库存金额上限
    /// </summary>
    public decimal? StorageAmountLimit { get; set; }

    /// <summary>
    /// 父级库房ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 父级库房编码
    /// </summary>
    public string? ParentCode { get; set; }

    /// <summary>
    /// 服务对象（门诊，急诊、住院）
    /// </summary>
    public List<int> ServiceObject { get; set; }

    /// <summary>
    /// 采购入库审核
    /// </summary>
    public YesNoEnum PurchaseAudit { get; set; }

    /// <summary>
    /// 采购退货审核
    /// </summary>
    public YesNoEnum PurchaseReturnAudit { get; set; }

    /// <summary>
    /// 药店申领审核
    /// </summary>
    public YesNoEnum ApplyAudit { get; set; }

    /// <summary>
    /// 药店退药审核
    /// </summary>
    public YesNoEnum ApplyReturnAudit { get; set; }

    /// <summary>
    /// 出库审核
    /// </summary>
    public YesNoEnum OutAudit { get; set; }

    /// <summary>
    /// 特殊处理审核
    /// </summary>
    public YesNoEnum SpecialAudit { get; set; }

    /// <summary>
    /// 按批号盘点
    /// </summary>
    public YesNoEnum BatchCheck { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool? IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
}