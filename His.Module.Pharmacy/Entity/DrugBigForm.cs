using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品大剂型表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_big_form", "药品大剂型表")]
public class DrugBigForm : EntityTenant
{
    /// <summary>
    /// 大剂型名称
    /// </summary>
    [SugarColumn(ColumnName = "big_form_name", ColumnDescription = "大剂型名称", Length = 100)]
    public virtual string? BigFormName { get; set; }
    
    /// <summary>
    /// 大剂型名称拼音
    /// </summary>
    [SugarColumn(ColumnName = "big_form_name_pinyin", ColumnDescription = "大剂型名称拼音", Length = 100)]
    public virtual string? BigFormNamePinyin { get; set; }
    
    /// <summary>
    /// 1 启用 2 停用
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "1 启用 2 停用")]
    public virtual int? Status { get; set; }
    
}
