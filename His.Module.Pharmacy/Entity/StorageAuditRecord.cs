namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品出入库审核记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("storage_audit_record", "药品出入库审核记录表")]
public class StorageAuditRecord : EntityTenantBaseData
{
    /// <summary>
    /// 业务类型：StorageIn-入库,StorageOut-出库,StorageSpecial-特殊处理
    /// </summary>
    [SugarColumn(ColumnName = "business_type", ColumnDescription = "业务类型", Length = 20)]
    public string BusinessType { get; set; }

    /// <summary>
    /// 业务单据ID
    /// </summary>
    [SugarColumn(ColumnName = "business_id", ColumnDescription = "业务单据ID")]
    public long BusinessId { get; set; }

    /// <summary>
    /// 业务单据号
    /// </summary>
    [SugarColumn(ColumnName = "business_no", ColumnDescription = "业务单据号", Length = 50)]
    public string BusinessNo { get; set; }

    /// <summary>
    /// 审核状态：2-待审核,3-审核通过,9-审核拒绝
    /// </summary>
    [SugarColumn(ColumnName = "audit_status", ColumnDescription = "审核状态")]
    public int AuditStatus { get; set; } = 2;

    /// <summary>
    /// 审核意见
    /// </summary>
    [SugarColumn(ColumnName = "audit_opinion", ColumnDescription = "审核意见", ColumnDataType = "text")]
    public string AuditOpinion { get; set; }

    /// <summary>
    /// 审核时间
    /// </summary>
    [SugarColumn(ColumnName = "audit_time", ColumnDescription = "审核时间")]
    public DateTime? AuditTime { get; set; }

    /// <summary>
    /// 审核人Id
    /// </summary>
    [SugarColumn(ColumnName = "auditor_id", ColumnDescription = "审核人Id")]
    public long AuditorId { get; set; }

    /// <summary>
    /// 审核人姓名
    /// </summary>
    [SugarColumn(ColumnName = "auditor_name", ColumnDescription = "审核人姓名", Length = 50)]
    public string AuditorName { get; set; }

}