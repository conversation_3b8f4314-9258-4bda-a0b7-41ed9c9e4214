using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品单位表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_unit", "药品单位表")]
public class DrugUnit : EntityTenant
{
    /// <summary>
    /// 单位名称
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位名称", Length = 100)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 转换单位
    /// </summary>
    [SugarColumn(ColumnName = "convert_unit", ColumnDescription = "转换单位", Length = 100)]
    public virtual string? ConvertUnit { get; set; }
    
    /// <summary>
    /// 转换比率
    /// </summary>
    [SugarColumn(ColumnName = "convert_ratio", ColumnDescription = "转换比率", Length = 20, DecimalDigits=4)]
    public virtual decimal? ConvertRatio { get; set; }
    
    /// <summary>
    /// 单位拼音
    /// </summary>
    [SugarColumn(ColumnName = "unit_pinyin", ColumnDescription = "单位拼音", Length = 100)]
    public virtual string? UnitPinyin { get; set; }
    
    /// <summary>
    /// 状态（1 启用 2 停用）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（1 启用 2 停用）")]
    public virtual int? Status { get; set; }
    
}
