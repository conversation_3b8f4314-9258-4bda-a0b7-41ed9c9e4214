using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 采购计划明细表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("drug_purchase_plan_detail", "采购计划明细表")]
public class DrugPurchasePlanDetail : EntityTenant
{
    /// <summary>
    /// 采购计划ID
    /// </summary>
    [SugarColumn(ColumnName = "plan_id", ColumnDescription = "采购计划ID")]
    public virtual long? PlanId { get; set; }
    
    /// <summary>
    /// 药品ID
    /// </summary>
    [SugarColumn(ColumnName = "drug_id", ColumnDescription = "药品ID")]
    public virtual long? DrugId { get; set; }
    
    /// <summary>
    /// 药品编码
    /// </summary>
    [SugarColumn(ColumnName = "drug_code", ColumnDescription = "药品编码", Length = 100)]
    public virtual string? DrugCode { get; set; }
    
    /// <summary>
    /// 药品名称
    /// </summary>
    [SugarColumn(ColumnName = "drug_name", ColumnDescription = "药品名称", Length = 100)]
    public virtual string? DrugName { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型", Length = 100)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 规格
    /// </summary>
    [SugarColumn(ColumnName = "spec", ColumnDescription = "规格", Length = 100)]
    public virtual string? Spec { get; set; }
    
    /// <summary>
    /// 单位
    /// </summary>
    [SugarColumn(ColumnName = "unit", ColumnDescription = "单位", Length = 100)]
    public virtual string? Unit { get; set; }
    
    /// <summary>
    /// 药店库存数量
    /// </summary>
    [SugarColumn(ColumnName = "pharmacy_quantity", ColumnDescription = "药店库存数量")]
    public virtual int? PharmacyQuantity { get; set; }
    
    /// <summary>
    /// 药库库存数量
    /// </summary>
    [SugarColumn(ColumnName = "storage_quantity", ColumnDescription = "药库库存数量")]
    public virtual int? StorageQuantity { get; set; }
    
    /// <summary>
    /// 当前销售数量
    /// </summary>
    [SugarColumn(ColumnName = "current_sale_quantity", ColumnDescription = "当前销售数量")]
    public virtual int? CurrentSaleQuantity { get; set; }
    
    /// <summary>
    /// 上次销售数量
    /// </summary>
    [SugarColumn(ColumnName = "last_sale_quantity", ColumnDescription = "上次销售数量")]
    public virtual int? LastSaleQuantity { get; set; }
    
    /// <summary>
    /// 平均销售数量
    /// </summary>
    [SugarColumn(ColumnName = "average_sale_quantity", ColumnDescription = "平均销售数量")]
    public virtual int? AverageSaleQuantity { get; set; }
    
    /// <summary>
    /// 计划采购数量
    /// </summary>
    [SugarColumn(ColumnName = "quantity", ColumnDescription = "计划采购数量")]
    public virtual int? Quantity { get; set; }
    
    /// <summary>
    /// 采购单价
    /// </summary>
    [SugarColumn(ColumnName = "purchase_price", ColumnDescription = "采购单价", Length = 20, DecimalDigits=4)]
    public virtual decimal? PurchasePrice { get; set; }
    
    /// <summary>
    /// 总采购价
    /// </summary>
    [SugarColumn(ColumnName = "total_purchase_price", ColumnDescription = "总采购价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 生产厂家ID
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_id", ColumnDescription = "生产厂家ID")]
    public virtual long? ManufacturerId { get; set; }
    
    /// <summary>
    /// 生产厂家名称
    /// </summary>
    [SugarColumn(ColumnName = "manufacturer_name", ColumnDescription = "生产厂家名称", Length = 100)]
    public virtual string? ManufacturerName { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    [SugarColumn(ColumnName = "supplier_id", ColumnDescription = "供应商ID")]
    public virtual long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    [SugarColumn(ColumnName = "supplier_name", ColumnDescription = "供应商名称", Length = 100)]
    public virtual string? SupplierName { get; set; }
    
    /// <summary>
    /// 状态（0 未处理 1 处理中 2 已完成等）
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态（0 未处理 1 处理中 2 已完成等）")]
    public virtual int? Status { get; set; }
    
}
