using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 药品盘点记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("storage_taking_record", "药品盘点记录表")]
public class StorageTakingRecord : EntityTenant
{
    /// <summary>
    /// 盘点单号
    /// </summary>
    [SugarColumn(ColumnName = "taking_no", ColumnDescription = "盘点单号", Length = 100)]
    public virtual string? TakingNo { get; set; }
    
    /// <summary>
    /// 盘点时间
    /// </summary>
    [SugarColumn(ColumnName = "taking_time", ColumnDescription = "盘点时间")]
    public virtual DateTime? TakingTime { get; set; }
    
    /// <summary>
    /// 盘点结果 1 盘盈 2 盘亏
    /// </summary>
    [SugarColumn(ColumnName = "taking_result", ColumnDescription = "盘点结果")]
    public virtual int? TakingResult { get; set; }
    
    /// <summary>
    /// 库房ID
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "库房ID")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [SugarColumn(ColumnName = "storage_code", ColumnDescription = "库房编码", Length = 100)]
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "库房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 现有数量
    /// </summary>
    [SugarColumn(ColumnName = "current_quantity", ColumnDescription = "现有数量")]
    public virtual decimal? CurrentQuantity { get; set; }
    
    /// <summary>
    /// 盘点数量
    /// </summary>
    [SugarColumn(ColumnName = "taking_quantity", ColumnDescription = "盘点数量")]
    public virtual decimal? TakingQuantity { get; set; }
    
    /// <summary>
    /// 现有零售价
    /// </summary>
    [SugarColumn(ColumnName = "current_sale_price", ColumnDescription = "现有零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? CurrentSalePrice { get; set; }
    
    /// <summary>
    /// 盘点零售价
    /// </summary>
    [SugarColumn(ColumnName = "taking_sale_price", ColumnDescription = "盘点零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TakingSalePrice { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
}
