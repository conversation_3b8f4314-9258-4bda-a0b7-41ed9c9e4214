using Admin.NET.Core;
namespace His.Module.Pharmacy.Entity;

/// <summary>
/// 存储特殊处理记录表
/// </summary>
[Tenant("1300000000008")]
[SugarTable("storage_special_record", "存储特殊处理记录表")]
public class StorageSpecialRecord : EntityTenant
{
    /// <summary>
    /// 库房ID
    /// </summary>
    [SugarColumn(ColumnName = "storage_id", ColumnDescription = "库房ID")]
    public virtual long? StorageId { get; set; }
    
    /// <summary>
    /// 库房编码
    /// </summary>
    [SugarColumn(ColumnName = "storage_code", ColumnDescription = "库房编码", Length = 100)]
    public virtual string? StorageCode { get; set; }
    
    /// <summary>
    /// 库房名称
    /// </summary>
    [SugarColumn(ColumnName = "storage_name", ColumnDescription = "库房名称", Length = 100)]
    public virtual string? StorageName { get; set; }
    
    /// <summary>
    /// 特殊处理单号
    /// </summary>
    [SugarColumn(ColumnName = "handle_no", ColumnDescription = "特殊处理单号", Length = 100)]
    public virtual string? HandleNo { get; set; }
    
    /// <summary>
    /// 药品类型
    /// </summary>
    [SugarColumn(ColumnName = "drug_type", ColumnDescription = "药品类型", Length = 100)]
    public virtual string? DrugType { get; set; }
    
    /// <summary>
    /// 供应商ID
    /// </summary>
    [SugarColumn(ColumnName = "supplier_id", ColumnDescription = "供应商ID")]
    public virtual long? SupplierId { get; set; }
    
    /// <summary>
    /// 供应商编码
    /// </summary>
    [SugarColumn(ColumnName = "supplier_code", ColumnDescription = "供应商编码", Length = 100)]
    public virtual string? SupplierCode { get; set; }
    
    /// <summary>
    /// 供应商名称
    /// </summary>
    [SugarColumn(ColumnName = "supplier_name", ColumnDescription = "供应商名称", Length = 100)]
    public virtual string? SupplierName { get; set; }
    
    /// <summary>
    /// 特殊处理类型
    /// </summary>
    [SugarColumn(ColumnName = "handle_type", ColumnDescription = "特殊处理类型", Length = 100)]
    public virtual string? HandleType { get; set; }
    
    /// <summary>
    /// 特殊处理时间
    /// </summary>
    [SugarColumn(ColumnName = "handle_time", ColumnDescription = "特殊处理时间")]
    public virtual DateTime? HandleTime { get; set; }
    
    /// <summary>
    /// 总进价
    /// </summary>
    [SugarColumn(ColumnName = "total_purchase_price", ColumnDescription = "总进价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalPurchasePrice { get; set; }
    
    /// <summary>
    /// 总零售价
    /// </summary>
    [SugarColumn(ColumnName = "total_sale_price", ColumnDescription = "总零售价", Length = 20, DecimalDigits=4)]
    public virtual decimal? TotalSalePrice { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 100)]
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
}
