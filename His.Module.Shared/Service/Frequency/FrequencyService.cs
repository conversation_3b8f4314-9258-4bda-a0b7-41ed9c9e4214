using Microsoft.AspNetCore.Http;

namespace His.Module.Shared.Service;

/// <summary>
/// 频次服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class FrequencyService : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roller, ITransient
{
    private readonly SqlSugarRepository<Frequency> _frequencyRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public FrequencyService(SqlSugarRepository<Frequency> frequencyRep, ISqlSugarClient sqlSugarClient)
    {
        _frequencyRep = frequencyRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询频次 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询频次")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<FrequencyOutput>> Page(PageFrequencyInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.Code = input.Code?.Trim();
        input.Name = input.Name?.Trim().ToLower();
        var query = _frequencyRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword)
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select<FrequencyOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取频次详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取频次详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<Frequency> Detail([FromQuery] QueryByIdFrequencyInput input)
    {
        return await _frequencyRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 获取频次列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [ApiDescriptionSettings(Name = "List")]
    [DisplayName("获取频次列表")]
    public async Task<List<Frequency>> List()
    {
        return await _frequencyRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 增加频次 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加频次")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddFrequencyInput input)
    {
        var entity = input.Adapt<Frequency>();
        entity.Code = await _frequencyRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('frequency_code_seq')As varchar),3,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        return await _frequencyRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新频次 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新频次")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateFrequencyInput input)
    {
        var entity = input.Adapt<Frequency>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _frequencyRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.Code,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除频次 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除频次")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteFrequencyInput input)
    {
        var entity = await _frequencyRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _frequencyRep.FakeDeleteAsync(entity);   //假删除
        //await _frequencyRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除频次 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除频次")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteFrequencyInput> input)
    {
        var exp = Expressionable.Create<Frequency>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _frequencyRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _frequencyRep.FakeDeleteAsync(list);   //假删除
        //return await _frequencyRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置频次状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置频次状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetFrequencyStatus(SetFrequencyStatusInput input)
    {
        await _frequencyRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 导出频次记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出频次记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageFrequencyInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportFrequencyOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "频次导出记录");
    }

    /// <summary>
    /// 下载频次数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载频次数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportFrequencyOutput>(), "频次导入模板");
    }

    /// <summary>
    /// 导入频次记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入频次记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportFrequencyInput, Frequency>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.TimeInterval == null)
                        {
                            x.Error = "时间间隔不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.ExecutionFrequency == null)
                        {
                            x.Error = "执行频率不能为空";
                            return false;
                        }
                        if (!string.IsNullOrWhiteSpace(x.Error)) return false;
                        if (x.UsageScope == null)
                        {
                            x.Error = "使用范围不能为空";
                            return false;
                        }
                        return true;
                    }).Adapt<List<Frequency>>();

                    var storageable = _frequencyRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.Code?.Length > 32, "编码长度不能超过32个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.Name), "名称不能为空")
                        .SplitError(it => it.Item.Name?.Length > 32, "名称长度不能超过32个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 32, "拼音码长度不能超过32个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 32, "五笔码长度不能超过32个字符")
                        .SplitError(it => it.Item.TimeInterval == null, "时间间隔不能为空")
                        .SplitError(it => it.Item.TimeUnit == null, "时间单位不能为空")
                        .SplitError(it => it.Item.ExecutionFrequency == null, "执行频率不能为空")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.ExecutionTime), "执行时间不能为空")
                        .SplitError(it => it.Item.ExecutionTime?.Length > 256, "执行时间长度不能超过256个字符")
                        .SplitError(it => it.Item.Sustain == null, "持续标识不能为空")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}