namespace His.Module.Shared;

/// <summary>
/// 处方类型基础输入参数
/// </summary>
public class PrescriptionTypeBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 可使用的收费类别
    /// </summary>
    public virtual List<long>? ChargeCategorys { get; set; }

    /// <summary>
    /// 处方条目
    /// </summary>
    public virtual int? PrescriptionEntries { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
}

/// <summary>
/// 处方类型分页查询输入参数
/// </summary>
public class PagePrescriptionTypeInput : BasePageInput
{
    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 处方类型增加输入参数
/// </summary>
public class AddPrescriptionTypeInput
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 可使用的收费类别
    /// </summary>
    public List<long>? ChargeCategorys { get; set; }

    /// <summary>
    /// 处方条目
    /// </summary>
    public int? PrescriptionEntries { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 处方类型删除输入参数
/// </summary>
public class DeletePrescriptionTypeInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

/// <summary>
/// 处方类型更新输入参数
/// </summary>
public class UpdatePrescriptionTypeInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(32, ErrorMessage = "名称字符长度不能超过32")]
    public string? Name { get; set; }

    /// <summary>
    /// 可使用的收费类别
    /// </summary>
    public List<long>? ChargeCategorys { get; set; }

    /// <summary>
    /// 处方条目
    /// </summary>
    public int? PrescriptionEntries { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
}

/// <summary>
/// 处方类型主键查询输入参数
/// </summary>
public class QueryByIdPrescriptionTypeInput : DeletePrescriptionTypeInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataPrescriptionTypeInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetPrescriptionTypeStatusInput : BaseStatusInput
{
}

/// <summary>
/// 处方类型数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportPrescriptionTypeInput : BaseImportInput
{
    /// <summary>
    /// 编码
    /// </summary>
    [ImporterHeader(Name = "编码")]
    [ExporterHeader("编码", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [ImporterHeader(Name = "*名称")]
    [ExporterHeader("*名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 可使用的收费类别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? ChargeCategorys { get; set; }

    /// <summary>
    /// 可使用的收费类别 文本
    /// </summary>
    [ImporterHeader(Name = "可使用的收费类别")]
    [ExporterHeader("可使用的收费类别", Format = "", Width = 25, IsBold = true)]
    public string ChargeCategorysFkDisplayName { get; set; }

    /// <summary>
    /// 处方条目
    /// </summary>
    [ImporterHeader(Name = "处方条目")]
    [ExporterHeader("处方条目", Format = "", Width = 25, IsBold = true)]
    public int? PrescriptionEntries { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
}