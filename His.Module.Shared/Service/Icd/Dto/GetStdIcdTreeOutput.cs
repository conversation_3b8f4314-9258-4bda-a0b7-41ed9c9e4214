namespace His.Module.Shared.Service;

public class GetStdIcdTreeOutput
{
    /// <summary>
    /// ICD的所有父节点ID，以逗号分隔。
    /// </summary>
    public string IcdAllPid { get; set; }

    /// <summary>
    /// 当前ICD的唯一标识符ID。
    /// </summary>
    public string IcdId { get; set; }

    /// <summary>
    /// 父级ICD的唯一标识符ID。
    /// </summary>
    public string IcdPid { get; set; }

    /// <summary>
    /// ICD编码。
    /// </summary>
    public string IcdCode { get; set; }

    /// <summary>
    /// ICD名称，如“肠道传染病”。
    /// </summary>
    public string IcdName { get; set; }

    /// <summary>
    /// 部分编号。
    /// </summary>
    public string Partnum { get; set; }

    /// <summary>
    /// 代码长度。
    /// </summary>
    public int? CodeLength { get; set; }

    /// <summary>
    /// 是否为临床（-1表示否，1表示是）。
    /// </summary>
    public int? IsClinical { get; set; }

    /// <summary>
    /// 是否在使用（0表示否，1表示是）。
    /// </summary>
    public int? IsUsing { get; set; }

    /// <summary>
    /// 备注信息。
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 初始化状态。
    /// </summary>
    public string InitializationState { get; set; }

    /// <summary>
    /// 添加该条记录的用户ID。
    /// </summary>
    public string AddUserId { get; set; }

    /// <summary>
    /// 添加该条记录的用户名。
    /// </summary>
    public string AddUserName { get; set; }

    /// <summary>
    /// 添加时间。
    /// </summary>
    public DateTime? AddTime { get; set; }

    /// <summary>
    /// 最后更新该条记录的用户ID。
    /// </summary>
    public string LastUpdateUserId { get; set; }

    /// <summary>
    /// 最后更新该条记录的用户名。
    /// </summary>
    public string LastUpdateUserName { get; set; }

    /// <summary>
    /// 最后更新时间。
    /// </summary>
    public DateTime? LastUpdateTime { get; set; }

    /// <summary>
    /// ICD的层级（1表示一级分类）。
    /// </summary>
    public int? Level { get; set; }

    /// <summary>
    /// 子节点（如果有）。
    /// </summary>
    public object Children { get; set; }

    /// <summary>
    /// 应用说明。
    /// </summary>
    public string ApplyExplain { get; set; }

    /// <summary>
    /// ICD的附件信息。
    /// </summary>
    public object IcdAttachment { get; set; }

    /// <summary>
    /// 分类代码。
    /// </summary>
    public string ClassCode { get; set; }

    /// <summary>
    /// 分类名称，如“A00-A09 肠道传染病”。
    /// </summary>
    public string ClassName { get; set; }

    /// <summary>
    /// 诊断编码。
    /// </summary>
    public string DiagnoseCode { get; set; }

    /// <summary>
    /// 诊断名称。
    /// </summary>
    public string DiagnoseName { get; set; }

    /// <summary>
    /// 是否展开节点。
    /// </summary>
    public string Open { get; set; }

    /// <summary>
    /// 是否为父节点。
    /// </summary>
    public string IsParent { get; set; }

    /// <summary>
    /// 中间ID（通常用于表示某种中间状态或临时标识）。
    /// </summary>
    public string Mid { get; set; }

    /// <summary>
    /// 子标识符，表示子节点的状态。
    /// </summary>
    public int? Subflag { get; set; }

    /// <summary>
    /// 确认状态。
    /// </summary>
    public int? ConfirmStatus { get; set; }

    /// <summary>
    /// 版本代码。
    /// </summary>
    public string VerCode { get; set; }

    /// <summary>
    /// 版本名称。
    /// </summary>
    public string VerName { get; set; }

    /// <summary>
    /// 资源附件信息。
    /// </summary>
    public object ResourceAttachment { get; set; }

    /// <summary>
    /// 参考定义。
    /// </summary>
    public string RefDefine { get; set; }

    /// <summary>
    /// 诊断依据。
    /// </summary>
    public string DiagnosticBasis { get; set; }

    /// <summary>
    /// 版本号。
    /// </summary>
    public string Version { get; set; }

    /// <summary>
    /// 确认人用户ID。
    /// </summary>
    public string ComfirmUserId { get; set; }

    /// <summary>
    /// 确认人用户名。
    /// </summary>
    public string ComfirmUserName { get; set; }

    /// <summary>
    /// 确认时间。
    /// </summary>
    public DateTime? ComfirmTime { get; set; }

    /// <summary>
    /// M代码（可能是与其他系统的关联代码）。
    /// </summary>
    public string Mcode { get; set; }

    /// <summary>
    /// 是否为管理类别的关联。
    /// </summary>
    public int? GlybRel { get; set; }

    /// <summary>
    /// 是否有地图（可能是地理信息）。
    /// </summary>
    public string HasMap { get; set; }

    /// <summary>
    /// 是否有下一个节点。
    /// </summary>
    public string HasNext { get; set; }
}