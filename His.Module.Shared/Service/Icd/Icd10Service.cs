using Yitter.IdGenerator;

namespace His.Module.Shared.Service;

/// <summary>
/// 系统疾病服务
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class Icd10Service : IDynamicApiController, ITransient
{
    //private readonly IIcd10Api _icd10Api;
    private readonly SqlSugarRepository<Icd10> _sysIcd10Rep;

    public Icd10Service(SqlSugarRepository<Icd10> sysIcd10Rep)
    {
        _sysIcd10Rep = sysIcd10Rep;
    }

    /// <summary>
    /// 获取疾病分页列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取疾病分页列表"),SkipPermission]
    public async Task<SqlSugarPagedList<Icd10>> Page(PageIcd10Input input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.Code = input.Code?.Trim().ToUpper();
        input.Name = input.Name?.Trim().ToLower();
        return await _sysIcd10Rep.AsQueryable()
            .WhereIF(input.Pid > 0, u => u.Pid == input.Pid || u.Id == input.Pid)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Code.Contains(input.Keyword.ToUpper())
            || u.Name.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.Level == 5)
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Level == 5)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取疾病列表
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [DisplayName("获取疾病列表")]
    public async Task<List<Icd10>> GetList(long id)
    {
        return await _sysIcd10Rep.AsQueryable()
            .WhereIF(id >= 0, u => u.Pid == id)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }

    /// <summary>
    /// 增加疾病
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    [DisplayName("增加疾病")]
    public async Task AddIcd10(AddIcd10Input input)
    {
        input.Code = input.Code.Trim();
        if (input.Pid > 0)
        {
            var pIcd10 = await _sysIcd10Rep.GetFirstAsync(u => u.Id == input.Pid);
            pIcd10 ??= await _sysIcd10Rep.GetFirstAsync(u => u.Code == input.Code);
            if (pIcd10 == null)
                throw Oops.Oh(ErrorCodeEnum.R2000);
            input.Pid = pIcd10.Id;
        }

        var isExist = await _sysIcd10Rep.IsAnyAsync(u => u.Name == input.Name && u.Code == input.Code);
        if (isExist)
            throw Oops.Oh(ErrorCodeEnum.R2002);

        var sysIcd10 = input.Adapt<Icd10>();
        await _sysIcd10Rep.InsertAsync(sysIcd10);
    }

    /// <summary>
    /// 更新疾病
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    [DisplayName("更新疾病")]
    public async Task UpdateIcd10(UpdateIcd10Input input)
    {
        input.Code = input.Code.Trim();
        var sysIcd10 = await _sysIcd10Rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (sysIcd10.Pid != input.Pid && input.Pid > 0)
        {
            var pIcd10 = await _sysIcd10Rep.GetFirstAsync(u => u.Id == input.Pid);
            pIcd10 ??= await _sysIcd10Rep.GetFirstAsync(u => u.Code == input.Pid.ToString());
            if (pIcd10 == null)
                throw Oops.Oh(ErrorCodeEnum.R2000);

            input.Pid = pIcd10.Id;
            var icd10TreeList = await _sysIcd10Rep.AsQueryable().ToChildListAsync(u => u.Pid, input.Id, true);
            var childIdList = icd10TreeList.Select(u => u.Id).ToList();
            if (childIdList.Contains(input.Pid.Value))
                throw Oops.Oh(ErrorCodeEnum.R2004);
        }

        if (input.Id == input.Pid)
            throw Oops.Oh(ErrorCodeEnum.R2001);

        var isExist = await _sysIcd10Rep.IsAnyAsync(u => (u.Name == input.Name && u.Code == input.Code) && u.Id != sysIcd10.Id);
        if (isExist)
            throw Oops.Oh(ErrorCodeEnum.R2002);

        await _sysIcd10Rep.AsUpdateable(input.Adapt<Icd10>()).IgnoreColumns(true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除疾病
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    [DisplayName("删除疾病")]
    public async Task DeleteRegion(DeleteIcd10Input input)
    {
        var icd10TreeList = await _sysIcd10Rep.AsQueryable().ToChildListAsync(u => u.Pid, input.Id, true);
        var icd10IdList = icd10TreeList.Select(u => u.Id).ToList();
        await _sysIcd10Rep.DeleteAsync(u => icd10IdList.Contains(u.Id));
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task SetStatus(SetStatusIcd10Input input)
    {
        var icd10 = await _sysIcd10Rep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (!System.Enum.IsDefined(typeof(StatusEnum), input.Status))
            throw Oops.Oh(ErrorCodeEnum.D3005);
        icd10.Status = input.Status;
        await _sysIcd10Rep.AsUpdateable(icd10).UpdateColumns(u => new { u.Status }).ExecuteCommandAsync();
    }

    public async Task Sync()
    {
        // 一次性加载所有SysIcd10数据并按Pid分组
        var allSysIcd10 = await _sysIcd10Rep.Context.Queryable<SysIcd10>().ToListAsync();
        var itemsByPid = allSysIcd10
            .GroupBy(u => u.Pid)
            .ToDictionary(g => g.Key, g => g.ToList());

        var allIcd10 = new List<Icd10>();

        // 处理顶级章节（Pid = "0"）
        if (itemsByPid.TryGetValue("0", out var chapters))
        {
            foreach (var chapter in chapters)
            {
                // 创建章节点
                var chapterEntity = CreateIcd10Entity(chapter, null, 1);
                allIcd10.Add(chapterEntity);

                // 递归处理子节点
                ProcessHierarchy(chapter.Id, chapterEntity.Id, 2, itemsByPid, allIcd10);
            }
        }

        // 批量写入所有数据
        _sysIcd10Rep.Context.Fastest<Icd10>().BulkCopy(allIcd10);
    }

    // 递归处理层级结构
    private void ProcessHierarchy(string sysParentId, long icdParentId, int level, Dictionary<string, List<SysIcd10>> dataSource, List<Icd10> resultList)
    {
        if (level > 5) return;

        if (dataSource.TryGetValue(sysParentId, out var children))
        {
            foreach (var child in children)
            {
                var entity = CreateIcd10Entity(child, icdParentId, level);
                resultList.Add(entity);

                ProcessHierarchy(child.Id, entity.Id, level + 1, dataSource, resultList);
            }
        }
    }

    // 创建实体工具方法
    private Icd10 CreateIcd10Entity(SysIcd10 source, long? pid, int level)
    {
        return new Icd10
        {
            Id = YitIdHelper.NextId(),
            Pid = pid ?? 0,
            Name = source.Name,
            PinyinCode = source.PinyinCode,
            WubiCode = source.WubiCode,
            Code = source.Code,
            Level = level,
            MedInsCode = level == 5 ? source.MedInsCode : null
        };
    }

    ///// <summary>
    ///// 同步疾病数据
    ///// </summary>
    ///// <returns></returns>
    //[DisplayName("同步疾病数据")]
    //public async Task Sync()
    //{
    //    //章
    //    var itemList = await _icd10Api.GetStdIcdTreeList("0");
    //    if (itemList.Count == 0)
    //        throw Oops.Oh(ErrorCodeEnum.R2006);
    //    await _sysIcd10Rep.DeleteAsync(u => u.Id != "");
    //    foreach (var item in itemList)
    //    {
    //        var list = new List<SysIcd10>();
    //        var entity = new SysIcd10()
    //        {
    //            Id = item.IcdId,
    //            Pid = "0",
    //            Name = item.ClassName,
    //            PinyinCode = TextUtil.GetFirstPinyin(item.ClassName),
    //            WubiCode = TextUtil.GetFirstWuBi(item.ClassName),
    //            Code = item.Remark,
    //            Level = 1,
    //        };
    //        list.Add(entity);
    //        //节
    //        if (!string.IsNullOrEmpty(item.IcdId))
    //        {
    //            var itemList1 = await _icd10Api.GetStdIcdTreeList(item.IcdId);
    //            foreach (var item1 in itemList1)
    //            {
    //                var entity1 = new SysIcd10()
    //                {
    //                    Id = item1.IcdId,
    //                    Pid = item1.IcdPid,
    //                    Name = item1.IcdName,
    //                    PinyinCode = TextUtil.GetFirstPinyin(item1.IcdName),
    //                    WubiCode = TextUtil.GetFirstWuBi(item1.IcdName),
    //                    Code = item1.IcdCode,
    //                    Level = 2,
    //                };
    //                list.Add(entity1);
    //                //类目
    //                if (!string.IsNullOrEmpty(item1.IcdId))
    //                {
    //                    var itemList2 = await _icd10Api.GetStdIcdTreeList(item1.IcdId);
    //                    foreach (var item2 in itemList2)
    //                    {
    //                        var entity2 = new SysIcd10()
    //                        {
    //                            Id = item2.IcdId,
    //                            Pid = item2.IcdPid,
    //                            Name = item2.IcdName,
    //                            PinyinCode = TextUtil.GetFirstPinyin(item2.IcdName),
    //                            WubiCode = TextUtil.GetFirstWuBi(item2.IcdName),
    //                            Code = item2.IcdCode,
    //                            Level = 3,
    //                        };
    //                        list.Add(entity2);
    //                        //亚目
    //                        if (!string.IsNullOrEmpty(item2.IcdId))
    //                        {
    //                            var itemList3 = await _icd10Api.GetStdIcdTreeList(item2.IcdId);
    //                            foreach (var item3 in itemList3)
    //                            {
    //                                var entity3 = new SysIcd10()
    //                                {
    //                                    Id = item3.IcdId,
    //                                    Pid = item3.IcdPid,
    //                                    Name = item3.IcdName,
    //                                    PinyinCode = TextUtil.GetFirstPinyin(item3.IcdName),
    //                                    WubiCode = TextUtil.GetFirstWuBi(item3.IcdName),
    //                                    Code = item3.IcdCode,
    //                                    Level = 4,
    //                                };
    //                                list.Add(entity3);
    //                                //疾病信息
    //                                if (!string.IsNullOrEmpty(item3.IcdId))
    //                                {
    //                                    var itemList4 = await _icd10Api.GetStdIcdDetail(new GetStdIcdDetailInput()
    //                                    {
    //                                        icdId = "[\"" + item3.IcdId + "\"]"
    //                                    });
    //                                    var context = BrowsingContext.New(AngleSharp.Configuration.Default.WithDefaultLoader());
    //                                    var dom = await context.OpenAsync(u => u.Content(itemList4));
    //                                    var divTags = dom.QuerySelectorAll(".els-doc-con");
    //                                    foreach (var divTag in divTags)
    //                                    {
    //                                        var aTags = divTag.QuerySelectorAll("a");
    //                                        var spanTag = divTag.QuerySelector("span");
    //                                        var entity4 = new SysIcd10()
    //                                        {
    //                                            Id = divTag.Id,
    //                                            Pid = entity3.Id,
    //                                            Name = spanTag.TextContent.Trim(),
    //                                            PinyinCode = TextUtil.GetFirstPinyin(spanTag.TextContent.Trim()),
    //                                            WubiCode = TextUtil.GetFirstWuBi(spanTag.TextContent.Trim()),
    //                                            Code = aTags[0].TextContent.Trim(),
    //                                            MedInsCode = aTags[0].TextContent.Trim(),
    //                                            Level = 5,
    //                                        };
    //                                        list.Add(entity4);
    //                                        if (aTags.Length > 1)
    //                                        {
    //                                            var itemList5 = await _icd10Api.GetIcdMapDetail(divTag.Id);
    //                                            var entity5 = new SysIcd10()
    //                                            {
    //                                                Id = itemList5[0].IcdId,
    //                                                Pid = itemList5[0].IcdPid,
    //                                                Name = itemList5[0].IcdName,
    //                                                PinyinCode = TextUtil.GetFirstPinyin(itemList5[0].IcdName),
    //                                                WubiCode = TextUtil.GetFirstWuBi(itemList5[0].IcdName),
    //                                                Code = itemList5[0].IcdCode,
    //                                                MedInsCode = entity4.Code,
    //                                                Level = 5,
    //                                            };
    //                                            list.Add(entity5);
    //                                        }
    //                                    }
    //                                }
    //                            }
    //                        }
    //                    }
    //                }
    //            }
    //        }
    //        //按章同步快速写入提升同步效率
    //        _sysIcd10Rep.Context.Fastest<SysIcd10>().BulkCopy(list);
    //    }
    //}
}