//using Furion.RemoteRequest;

//namespace His.Core.Service;

//public interface IIcd10Api : IHttpDispatchProxy
//{
//    /// <summary>
//	/// 获取疾病列表
//	/// </summary>
//	/// <param name="icdId">疾病的唯一标识符ID</param>
//	/// <returns></returns>
//	[Get("https://code.nhsa.gov.cn/jbzd/public/toStdIcdTreeList.html")]
//    Task<List<GetStdIcdTreeOutput>> GetStdIcdTreeList([QueryString] string icdId);

//    /// <summary>
//	/// 获取疾病映射关系
//	/// </summary>
//	/// <param name="icdId">疾病的唯一标识符ID</param>
//	/// <returns></returns>
//	[Get("https://code.nhsa.gov.cn/jbzd/public/getIcdMapDetail.html")]
//    Task<List<GetStdIcdTreeOutput>> GetIcdMapDetail([QueryString] string icdId);

//    /// <summary>
//    /// 获取疾病详情
//    /// </summary>
//    /// <param name="input"></param>
//    /// <returns></returns>
//    [Post("https://code.nhsa.gov.cn/jbzd/public/toStdIcdDetail.html", ContentType = "application/x-www-form-urlencoded")]
//    Task<string> GetStdIcdDetail([Body("application/x-www-form-urlencoded")] GetStdIcdDetailInput input);
//}