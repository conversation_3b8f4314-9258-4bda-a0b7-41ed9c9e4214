using Admin.NET.Core;
using System.ComponentModel.DataAnnotations;

namespace His.Module.Shared;

/// <summary>
/// 中医证型基础输入参数
/// </summary>
public class TcmSyndromeBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }
    
    /// <summary>
    /// 中医证型编码
    /// </summary>
    [Required(ErrorMessage = "中医证型编码不能为空")]
    public virtual string? TcmSyndromeCode { get; set; }
    
    /// <summary>
    /// 中医证型名称
    /// </summary>
    [Required(ErrorMessage = "中医证型名称不能为空")]
    public virtual string? TcmSyndromeName { get; set; }
    
    /// <summary>
    /// 版本
    /// </summary>
    public virtual string? Version { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public virtual StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }
    
}

/// <summary>
/// 中医证型分页查询输入参数
/// </summary>
public class PageTcmSyndromeInput : BasePageInput
{
    /// <summary>
    /// 中医证型编码
    /// </summary>
    public string? TcmSyndromeCode { get; set; }
    
    /// <summary>
    /// 中医证型名称
    /// </summary>
    public string? TcmSyndromeName { get; set; }
    
    /// <summary>
    /// 选中主键列表
    /// </summary>
     public List<long> SelectKeyList { get; set; }
}

/// <summary>
/// 中医证型增加输入参数
/// </summary>
public class AddTcmSyndromeInput
{
    /// <summary>
    /// 中医证型编码
    /// </summary>
    [Required(ErrorMessage = "中医证型编码不能为空")]
    [MaxLength(255, ErrorMessage = "中医证型编码字符长度不能超过255")]
    public string? TcmSyndromeCode { get; set; }
    
    /// <summary>
    /// 中医证型名称
    /// </summary>
    [Required(ErrorMessage = "中医证型名称不能为空")]
    [MaxLength(255, ErrorMessage = "中医证型名称字符长度不能超过255")]
    public string? TcmSyndromeName { get; set; }
    
    /// <summary>
    /// 版本
    /// </summary>
    [MaxLength(32, ErrorMessage = "版本字符长度不能超过32")]
    public string? Version { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }
    
}

/// <summary>
/// 中医证型删除输入参数
/// </summary>
public class DeleteTcmSyndromeInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
}

/// <summary>
/// 中医证型更新输入参数
/// </summary>
public class UpdateTcmSyndromeInput
{
    /// <summary>
    /// 主键Id
    /// </summary>    
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
    
    /// <summary>
    /// 中医证型编码
    /// </summary>    
    [Required(ErrorMessage = "中医证型编码不能为空")]
    [MaxLength(255, ErrorMessage = "中医证型编码字符长度不能超过255")]
    public string? TcmSyndromeCode { get; set; }
    
    /// <summary>
    /// 中医证型名称
    /// </summary>    
    [Required(ErrorMessage = "中医证型名称不能为空")]
    [MaxLength(255, ErrorMessage = "中医证型名称字符长度不能超过255")]
    public string? TcmSyndromeName { get; set; }
    
    /// <summary>
    /// 版本
    /// </summary>    
    [MaxLength(32, ErrorMessage = "版本字符长度不能超过32")]
    public string? Version { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>    
    [MaxLength(128, ErrorMessage = "备注字符长度不能超过128")]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>    
    [Dict(nameof(StatusEnum), AllowNullValue=true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>    
    public int? OrderNo { get; set; }
    
}

/// <summary>
/// 中医证型主键查询输入参数
/// </summary>
public class QueryByIdTcmSyndromeInput : DeleteTcmSyndromeInput
{
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetTcmSyndromeStatusInput : BaseStatusInput
{
}

/// <summary>
/// 中医证型数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportTcmSyndromeInput : BaseImportInput
{
    /// <summary>
    /// 中医证型编码
    /// </summary>
    [ImporterHeader(Name = "*中医证型编码")]
    [ExporterHeader("*中医证型编码", Format = "", Width = 25, IsBold = true)]
    public string? TcmSyndromeCode { get; set; }
    
    /// <summary>
    /// 中医证型名称
    /// </summary>
    [ImporterHeader(Name = "*中医证型名称")]
    [ExporterHeader("*中医证型名称", Format = "", Width = 25, IsBold = true)]
    public string? TcmSyndromeName { get; set; }
    
    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }
    
    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }
    
    /// <summary>
    /// 版本
    /// </summary>
    [ImporterHeader(Name = "版本")]
    [ExporterHeader("版本", Format = "", Width = 25, IsBold = true)]
    public string? Version { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }
    
    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }
    
}
