using System.Data;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared;

/// <summary>
/// 中医诊断服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class TcmDiagnosisService : IDynamic<PERSON>pi<PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<TcmDiagnosis> _tcmDiagnosisRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public TcmDiagnosisService(SqlSugarRepository<TcmDiagnosis> tcmDiagnosisRep, ISqlSugarClient sqlSugarClient)
    {
        _tcmDiagnosisRep = tcmDiagnosisRep;
        _sqlSugarClient = sqlSugarClient;
    }

    /// <summary>
    /// 分页查询中医诊断 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询中医诊断")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<TcmDiagnosisOutput>> Page(PageTcmDiagnosisInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.TcmDiagnosisCode = input.TcmDiagnosisCode?.Trim();
        input.TcmDiagnosisName = input.TcmDiagnosisName?.Trim().ToLower();
        var query = _tcmDiagnosisRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.TcmDiagnosisCode.Contains(input.Keyword)
            || u.TcmDiagnosisName.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TcmDiagnosisCode), u => u.TcmDiagnosisCode.Contains(input.TcmDiagnosisCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.TcmDiagnosisName), u => u.TcmDiagnosisName.Contains(input.TcmDiagnosisName)
            || u.PinyinCode.Contains(input.TcmDiagnosisName)
            || u.WubiCode.Contains(input.TcmDiagnosisName))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select<TcmDiagnosisOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    //添加诊断数据
    [DisplayName("添加诊断数据")]
    [ApiDescriptionSettings(Name = "AddTcmDiagnosis"), HttpPost]
    public async Task<bool> AddTcmDiagnosis()
    {
        var dataTable1 = await _sqlSugarClient.Ado.GetDataTableAsync("SELECT * FROM \"shared\".\"XT_ZYZD\"");
        var tcmDiagnosis = new List<TcmDiagnosis>();
        foreach (DataRow item in dataTable1.Rows)
        {
            var entity = new TcmDiagnosis
            {
                TcmDiagnosisCode = item["VZYZD_CODE"].ToString(),
                TcmDiagnosisName = item["VZYZD_NAME"].ToString(),
                Version = item["VVERSION"].ToString(),
                Status = 1
            };
            entity.PinyinCode = TextUtil.GetFirstPinyin(entity.TcmDiagnosisName);
            entity.WubiCode = TextUtil.GetFirstWuBi(entity.TcmDiagnosisName);
            tcmDiagnosis.Add(entity);
        }
        var dataTable2 = await _sqlSugarClient.Ado.GetDataTableAsync("SELECT * FROM \"shared\".\"XT_ZYZD_ZH\"");
        var tcmSyndrome = new List<TcmSyndrome>();
        foreach (DataRow item in dataTable2.Rows)
        {
            var entity = new TcmSyndrome
            {
                TcmSyndromeCode = item["VZYZDZH_CODE"].ToString(),
                TcmSyndromeName = item["VZYZDZH_NAME"].ToString(),
                Version = item["VVERSION"].ToString(),
                Status = 1,
            };
            entity.PinyinCode = TextUtil.GetFirstPinyin(entity.TcmSyndromeName);
            entity.WubiCode = TextUtil.GetFirstWuBi(entity.TcmSyndromeName);
            tcmSyndrome.Add(entity);
        }
        await _tcmDiagnosisRep.ChangeRepository<SqlSugarRepository<TcmSyndrome>>().InsertRangeAsync(tcmSyndrome);
        return await _tcmDiagnosisRep.InsertRangeAsync(tcmDiagnosis);
    }

    /// <summary>
    /// 获取中医诊断详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取中医诊断详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<TcmDiagnosis> Detail([FromQuery] QueryByIdTcmDiagnosisInput input)
    {
        return await _tcmDiagnosisRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加中医诊断 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加中医诊断")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddTcmDiagnosisInput input)
    {
        var entity = input.Adapt<TcmDiagnosis>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.TcmDiagnosisName);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.TcmDiagnosisName);
        return await _tcmDiagnosisRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新中医诊断 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新中医诊断")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateTcmDiagnosisInput input)
    {
        var entity = input.Adapt<TcmDiagnosis>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.TcmDiagnosisName);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.TcmDiagnosisName);
        await _tcmDiagnosisRep.AsUpdateable(entity).ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除中医诊断 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除中医诊断")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteTcmDiagnosisInput input)
    {
        var entity = await _tcmDiagnosisRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _tcmDiagnosisRep.FakeDeleteAsync(entity);   //假删除
        //await _tcmDiagnosisRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除中医诊断 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除中医诊断")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteTcmDiagnosisInput> input)
    {
        var exp = Expressionable.Create<TcmDiagnosis>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _tcmDiagnosisRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _tcmDiagnosisRep.FakeDeleteAsync(list);   //假删除
        //return await _tcmDiagnosisRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 设置中医诊断状态 🚫
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置中医诊断状态")]
    [ApiDescriptionSettings(Name = "SetStatus"), HttpPost]
    public async Task SetTcmDiagnosisStatus(SetTcmDiagnosisStatusInput input)
    {
        await _tcmDiagnosisRep.AsUpdateable().SetColumns(u => u.Status, input.Status).Where(u => u.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    /// 导出中医诊断记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出中医诊断记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageTcmDiagnosisInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportTcmDiagnosisOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "中医诊断导出记录");
    }

    /// <summary>
    /// 下载中医诊断数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载中医诊断数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportTcmDiagnosisOutput>(), "中医诊断导入模板");
    }

    /// <summary>
    /// 导入中医诊断记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入中医诊断记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportTcmDiagnosisInput, TcmDiagnosis>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        return true;
                    }).Adapt<List<TcmDiagnosis>>();

                    var storageable = _tcmDiagnosisRep.Context.Storageable(rows)
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.TcmDiagnosisCode), "中医诊断编码不能为空")
                        .SplitError(it => it.Item.TcmDiagnosisCode?.Length > 64, "中医诊断编码长度不能超过64个字符")
                        .SplitError(it => string.IsNullOrWhiteSpace(it.Item.TcmDiagnosisName), "中医诊断名称不能为空")
                        .SplitError(it => it.Item.TcmDiagnosisName?.Length > 64, "中医诊断名称长度不能超过64个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 64, "拼音码长度不能超过64个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 64, "五笔码长度不能超过64个字符")
                        .SplitError(it => it.Item.Version?.Length > 32, "版本长度不能超过32个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}