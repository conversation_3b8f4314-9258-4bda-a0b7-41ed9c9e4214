namespace His.Module.Shared;

/// <summary>
/// 给药途径输出参数
/// </summary>
public class MedicationRoutesOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }    
    
    /// <summary>
    /// 途径编码
    /// </summary>
    public string? RouteCode { get; set; }    
    
    /// <summary>
    /// 途径名称
    /// </summary>
    public string? RouteName { get; set; }    
    
    /// <summary>
    /// 拼音码
    /// </summary>
    public string? PinyinCode { get; set; }    
    
    /// <summary>
    /// 五笔码
    /// </summary>
    public string? WubiCode { get; set; }    
    
    /// <summary>
    /// 缩写
    /// </summary>
    public string? Abbreviation { get; set; }    
    
    /// <summary>
    /// 分类
    /// </summary>
    public string? RouteCategory { get; set; }    
    
    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }    
    
    /// <summary>
    /// 状态
    /// </summary>
    public int? Status { get; set; }    
    
    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }    
    
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }    
    
    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }    
    
    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }    
    
    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }    
    
    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }    
    
    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }    
    
    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }    
    
    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }    
    
}

/// <summary>
/// 给药途径数据导入模板实体
/// </summary>
public class ExportMedicationRoutesOutput : ImportMedicationRoutesInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}
