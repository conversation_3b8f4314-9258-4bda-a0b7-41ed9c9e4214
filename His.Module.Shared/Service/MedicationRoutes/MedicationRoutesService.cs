using Admin.NET.Core.Service;
using Microsoft.AspNetCore.Http;

namespace His.Module.Shared;

/// <summary>
/// 给药途径服务 🧩
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class MedicationRoutesService : IDynamicApiController, ITransient
{
    private readonly SqlSugarRepository<MedicationRoutes> _medicationRoutesRep;
    private readonly ISqlSugarClient _sqlSugarClient;
    private readonly SysDictTypeService _sysDictTypeService;

    public MedicationRoutesService(SqlSugarRepository<MedicationRoutes> medicationRoutesRep, ISqlSugarClient sqlSugarClient, SysDictTypeService sysDictTypeService)
    {
        _medicationRoutesRep = medicationRoutesRep;
        _sqlSugarClient = sqlSugarClient;
        _sysDictTypeService = sysDictTypeService;
    }

    /// <summary>
    /// 分页查询给药途径 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询给药途径")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost]
    public async Task<SqlSugarPagedList<MedicationRoutesOutput>> Page(PageMedicationRoutesInput input)
    {
        input.Keyword = input.Keyword?.Trim().ToLower();
        input.RouteCode = input.RouteCode?.Trim();
        input.RouteName = input.RouteName?.Trim().ToLower();
        var query = _medicationRoutesRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword), u => u.RouteCode.Contains(input.Keyword)
            || u.RouteName.Contains(input.Keyword)
            || u.PinyinCode.Contains(input.Keyword)
            || u.WubiCode.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RouteCode), u => u.RouteCode.Contains(input.RouteCode))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RouteName), u => u.RouteName.Contains(input.RouteName)
            || u.PinyinCode.Contains(input.RouteName)
            || u.WubiCode.Contains(input.RouteName))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .Select<MedicationRoutesOutput>();
        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取给药途径详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取给药途径详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<MedicationRoutes> Detail([FromQuery] QueryByIdMedicationRoutesInput input)
    {
        return await _medicationRoutesRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    /// 增加给药途径 ➕
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("增加给药途径")]
    [ApiDescriptionSettings(Name = "Add"), HttpPost]
    public async Task<long> Add(AddMedicationRoutesInput input)
    {
        var entity = input.Adapt<MedicationRoutes>();
        entity.RouteCode = await _medicationRoutesRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('medication_routes_code_seq')As varchar),3,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.RouteName);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.RouteName);
        return await _medicationRoutesRep.InsertAsync(entity) ? entity.Id : 0;
    }

    /// <summary>
    /// 更新给药途径 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新给药途径")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task Update(UpdateMedicationRoutesInput input)
    {
        var entity = input.Adapt<MedicationRoutes>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.RouteName);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.RouteName);
        await _medicationRoutesRep.AsUpdateable(entity)
        .IgnoreColumns(u => new
        {
            u.RouteCode,
        })
        .ExecuteCommandAsync();
    }

    /// <summary>
    /// 删除给药途径 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除给药途径")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteMedicationRoutesInput input)
    {
        var entity = await _medicationRoutesRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _medicationRoutesRep.FakeDeleteAsync(entity);   //假删除
        //await _medicationRoutesRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除给药途径 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除给药途径")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteMedicationRoutesInput> input)
    {
        var exp = Expressionable.Create<MedicationRoutes>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _medicationRoutesRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _medicationRoutesRep.FakeDeleteAsync(list);   //假删除
        //return await _medicationRoutesRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 导出给药途径记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出给药途径记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageMedicationRoutesInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportMedicationRoutesOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        var routeCategoryDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "RouteCategory" }).Result.ToDictionary(x => x.Value, x => x.Label);
        list.ForEach(e =>
        {
            e.RouteCategoryDictLabel = routeCategoryDictMap.GetValueOrDefault(e.RouteCategory ?? "", e.RouteCategory);
        });
        return ExcelHelper.ExportTemplate(list, "给药途径导出记录");
    }

    /// <summary>
    /// 下载给药途径数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载给药途径数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportMedicationRoutesOutput>(), "给药途径导入模板");
    }

    /// <summary>
    /// 导入给药途径记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入给药途径记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var routeCategoryDictMap = _sysDictTypeService.GetDataList(new GetDataDictTypeInput { Code = "RouteCategory" }).Result.ToDictionary(x => x.Label!, x => x.Value);
            var stream = ExcelHelper.ImportData<ImportMedicationRoutesInput, MedicationRoutes>(file, (list, markerErrorAction) =>
            {
                _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                {
                    // 映射字典值
                    foreach (var item in pageItems)
                    {
                        if (string.IsNullOrWhiteSpace(item.RouteCategoryDictLabel)) continue;
                        item.RouteCategory = routeCategoryDictMap.GetValueOrDefault(item.RouteCategoryDictLabel);
                        if (item.RouteCategory == null) item.Error = "分类字典映射失败";
                    }

                    // 校验并过滤必填基本类型为null的字段
                    var rows = pageItems.Where(x =>
                    {
                        return true;
                    }).Adapt<List<MedicationRoutes>>();

                    var storageable = _medicationRoutesRep.Context.Storageable(rows)
                        .SplitError(it => it.Item.RouteCode?.Length > 64, "途径编码长度不能超过64个字符")
                        .SplitError(it => it.Item.RouteName?.Length > 64, "途径名称长度不能超过64个字符")
                        .SplitError(it => it.Item.PinyinCode?.Length > 32, "拼音码长度不能超过32个字符")
                        .SplitError(it => it.Item.WubiCode?.Length > 32, "五笔码长度不能超过32个字符")
                        .SplitError(it => it.Item.Abbreviation?.Length > 64, "缩写长度不能超过64个字符")
                        .SplitError(it => it.Item.RouteCategory?.Length > 64, "分类长度不能超过64个字符")
                        .SplitError(it => it.Item.Remark?.Length > 128, "备注长度不能超过128个字符")
                        .SplitInsert(_ => true)
                        .ToStorage();

                    storageable.BulkCopy();
                    storageable.BulkUpdate();

                    // 标记错误信息
                    markerErrorAction.Invoke(storageable, pageItems, rows);
                });
            });

            return stream;
        }
    }
}