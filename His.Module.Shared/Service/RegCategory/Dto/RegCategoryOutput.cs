namespace His.Module.Shared.Service;

/// <summary>
/// 挂号类别输出参数
/// </summary>
public class RegCategoryOutput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    public string? WubiCode { get; set; }

    /// <summary>
    /// 挂号费
    /// </summary>
    public decimal? RegistrationFee { get; set; }

    /// <summary>
    /// 诊疗费
    /// </summary>
    public decimal? ConsultationFee { get; set; }

    /// <summary>
    /// 收费项目
    /// </summary>
    public long? ChargeItemId { get; set; }

    /// <summary>
    /// 收费项目 描述
    /// </summary>
    public string ChargeItemFkDisplayName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }

    /// <summary>
    /// 创建者Id
    /// </summary>
    public long? CreateUserId { get; set; }

    /// <summary>
    /// 创建者姓名
    /// </summary>
    public string? CreateUserName { get; set; }

    /// <summary>
    /// 修改者Id
    /// </summary>
    public long? UpdateUserId { get; set; }

    /// <summary>
    /// 修改者姓名
    /// </summary>
    public string? UpdateUserName { get; set; }

    /// <summary>
    /// 软删除
    /// </summary>
    public bool IsDelete { get; set; }

    /// <summary>
    /// 租户Id
    /// </summary>
    public long? TenantId { get; set; }
}

/// <summary>
/// 挂号类别数据导入模板实体
/// </summary>
public class ExportRegCategoryOutput : ImportRegCategoryInput
{
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public override string Error { get; set; }
}