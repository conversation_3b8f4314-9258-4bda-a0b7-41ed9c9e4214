namespace His.Module.Shared.Service;

/// <summary>
/// 收费项目基础输入参数
/// </summary>
public class ChargeItemBaseInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    public virtual long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public virtual string? Unit { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    public virtual string? Spec { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [Required(ErrorMessage = "单价不能为空")]
    public virtual decimal? Price { get; set; }

    /// <summary>
    /// 进价
    /// </summary>
    public virtual decimal? PurchasePrice { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    public virtual string? Model { get; set; }

    /// <summary>
    /// 批件产品名称
    /// </summary>
    public virtual string? ApprovalName { get; set; }

    /// <summary>
    /// 产地
    /// </summary>
    public virtual string? Producer { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    public virtual string? Manufacturer { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    public virtual string? RegistrationNumber { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>
    public virtual string? PriceCode { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    [Required(ErrorMessage = "收费类别不能为空")]
    public virtual long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 核算类别
    /// </summary>
    [Required(ErrorMessage = "核算类别不能为空")]
    public virtual long? CalculateCategoryId { get; set; }

    /// <summary>
    /// 电子发票费用类别
    /// </summary>
    [Dict("ElectronicInvoiceFeeCategory", AllowNullValue = true)]
    [Required(ErrorMessage = "电子发票费用类别不能为空")]
    public virtual string? DzfpChargeCategory { get; set; }

    /// <summary>
    /// 病案首页费用类别
    /// </summary>
    [Dict("MedicalRecordFeeCategory", AllowNullValue = true)]
    [Required(ErrorMessage = "病案首页费用类别不能为空")]
    public virtual string? BasyChargeCategory { get; set; }

    /// <summary>
    /// 是否高值耗材
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? HighValue { get; set; }

    /// <summary>
    /// 是否单用
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "是否单用不能为空")]
    public virtual YesNoEnum? UseSeparately { get; set; }

    /// <summary>
    /// 是否上传地纬
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "是否上传地纬不能为空")]
    public virtual YesNoEnum? UploadDw { get; set; }

    /// <summary>
    /// 频次
    /// </summary>
    public virtual long? FrequencyId { get; set; }

    /// <summary>
    /// 样本类型
    /// </summary>
    [Dict("SampleType", AllowNullValue = true)]
    public virtual string? SampleType { get; set; }

    /// <summary>
    /// 护理等级
    /// </summary>
    [Dict("NurseLevel", AllowNullValue = true)]
    public virtual string? NurseLevel { get; set; }

    /// <summary>
    /// 检查类别
    /// </summary>
    public virtual long? CheckCategoryId { get; set; }

    /// <summary>
    /// 退费模式
    /// </summary>
    public virtual int? RefundMode { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public virtual YesNoEnum? Package { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    public virtual long? UseDepts { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public virtual MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public virtual string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public virtual StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    public virtual long? CheckPointId { get; set; }
}

/// <summary>
/// 收费项目分页查询输入参数
/// </summary>
public class PageChargeItemInput : BasePageInput
{
    /// <summary>
    /// 编码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    public long? UseDepts { get; set; }

    /// <summary>
    /// 选中主键列表
    /// </summary>
    public List<long> SelectKeyList { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    public YesNoEnum Package { get; set; }
}

/// <summary>
/// 收费项目增加输入参数
/// </summary>
public class AddChargeItemInput
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(64, ErrorMessage = "名称字符长度不能超过64")]
    public string? Name { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(32, ErrorMessage = "单位字符长度不能超过32")]
    public string? Unit { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(64, ErrorMessage = "规格字符长度不能超过64")]
    public string? Spec { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [Required(ErrorMessage = "单价不能为空")]
    public decimal? Price { get; set; }

    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    [MaxLength(64, ErrorMessage = "型号字符长度不能超过64")]
    public string? Model { get; set; }

    /// <summary>
    /// 批件产品名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "批件产品名称字符长度不能超过128")]
    public string? ApprovalName { get; set; }

    /// <summary>
    /// 产地
    /// </summary>
    [MaxLength(8, ErrorMessage = "产地字符长度不能超过8")]
    public string? Producer { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    [MaxLength(256, ErrorMessage = "生产厂家字符长度不能超过256")]
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    [MaxLength(128, ErrorMessage = "注册证号字符长度不能超过128")]
    public string? RegistrationNumber { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>
    [MaxLength(32, ErrorMessage = "物价编码字符长度不能超过32")]
    public string? PriceCode { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    [Required(ErrorMessage = "收费类别不能为空")]
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 核算类别
    /// </summary>
    [Required(ErrorMessage = "核算类别不能为空")]
    public long? CalculateCategoryId { get; set; }

    /// <summary>
    /// 电子发票费用类别
    /// </summary>
    [Dict("ElectronicInvoiceFeeCategory", AllowNullValue = true)]
    //[Required(ErrorMessage = "电子发票费用类别不能为空")]
    [MaxLength(32, ErrorMessage = "电子发票费用类别字符长度不能超过32")]
    public string? DzfpChargeCategory { get; set; }

    /// <summary>
    /// 病案首页费用类别
    /// </summary>
    [Dict("MedicalRecordFeeCategory", AllowNullValue = true)]
    //[Required(ErrorMessage = "病案首页费用类别不能为空")]
    [MaxLength(32, ErrorMessage = "病案首页费用类别字符长度不能超过32")]
    public string? BasyChargeCategory { get; set; }

    /// <summary>
    /// 是否高值耗材
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? HighValue { get; set; }

    /// <summary>
    /// 是否单用
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "是否单用不能为空")]
    public YesNoEnum? UseSeparately { get; set; }

    /// <summary>
    /// 是否上传地纬
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "是否上传地纬不能为空")]
    public YesNoEnum? UploadDw { get; set; }

    /// <summary>
    /// 频次
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 样本类型
    /// </summary>
    [Dict("SampleType", AllowNullValue = true)]
    [MaxLength(32, ErrorMessage = "样本类型字符长度不能超过32")]
    public string? SampleType { get; set; }

    /// <summary>
    /// 护理等级
    /// </summary>
    [Dict("NurseLevel", AllowNullValue = true)]
    [MaxLength(32, ErrorMessage = "护理等级字符长度不能超过32")]
    public string? NurseLevel { get; set; }

    /// <summary>
    /// 检查类别
    /// </summary>
    public long? CheckCategoryId { get; set; }

    /// <summary>
    /// 退费模式
    /// </summary>
    public int? RefundMode { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? Package { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    public long? UseDepts { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注字符长度不能超过256")]
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    public long? CheckPointId { get; set; }
}

/// <summary>
/// 收费项目删除输入参数
/// </summary>
public class DeleteChargeItemInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }
}

public class ChargeItemListInput : BasePageInput
{
    /// <summary>
    /// 处方类型编码
    /// </summary>

    public string? PrescriptionTypeCode { get; set; }

    /// <summary>
    /// 检查类别Id
    /// </summary>
    public long? ExaminationTypeId { get; set; }          

    /// <summary>
    /// 收费类别Id
    /// </summary>
    public long? ChargeCategoryId { get; set; }
}

/// <summary>
/// 收费项目更新输入参数
/// </summary>
public class UpdateChargeItemInput
{
    /// <summary>
    /// 主键Id
    /// </summary>
    [Required(ErrorMessage = "主键Id不能为空")]
    public long? Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    [MaxLength(64, ErrorMessage = "名称字符长度不能超过64")]
    public string? Name { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [MaxLength(32, ErrorMessage = "单位字符长度不能超过32")]
    public string? Unit { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [MaxLength(64, ErrorMessage = "规格字符长度不能超过64")]
    public string? Spec { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [Required(ErrorMessage = "单价不能为空")]
    public decimal? Price { get; set; }

    /// <summary>
    /// 进价
    /// </summary>
    public decimal? PurchasePrice { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    [MaxLength(64, ErrorMessage = "型号字符长度不能超过64")]
    public string? Model { get; set; }

    /// <summary>
    /// 批件产品名称
    /// </summary>
    [MaxLength(128, ErrorMessage = "批件产品名称字符长度不能超过128")]
    public string? ApprovalName { get; set; }

    /// <summary>
    /// 产地
    /// </summary>
    [MaxLength(8, ErrorMessage = "产地字符长度不能超过8")]
    public string? Producer { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    [MaxLength(256, ErrorMessage = "生产厂家字符长度不能超过256")]
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    [MaxLength(128, ErrorMessage = "注册证号字符长度不能超过128")]
    public string? RegistrationNumber { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>
    [MaxLength(32, ErrorMessage = "物价编码字符长度不能超过32")]
    public string? PriceCode { get; set; }

    /// <summary>
    /// 收费类别
    /// </summary>
    [Required(ErrorMessage = "收费类别不能为空")]
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 核算类别
    /// </summary>
    [Required(ErrorMessage = "核算类别不能为空")]
    public long? CalculateCategoryId { get; set; }

    /// <summary>
    /// 电子发票费用类别
    /// </summary>
    [Dict("ElectronicInvoiceFeeCategory", AllowNullValue = true)]
    [Required(ErrorMessage = "电子发票费用类别不能为空")]
    [MaxLength(32, ErrorMessage = "电子发票费用类别字符长度不能超过32")]
    public string? DzfpChargeCategory { get; set; }

    /// <summary>
    /// 病案首页费用类别
    /// </summary>
    [Dict("MedicalRecordFeeCategory", AllowNullValue = true)]
    [Required(ErrorMessage = "病案首页费用类别不能为空")]
    [MaxLength(32, ErrorMessage = "病案首页费用类别字符长度不能超过32")]
    public string? BasyChargeCategory { get; set; }

    /// <summary>
    /// 是否高值耗材
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? HighValue { get; set; }

    /// <summary>
    /// 是否单用
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "是否单用不能为空")]
    public YesNoEnum? UseSeparately { get; set; }

    /// <summary>
    /// 是否上传地纬
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "是否上传地纬不能为空")]
    public YesNoEnum? UploadDw { get; set; }

    /// <summary>
    /// 频次
    /// </summary>
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 样本类型
    /// </summary>
    [Dict("SampleType", AllowNullValue = true)]
    [MaxLength(32, ErrorMessage = "样本类型字符长度不能超过32")]
    public string? SampleType { get; set; }

    /// <summary>
    /// 护理等级
    /// </summary>
    [Dict("NurseLevel", AllowNullValue = true)]
    [MaxLength(32, ErrorMessage = "护理等级字符长度不能超过32")]
    public string? NurseLevel { get; set; }

    /// <summary>
    /// 检查类别
    /// </summary>
    public long? CheckCategoryId { get; set; }

    /// <summary>
    /// 退费模式
    /// </summary>
    public int? RefundMode { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    [Dict(nameof(YesNoEnum), AllowNullValue = true)]
    public YesNoEnum? Package { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    public long? UseDepts { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [Dict(nameof(MedServiceCategoryEnum), AllowNullValue = true)]
    [Required(ErrorMessage = "使用范围不能为空")]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [MaxLength(256, ErrorMessage = "备注字符长度不能超过256")]
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [Dict(nameof(StatusEnum), AllowNullValue = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int? OrderNo { get; set; }

    /// <summary>
    /// 检查部位
    /// </summary>
    public long? CheckPointId { get; set; }
}

/// <summary>
/// 收费项目主键查询输入参数
/// </summary>
public class QueryByIdChargeItemInput : DeleteChargeItemInput
{
}

/// <summary>
/// 下拉数据输入参数
/// </summary>
public class DropdownDataChargeItemInput
{
    /// <summary>
    /// 是否用于分页查询
    /// </summary>
    public bool FromPage { get; set; }
}

/// <summary>
/// 设置状态输入参数
/// </summary>
public class SetChargeItemStatusInput : BaseStatusInput
{
}

/// <summary>
/// 收费项目数据导入实体
/// </summary>
[ExcelImporter(SheetIndex = 1, IsOnlyErrorRows = true)]
public class ImportChargeItemInput : BaseImportInput
{
    /// <summary>
    /// 编码
    /// </summary>
    [ImporterHeader(Name = "编码")]
    [ExporterHeader("编码", Format = "", Width = 25, IsBold = true)]
    public string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [ImporterHeader(Name = "*名称")]
    [ExporterHeader("*名称", Format = "", Width = 25, IsBold = true)]
    public string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [ImporterHeader(Name = "拼音码")]
    [ExporterHeader("拼音码", Format = "", Width = 25, IsBold = true)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [ImporterHeader(Name = "五笔码")]
    [ExporterHeader("五笔码", Format = "", Width = 25, IsBold = true)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    [ImporterHeader(Name = "单位")]
    [ExporterHeader("单位", Format = "", Width = 25, IsBold = true)]
    public string? Unit { get; set; }

    /// <summary>
    /// 规格
    /// </summary>
    [ImporterHeader(Name = "规格")]
    [ExporterHeader("规格", Format = "", Width = 25, IsBold = true)]
    public string? Spec { get; set; }

    /// <summary>
    /// 单价
    /// </summary>
    [ImporterHeader(Name = "*单价")]
    [ExporterHeader("*单价", Format = "", Width = 25, IsBold = true)]
    public decimal? Price { get; set; }

    /// <summary>
    /// 进价
    /// </summary>
    [ImporterHeader(Name = "进价")]
    [ExporterHeader("进价", Format = "", Width = 25, IsBold = true)]
    public decimal? PurchasePrice { get; set; }

    /// <summary>
    /// 型号
    /// </summary>
    [ImporterHeader(Name = "型号")]
    [ExporterHeader("型号", Format = "", Width = 25, IsBold = true)]
    public string? Model { get; set; }

    /// <summary>
    /// 批件产品名称
    /// </summary>
    [ImporterHeader(Name = "批件产品名称")]
    [ExporterHeader("批件产品名称", Format = "", Width = 25, IsBold = true)]
    public string? ApprovalName { get; set; }

    /// <summary>
    /// 产地
    /// </summary>
    [ImporterHeader(Name = "产地")]
    [ExporterHeader("产地", Format = "", Width = 25, IsBold = true)]
    public string? Producer { get; set; }

    /// <summary>
    /// 生产厂家
    /// </summary>
    [ImporterHeader(Name = "生产厂家")]
    [ExporterHeader("生产厂家", Format = "", Width = 25, IsBold = true)]
    public string? Manufacturer { get; set; }

    /// <summary>
    /// 注册证号
    /// </summary>
    [ImporterHeader(Name = "注册证号")]
    [ExporterHeader("注册证号", Format = "", Width = 25, IsBold = true)]
    public string? RegistrationNumber { get; set; }

    /// <summary>
    /// 物价编码
    /// </summary>
    [ImporterHeader(Name = "物价编码")]
    [ExporterHeader("物价编码", Format = "", Width = 25, IsBold = true)]
    public string? PriceCode { get; set; }

    /// <summary>
    /// 收费类别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? ChargeCategoryId { get; set; }

    /// <summary>
    /// 收费类别 文本
    /// </summary>
    [ImporterHeader(Name = "*收费类别")]
    [ExporterHeader("*收费类别", Format = "", Width = 25, IsBold = true)]
    public string ChargeCategoryFkDisplayName { get; set; }

    /// <summary>
    /// 核算类别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? CalculateCategoryId { get; set; }

    /// <summary>
    /// 核算类别 文本
    /// </summary>
    [ImporterHeader(Name = "*核算类别")]
    [ExporterHeader("*核算类别", Format = "", Width = 25, IsBold = true)]
    public string CalculateCategoryFkDisplayName { get; set; }

    /// <summary>
    /// 电子发票费用类别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? DzfpChargeCategory { get; set; }

    /// <summary>
    /// 电子发票费用类别 文本
    /// </summary>
    [Dict("ElectronicInvoiceFeeCategory")]
    [ImporterHeader(Name = "*电子发票费用类别")]
    [ExporterHeader("*电子发票费用类别", Format = "", Width = 25, IsBold = true)]
    public string DzfpChargeCategoryDictLabel { get; set; }

    /// <summary>
    /// 病案首页费用类别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? BasyChargeCategory { get; set; }

    /// <summary>
    /// 病案首页费用类别 文本
    /// </summary>
    [Dict("MedicalRecordFeeCategory")]
    [ImporterHeader(Name = "*病案首页费用类别")]
    [ExporterHeader("*病案首页费用类别", Format = "", Width = 25, IsBold = true)]
    public string BasyChargeCategoryDictLabel { get; set; }

    /// <summary>
    /// 是否高值耗材
    /// </summary>
    [ImporterHeader(Name = "是否高值耗材")]
    [ExporterHeader("是否高值耗材", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? HighValue { get; set; }

    /// <summary>
    /// 是否单用
    /// </summary>
    [ImporterHeader(Name = "*是否单用")]
    [ExporterHeader("*是否单用", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? UseSeparately { get; set; }

    /// <summary>
    /// 是否上传地纬
    /// </summary>
    [ImporterHeader(Name = "*是否上传地纬")]
    [ExporterHeader("*是否上传地纬", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? UploadDw { get; set; }

    /// <summary>
    /// 频次 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? FrequencyId { get; set; }

    /// <summary>
    /// 频次 文本
    /// </summary>
    [ImporterHeader(Name = "频次")]
    [ExporterHeader("频次", Format = "", Width = 25, IsBold = true)]
    public string FrequencyFkDisplayName { get; set; }

    /// <summary>
    /// 样本类型 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? SampleType { get; set; }

    /// <summary>
    /// 样本类型 文本
    /// </summary>
    [Dict("SampleType")]
    [ImporterHeader(Name = "样本类型")]
    [ExporterHeader("样本类型", Format = "", Width = 25, IsBold = true)]
    public string SampleTypeDictLabel { get; set; }

    /// <summary>
    /// 护理等级 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public string? NurseLevel { get; set; }

    /// <summary>
    /// 护理等级 文本
    /// </summary>
    [Dict("NurseLevel")]
    [ImporterHeader(Name = "护理等级")]
    [ExporterHeader("护理等级", Format = "", Width = 25, IsBold = true)]
    public string NurseLevelDictLabel { get; set; }

    /// <summary>
    /// 检查类别 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? CheckCategoryId { get; set; }

    /// <summary>
    /// 检查类别 文本
    /// </summary>
    [ImporterHeader(Name = "检查类别")]
    [ExporterHeader("检查类别", Format = "", Width = 25, IsBold = true)]
    public string CheckCategoryFkDisplayName { get; set; }

    /// <summary>
    /// 退费模式
    /// </summary>
    [ImporterHeader(Name = "退费模式")]
    [ExporterHeader("退费模式", Format = "", Width = 25, IsBold = true)]
    public int? RefundMode { get; set; }

    /// <summary>
    /// 是否套餐
    /// </summary>
    [ImporterHeader(Name = "是否套餐")]
    [ExporterHeader("是否套餐", Format = "", Width = 25, IsBold = true)]
    public YesNoEnum? Package { get; set; }

    /// <summary>
    /// 使用科室
    /// </summary>
    [ImporterHeader(Name = "使用科室")]
    [ExporterHeader("使用科室", Format = "", Width = 25, IsBold = true)]
    public long? UseDepts { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    [ImporterHeader(Name = "*使用范围")]
    [ExporterHeader("*使用范围", Format = "", Width = 25, IsBold = true)]
    public MedServiceCategoryEnum? UsageScope { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [ImporterHeader(Name = "备注")]
    [ExporterHeader("备注", Format = "", Width = 25, IsBold = true)]
    public string? Remark { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [ImporterHeader(Name = "状态")]
    [ExporterHeader("状态", Format = "", Width = 25, IsBold = true)]
    public StatusEnum? Status { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [ImporterHeader(Name = "排序")]
    [ExporterHeader("排序", Format = "", Width = 25, IsBold = true)]
    public int? OrderNo { get; set; }

    /// <summary>
    /// 检查部位 关联值
    /// </summary>
    [ImporterHeader(IsIgnore = true)]
    [ExporterHeader(IsIgnore = true)]
    public long? CheckPointId { get; set; }

    /// <summary>
    /// 检查部位 文本
    /// </summary>
    [ImporterHeader(Name = "检查部位")]
    [ExporterHeader("检查部位", Format = "", Width = 25, IsBold = true)]
    public string CheckPointFkDisplayName { get; set; }
}