using His.Module.Shared.Enum;

namespace His.Module.Shared.Service;

/// <summary>
/// 系统支付方式服务
/// </summary>
[ApiDescriptionSettings(SharedConst.GroupName, Order = 100)]
public class PayMethodService : ID<PERSON><PERSON>pi<PERSON>ontroller, ITransient
{
    private readonly SqlSugarRepository<PayMethod> _PayMethodRep;

    public PayMethodService(SqlSugarRepository<PayMethod> PayMethodRep)
    {
        _PayMethodRep = PayMethodRep;
    }

    /// <summary>
    /// 分页查询系统支付方式
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Page")]
    public async Task<SqlSugarPagedList<PayMethod>> Page(PayMethodInput input)
    {
        input.Name = input.Name?.ToLower();
        return await _PayMethodRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Code), u => u.Code.Contains(input.Code))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Name), u => u.Name.Contains(input.Name)
            || u.PinyinCode.Contains(input.Name)
            || u.WubiCode.Contains(input.Name))
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 增加系统支付方式
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Add")]
    public async Task Add(AddPayMethodInput input)
    {
        //var isExist = await _PayMethodRep.AsQueryable().ClearFilter().AnyAsync(u => u.Code == input.Code);
        //if (isExist) throw Oops.Oh(SharedErrorCodeEnum.S0001);
        var entity = input.Adapt<PayMethod>();
        entity.Code = await _PayMethodRep.Context.Ado.GetStringAsync("SELECT LPAD(CAST(NEXTVAL('pay_method_code_seq')As varchar),3,'0')");
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _PayMethodRep.InsertAsync(entity);
    }

    /// <summary>
    /// 删除系统支付方式
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Delete")]
    public async Task Delete(DeletePayMethodInput input)
    {
        var entity = await _PayMethodRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        await _PayMethodRep.FakeDeleteAsync(entity);   //假删除
        //await _PayMethodRep.DeleteAsync(entity);
    }

    /// <summary>
    /// 更新系统支付方式
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [ApiDescriptionSettings(Name = "Update")]
    public async Task Update(UpdatePayMethodInput input)
    {
        var isExist = await _PayMethodRep.AsQueryable().ClearFilter().AnyAsync(u => u.Id == input.Id);
        if (!isExist) throw Oops.Oh(ErrorCodeEnum.D1002);
        isExist = await _PayMethodRep.AsQueryable().ClearFilter().AnyAsync(u => u.Code == input.Code && u.Id != input.Id);
        if (isExist) throw Oops.Oh(SharedErrorCodeEnum.S0001);
        var entity = input.Adapt<PayMethod>();
        entity.PinyinCode = TextUtil.GetFirstPinyin(entity.Name);
        entity.WubiCode = TextUtil.GetFirstWuBi(entity.Name);
        await _PayMethodRep.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
    }

    /// <summary>
    /// 设置状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("设置状态")]
    public async Task SetStatus(SetStatusPayMethodInput input)
    {
        var payMethod = await _PayMethodRep.GetFirstAsync(u => u.Id == input.Id) ?? throw Oops.Oh(ErrorCodeEnum.D1002);
        if (!System.Enum.IsDefined(typeof(StatusEnum), input.Status))
            throw Oops.Oh(ErrorCodeEnum.D3005);
        payMethod.Status = input.Status;
        await _PayMethodRep.AsUpdateable(payMethod).UpdateColumns(u => new { u.Status }).ExecuteCommandAsync();
    }

    /// <summary>
    /// 获取支付类型列表
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取支付类型列表")]
    public async Task<List<PayMethod>> GetList()
    {
        return await _PayMethodRep.AsQueryable()
            .Where(u => u.Status == StatusEnum.Enable)
            .OrderBy(u => new { u.OrderNo, u.Id })
            .ToListAsync();
    }
}