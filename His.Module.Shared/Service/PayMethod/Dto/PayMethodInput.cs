using His.Module.Shared.Entity;

namespace His.Module.Shared.Service;

/// <summary>
/// 门诊支付类型分页查询输入参数
/// </summary>
public class PayMethodInput : BasePageInput
{
    /// <summary>
    /// 编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
/// 门诊支付类型增加输入参数
/// </summary>
public class AddPayMethodInput : PayMethod
{
    /// <summary>
    /// 名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public override string Name { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    [Required(ErrorMessage = "类型不能为空")]
    public override string Type { get; set; }
}

/// <summary>
/// 门诊支付类型删除输入参数
/// </summary>
public class DeletePayMethodInput : BaseIdInput
{
}

/// <summary>
/// 门诊支付类型更新输入参数
/// </summary>
public class UpdatePayMethodInput : AddPayMethodInput
{
}

/// <summary>
/// 设置支付类型状态输入参数
/// </summary>
public class SetStatusPayMethodInput : BaseIdInput
{
    /// <summary>
    /// 状态
    /// </summary>
    public StatusEnum Status { get; set; }
}