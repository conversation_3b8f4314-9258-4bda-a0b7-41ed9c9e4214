namespace His.Module.Shared.Entity;

/// <summary>
/// 检查部位表
/// </summary>
[Tenant("1300000000014")]
[SugarTable("check_point", "检查部位表")]
public class CheckPoint : EntityTenant
{
    /// <summary>
    /// 编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "编码", Length = 32)]
    public virtual string? Code { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 32)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 32)]
    public virtual string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 32)]
    public virtual string? WubiCode { get; set; }

    /// <summary>
    /// 检查类别Id
    /// </summary>
    [SugarColumn(ColumnName = "check_category_id", ColumnDescription = "检查类别Id", Length = 512)]
    public virtual long? CheckCategoryId { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 排序
    /// </summary>
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public virtual int? OrderNo { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public virtual string? Remark { get; set; }
}