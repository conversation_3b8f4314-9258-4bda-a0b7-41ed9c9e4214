using Admin.NET.Core;
namespace His.Module.Shared.Entity;

/// <summary>
/// 科室结构维护详情
/// </summary>
[Tenant("1300000000014")]
[SugarTable("sys_org_struct_detail", "科室结构维护详情")]
public class SysOrgStructDetail : EntityTenant
{
    /// <summary>
    /// 结构id
    /// </summary>
    [SugarColumn(ColumnName = "struct_id", ColumnDescription = "结构id")]
    public virtual long? StructId { get; set; }
    /// <summary>
    /// 结构名称
    /// </summary>
    [SugarColumn(ColumnName = "struct_code", ColumnDescription = "结构编号", Length = 255)]
    public virtual string? StructCode { get; set; }
    /// <summary>
    /// 结构名称
    /// </summary>
    [SugarColumn(ColumnName = "struct_name", ColumnDescription = "结构名称", Length = 255)]
    public virtual string? StructName { get; set; }
    
    /// <summary>
    /// 科室id
    /// </summary>
    [SugarColumn(ColumnName = "dept_id", ColumnDescription = "科室id")]
    public virtual long? DeptId { get; set; }
    
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_code", ColumnDescription = "科室编号", Length = 255)]
    public virtual string? DeptCode { get; set; }
    /// <summary>
    /// 科室名称
    /// </summary>
    [SugarColumn(ColumnName = "dept_name", ColumnDescription = "科室名称", Length = 255)]
    public virtual string? DeptName { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 255)]
    public virtual string? Remark { get; set; }
    
}
