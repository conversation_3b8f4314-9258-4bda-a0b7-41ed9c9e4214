namespace His.Module.Shared.Entity;

/// <summary>
/// 系统疾病编码
/// </summary>
[SugarTable(null, "系统疾病编码")]
[Tenant("1300000000014")]
public class Icd10 : EntityTenant
{
    /// <summary>
    /// 父Id
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "pid", ColumnDescription = "父Id")]
    public long? Pid { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(ColumnName = "name", ColumnDescription = "名称", Length = 128)]
    public virtual string? Name { get; set; }

    /// <summary>
    /// 拼音码
    /// </summary>
    [SugarColumn(ColumnName = "pinyin_code", ColumnDescription = "拼音码", Length = 64)]
    public string? PinyinCode { get; set; }

    /// <summary>
    /// 五笔码
    /// </summary>
    [SugarColumn(ColumnName = "wubi_code", ColumnDescription = "五笔码", Length = 64)]
    public string? WubiCode { get; set; }

    /// <summary>
    /// 国临编码
    /// </summary>
    [SugarColumn(ColumnName = "code", ColumnDescription = "国临编码", Length = 128)]
    public string? Code { get; set; }

    /// <summary>
    /// 医保编码
    /// </summary>
    [SugarColumn(ColumnName = "med_ins_code", ColumnDescription = "医保编码", Length = 128)]
    public string? MedInsCode { get; set; }

    /// <summary>
    /// 省医保编码
    /// </summary>
    [SugarColumn(ColumnName = "pro_med_ins_code", ColumnDescription = "省医保编码", Length = 128)]
    public string? ProMedInsCode { get; set; }

    /// <summary>
    /// 层级
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "level", ColumnDescription = "层级")]
    public int Level { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "order_no", ColumnDescription = "排序")]
    public int OrderNo { get; set; } = 100;

    /// <summary>
    /// 状态
    /// </summary>
    [Required]
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public StatusEnum Status { get; set; } = StatusEnum.Enable;

    /// <summary>
    /// 备注
    /// </summary>
    [SugarColumn(ColumnName = "remark", ColumnDescription = "备注", Length = 128)]
    public string? Remark { get; set; }
}