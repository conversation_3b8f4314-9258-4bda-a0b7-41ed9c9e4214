using Admin.NET.Core.Service;
using His.Module.Inpatient.Api.Api;
using His.Module.Inpatient.OtherModuleEntity;
using Microsoft.AspNetCore.Http;

namespace His.Module.Inpatient.Service;

/// <summary>
/// 住院预约服务 🧩
/// </summary>
[ApiDescriptionSettings(InpatientConst.GroupName, Order = 100)]
public class AppointmentRecordService : IDynamicApiController, ITransient, IAppointmentApi
{
    private readonly SqlSugarRepository<AppointmentRecord> _appointmentRecordRep;


    private readonly SqlSugarRepository<MedicalCardInfo> _medicalCardRep;
    private readonly SqlSugarRepository<OutpatientRegister> _outpatientRegisterRep;
    private readonly ISqlSugarClient _sqlSugarClient;

    public AppointmentRecordService(SqlSugarRepository<AppointmentRecord> appointmentRecordRep,
        SqlSugarRepository<OutpatientRegister> outpatientRegisterRep,
        SqlSugarRepository<MedicalCardInfo> medicalCardRep,
        ISqlSugarClient sqlSugarClient)
    {
        _appointmentRecordRep = appointmentRecordRep;
        _sqlSugarClient = sqlSugarClient;
        _outpatientRegisterRep = outpatientRegisterRep;
        _medicalCardRep = medicalCardRep;
    }

    /// <summary>
    /// 分页查询住院预约 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("分页查询住院预约")]
    [ApiDescriptionSettings(Name = "Page"), HttpPost,SkipPermission]
    public async Task<SqlSugarPagedList<AppointmentRecordOutput>> Page(PageAppointmentRecordInput input)
    {
        input.Keyword = input.Keyword?.Trim();

        var query = _appointmentRecordRep.AsTenant().QueryableWithAttr<AppointmentRecord>()
            .WhereIF(!string.IsNullOrWhiteSpace(input.Keyword),
                u => u.PatientName.Contains(input.Keyword) || u.IdCardType.Contains(input.Keyword) ||
                     u.IdCardNo.Contains(input.Keyword) || u.InsuranceNo.Contains(input.Keyword) ||
                     u.DeptName.Contains(input.Keyword) ||
                     u.DoctorName.Contains(input.Keyword) ||
                     u.DiagnosticName.Contains(input.Keyword) ||
                     u.Remark.Contains(input.Keyword))
            .WhereIF(!string.IsNullOrWhiteSpace(input.PatientName),
                u => u.PatientName.Contains(input.PatientName.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardType), u => u.IdCardType.Contains(input.IdCardType.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.IdCardNo), u => u.IdCardNo.Contains(input.IdCardNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.InsuranceNo),
                u => u.InsuranceNo.Contains(input.InsuranceNo.Trim()))
            .WhereIF(!string.IsNullOrWhiteSpace(input.Remark), u => u.Remark.Contains(input.Remark.Trim()))
            .WhereIF(input.PatientId != null, u => u.PatientId == input.PatientId)
            .WhereIF(input.AppointmentTimeRange?.Length == 2,
                u => u.AppointmentTime >= input.AppointmentTimeRange[0] &&
                     u.AppointmentTime <= input.AppointmentTimeRange[1])
            .WhereIF(input.DeptId != null, u => u.DeptId == input.DeptId) 
            .WhereIF(input.VisitNo != null, u => u.VisitNo == input.VisitNo) 
            
        
            .WhereIF(input.DoctorId != null, u => u.DoctorId == input.DoctorId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .InnerJoin<PatientInfo>((a, p) => a.PatientId == p.Id)
            .InnerJoin<MedicalCardInfo>((a, p, m) => a.MedicalCardId == m.Id)
            .InnerJoin<SysUser>((a, p, m, u) => u.Id == a.DoctorId)
            .InnerJoin<SysOrg>((a, p, m, u, o) => o.Id == a.DeptId)
            .WhereIF(input.Status != null, u => u.Status == input.Status)
            .Select((a, p, m, u, o) =>
                new AppointmentRecordOutput
                {
                    Id = a.Id,
                    PatientId = a.PatientId,
                    PatientName = p.Name,
                    Sex = p.Sex,
                    Age = p.Age,
                    AgeUnit = p.AgeUnit,
                    Birthday = p.Birthday,
                    InsuranceNo = a.InsuranceNo,
                    FeeId = a.FeeId,

                    MedicalCardId = a.MedicalCardId,
                    MedicalCardNo = m.CardNo,
                    OutpatientNo = a.OutpatientNo,

                    AppointmentTime = a.AppointmentTime,
                    IdCardType = ((int)p.CardType),
                    IdCardNo = p.IdCardNo,
                    Nation = int.Parse(p.Nation == "" || p.Nation == null ? "0" : p.Nation),
                    PatientNo = p.PatientNo,
                    Phone = p.Phone,
                    ContactName = p.ContactName,
                    ContactPhone = p.ContactPhone,
                    ContactAddress = p.ContactAddress,
                    ContactRelationship = p.ContactRelationship,
                    Nationality = p.Nationality,
                    InpatientWay = a.InpatientWay,
                    Occupation = p.Occupation,
                    Marriage = int.Parse(p.Marriage == "" || p.Marriage == null ? "0" : p.Marriage),
                    // NativePlace = new List<string>()
                    // {
                    //     p.NativePlaceProvince == null ? "0" : p.NativePlaceProvince.ToString(),
                    //     p.NativePlaceCity == null ? "0" : p.NativePlaceCity.ToString(),
                    //     p.NativePlaceCounty == null ? "0" : p.NativePlaceCounty.ToString()
                    // },
                    // Residence = new List<string>()
                    //     { p.ResidenceProvince.ToString(), p.ResidenceCity.ToString(), p.ResidenceCounty.ToString() },
                    // Work = new List<string>()
                    //     { p.WorkProvince.ToString(), p.WorkCity.ToString(), p.WorkCounty.ToString() },
                    // Birthplace = new List<string>()
                    //     { p.BirthplaceProvince.ToString(), p.BirthplaceCity.ToString(), p.BirthplaceCounty.ToString() },
                    NativePlaceCity = p.NativePlaceCity,
                    NativePlaceCounty = p.NativePlaceCounty,
                    NativePlaceProvince = p.NativePlaceProvince,
                    BirthplaceCounty = p.BirthplaceCounty,
                    BirthplaceCity = p.BirthplaceCity,
                    BirthplaceProvince = p.BirthplaceProvince,
                    ResidenceCounty = p.ResidenceCounty,
                    ResidenceCity = p.ResidenceCity,
                    ResidenceProvince = p.ResidenceProvince,
                    ResidenceAddress = p.ResidenceAddress,
                    WorkCounty = p.WorkCounty,
                    WorkCity = p.WorkCity,
                    WorkProvince = p.WorkProvince,
                    WorkAddress = p.WorkAddress,
                    WorkPlace = p.WorkPlace,
                    WorkPlacePhone = p.WorkPlacePhone,
                    MedInsCategory = p.MedInsCategory,

                    MedInsType = p.MedInsType,
                    MedCategory = p.MedCategory,
                    InsuranceType = p.InsuranceType,

                    IsNoCard = p.IsNoCard,
                    MedInsCardInfo = p.MedInsCardInfo,
                    DoctorId = a.DoctorId,
                    DoctorCode = u.Account,
                    DoctorName = u.NickName,
                    DeptId = a.DeptId,
                    DeptCode = o.Code,
                    DeptName = o.Name,
                    DiagnosticCode = a.DiagnosticCode,
                    DiagnosticName = a.DiagnosticName,
                    Remark = a.Remark,
                    Status = a.Status,

                    CreateOrgId = a.CreateOrgId,
                    CreateOrgName = a.CreateOrgName,
                    CreateTime = a.CreateTime,
                    CreateUserId = a.CreateUserId,
                    CreateUserName = a.CreateUserName,
                    UpdateTime = a.UpdateTime,
                    UpdateUserId = a.UpdateUserId,
                    UpdateUserName = a.UpdateUserName,
                });


        return await query.OrderBuilder(input).ToPagedListAsync(input.Page, input.PageSize);
    }

    /// <summary>
    /// 获取住院预约详情 ℹ️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("获取住院预约详情")]
    [ApiDescriptionSettings(Name = "Detail"), HttpGet]
    public async Task<AppointmentRecord> Detail([FromQuery] QueryByIdAppointmentRecordInput input)
    {
        return await _appointmentRecordRep.GetFirstAsync(u => u.Id == input.Id);
    }

    // /// <summary>
    // /// 增加住院预约 ➕
    // /// </summary>
    // /// <param name="input"></param>
    // /// <returns></returns>
    // [DisplayName("增加住院预约")]
    // [ApiDescriptionSettings(Name = "Add"), HttpPost]
    // public async Task<long> Add(AddAppointmentRecordInput input)
    // {
    //     var entity = input.Adapt<AppointmentRecord>();
    //     return await _appointmentRecordRep.InsertAsync(entity) ? entity.Id : 0;
    // }

    [DisplayName("门诊预约住院")]
    [ApiDescriptionSettings(Name = "OutpatientAppointment"), HttpPost]
    public async Task<long> OutpatientAppointment(AppointmentRecordDto dto)
    {
        //查询挂号就诊信息 

        var entity = dto.Adapt<AppointmentRecord>();
        // var register = await _outpatientRegisterRep.Context.Queryable<OutpatientRegister>()
        //     .Where(p => p.Id == dto.OutpatientRegisterId)
        //     .FirstAsync();
        //
        // var entity = dto.Adapt<AppointmentRecord>();
        // entity.VisitNo = register.VisitNo;
        // entity.OutpatientNo = register.OutpatientNo;
        // entity.MedicalCardId = register.CardId;
        // entity.InsuranceNo = register.InsuranceNum;
        // entity.DiagnosticCode = register.DiagnosticCode;
        // entity.DiagnosticName = register.DiagnosticName;
        entity.Status = 0;
       
        //     entity.MedicalCardNo = register.CardNo;
       return   await _appointmentRecordRep.InsertAsync(entity)? entity.Id : 0;
    }

    /// <summary>
    /// 更新住院预约 ✏️
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("更新住院预约")]
    [ApiDescriptionSettings(Name = "Update"), HttpPost]
    public async Task<long> Update(AppointmentRecordDto input)
    {
        var record = await _appointmentRecordRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);
        if (record.Status != 0)
        {
            throw Oops.Oh("当前状态禁止修改");
        }

        var entity = input.Adapt<AppointmentRecord>();
        await _appointmentRecordRep.UpdateAsync(u => new AppointmentRecord()
        {
                AppointmentTime = entity.AppointmentTime,
                DeptId = entity.DeptId,
                DeptCode = entity.DeptCode,
                DeptName = entity.DeptName,
                DoctorId = entity.DoctorId,
                DoctorCode = entity.DoctorCode,
                DoctorName = entity.DoctorName,
                DiagnosticCode = entity.DiagnosticCode,
                DiagnosticName = entity.DiagnosticName,
                Remark = entity.Remark,
                FeeId = entity.FeeId 
        }, u => u.Id == input.Id);
 
  
        return entity.Id;
    }

    /// <summary>
    /// 删除住院预约 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("删除住院预约")]
    [ApiDescriptionSettings(Name = "Delete"), HttpPost]
    public async Task Delete(DeleteAppointmentRecordInput input)
    {
        var entity = await _appointmentRecordRep.GetFirstAsync(u => u.Id == input.Id) ??
                     throw Oops.Oh(ErrorCodeEnum.D1002);

        if (entity.Status != 0)
        {
            throw Oops.Oh("当前状态禁止修改");
        }

        await _appointmentRecordRep.FakeDeleteAsync(entity); //假删除
        //await _appointmentRecordRep.DeleteAsync(entity);   //真删除
    }

    /// <summary>
    /// 批量删除住院预约 ❌
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("批量删除住院预约")]
    [ApiDescriptionSettings(Name = "BatchDelete"), HttpPost]
    public async Task<int> BatchDelete([Required(ErrorMessage = "主键列表不能为空")] List<DeleteAppointmentRecordInput> input)
    {
        var exp = Expressionable.Create<AppointmentRecord>();
        foreach (var row in input) exp = exp.Or(it => it.Id == row.Id);
        var list = await _appointmentRecordRep.AsQueryable().Where(exp.ToExpression()).ToListAsync();

        return await _appointmentRecordRep.FakeDeleteAsync(list); //假删除
        //return await _appointmentRecordRep.DeleteAsync(list);   //真删除
    }

    /// <summary>
    /// 导出住院预约记录 🔖
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [DisplayName("导出住院预约记录")]
    [ApiDescriptionSettings(Name = "Export"), HttpPost, NonUnify]
    public async Task<IActionResult> Export(PageAppointmentRecordInput input)
    {
        var list = (await Page(input)).Items?.Adapt<List<ExportAppointmentRecordOutput>>() ?? new();
        if (input.SelectKeyList?.Count > 0) list = list.Where(x => input.SelectKeyList.Contains(x.Id)).ToList();
        return ExcelHelper.ExportTemplate(list, "住院预约导出记录");
    }

    /// <summary>
    /// 下载住院预约数据导入模板 ⬇️
    /// </summary>
    /// <returns></returns>
    [DisplayName("下载住院预约数据导入模板")]
    [ApiDescriptionSettings(Name = "Import"), HttpGet, NonUnify]
    public IActionResult DownloadTemplate()
    {
        return ExcelHelper.ExportTemplate(new List<ExportAppointmentRecordOutput>(), "住院预约导入模板");
    }

    /// <summary>
    /// 导入住院预约记录 💾
    /// </summary>
    /// <returns></returns>
    [DisplayName("导入住院预约记录")]
    [ApiDescriptionSettings(Name = "Import"), HttpPost, NonUnify, UnitOfWork]
    public IActionResult ImportData([Required] IFormFile file)
    {
        lock (this)
        {
            var stream = ExcelHelper.ImportData<ImportAppointmentRecordInput, AppointmentRecord>(file,
                (list, markerErrorAction) =>
                {
                    _sqlSugarClient.Utilities.PageEach(list, 2048, pageItems =>
                    {
                        // 校验并过滤必填基本类型为null的字段
                        var rows = pageItems.Where(x => { return true; }).Adapt<List<AppointmentRecord>>();

                        var storageable = _appointmentRecordRep.Context.Storageable(rows)
                            .SplitError(it => it.Item.PatientName?.Length > 100, "患者姓名长度不能超过100个字符")
                            .SplitError(it => it.Item.IdCardType?.Length > 100, "证件类型长度不能超过100个字符")
                            .SplitError(it => it.Item.IdCardNo?.Length > 100, "证件号码长度不能超过100个字符")
                            .SplitError(it => it.Item.InsuranceNo?.Length > 100, "保险号长度不能超过100个字符")
                            .SplitError(it => it.Item.DeptCode?.Length > 100, "预约科室代码长度不能超过100个字符")
                            .SplitError(it => it.Item.DeptName?.Length > 100, "预约科室名称长度不能超过100个字符")
                            .SplitError(it => it.Item.DoctorCode?.Length > 100, "预约医生代码长度不能超过100个字符")
                            .SplitError(it => it.Item.DoctorName?.Length > 100, "预约医生姓名长度不能超过100个字符")
                            .SplitError(it => it.Item.DiagnosticCode?.Length > 100, "诊断代码长度不能超过100个字符")
                            .SplitError(it => it.Item.DiagnosticName?.Length > 100, "诊断名称长度不能超过100个字符")
                            .SplitError(it => it.Item.Remark?.Length > 500, "备注长度不能超过500个字符")
                            .SplitInsert(_ => true)
                            .ToStorage();

                        storageable.BulkCopy();
                        storageable.BulkUpdate();

                        // 标记错误信息
                        markerErrorAction.Invoke(storageable, pageItems, rows);
                    });
                });

            return stream;
        }
    }
}