using Admin.NET.Core;
namespace His.Module.Inpatient.Entity;

/// <summary>
/// 预交金表
/// </summary>
[Tenant("1300000000006")]
[SugarTable("advance_payment", "预交金表")]
public class AdvancePayment : EntityTenant
{
    /// <summary>
    /// 收据号
    /// </summary>
    [SugarColumn(ColumnName = "voucher_no", ColumnDescription = "收据号", Length = 100)]
    public virtual string? VoucherNo { get; set; }
    
    /// <summary>
    /// 患者ID
    /// </summary>
    [SugarColumn(ColumnName = "patient_id", ColumnDescription = "患者ID")]
    public virtual long? PatientId { get; set; }
    
    /// <summary>
    /// 住院登记号
    /// </summary>
    [SugarColumn(ColumnName = "register_id", ColumnDescription = "住院登记号")]
    public virtual long? RegisterId { get; set; }
    
    /// <summary>
    /// 住院号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_no", ColumnDescription = "住院号", Length = 100)]
    public virtual string? InpatientNo { get; set; }
    
    /// <summary>
    /// 住院流水号
    /// </summary>
    [SugarColumn(ColumnName = "inpatient_serial_no", ColumnDescription = "住院流水号", Length = 100)]
    public virtual string? InpatientSerialNo { get; set; }
    
    /// <summary>
    /// 病案号
    /// </summary>
    [SugarColumn(ColumnName = "medical_record_no", ColumnDescription = "病案号", Length = 100)]
    public virtual string? MedicalRecordNo { get; set; }
    
    /// <summary>
    /// 保险号码
    /// </summary>
    [SugarColumn(ColumnName = "insurance_no", ColumnDescription = "保险号码", Length = 100)]
    public virtual string? InsuranceNo { get; set; }
    
    /// <summary>
    /// 预缴金额
    /// </summary>
    [SugarColumn(ColumnName = "advance_amount", ColumnDescription = "预缴金额", Length = 20, DecimalDigits=4)]
    public virtual decimal? AdvanceAmount { get; set; }
    
    /// <summary>
    /// 大写金额
    /// </summary>
    [SugarColumn(ColumnName = "advance_amount_chinese", ColumnDescription = "大写金额", Length = 100)]
    public virtual string? AdvanceAmountChinese { get; set; }
    
    /// <summary>
    /// 预缴时间
    /// </summary>
    [SugarColumn(ColumnName = "advance_time", ColumnDescription = "预缴时间")]
    public virtual DateTime? AdvanceTime { get; set; }
    
    /// <summary>
    /// 付款方式
    /// </summary>
    [SugarColumn(ColumnName = "payment_method", ColumnDescription = "付款方式", Length = 100)]
    public virtual string? PaymentMethod { get; set; }
    
    /// <summary>
    /// 付款记录ID
    /// </summary>
    [SugarColumn(ColumnName = "payment_record_id", ColumnDescription = "付款记录ID")]
    public virtual long? PaymentRecordId { get; set; }
    
    /// <summary>
    /// 状态
    /// </summary>
    [SugarColumn(ColumnName = "status", ColumnDescription = "状态")]
    public virtual int? Status { get; set; }
    
    /// <summary>
    /// 创建组织ID
    /// </summary>
    [SugarColumn(ColumnName = "create_org_id", ColumnDescription = "创建组织ID")]
    public virtual long? CreateOrgId { get; set; }
    
    /// <summary>
    /// 创建组织名称
    /// </summary>
    [SugarColumn(ColumnName = "create_org_name", ColumnDescription = "创建组织名称", Length = 100)]
    public virtual string? CreateOrgName { get; set; }
    
}
