namespace @(Model.NameSpace);

/// <summary>
/// @(Model.BusName)输出参数
/// </summary>
public class @(Model.ClassName)Dto
{
@foreach (var column in Model.TableField){
if(column.EffectType == "ForeignKey"){
    @:/// <summary>
    @:/// @column.ColumnComment
    @:/// </summary>
    @:public string @(column.PropertyName)FkColumn { get; set; }
    @:
}
}
@foreach (var column in Model.TableField){
    @:/// <summary>
    @:/// @column.ColumnComment
    @:/// </summary>
    @:public @column.NetType @column.PropertyName { get; set; }
    @:
}
}
