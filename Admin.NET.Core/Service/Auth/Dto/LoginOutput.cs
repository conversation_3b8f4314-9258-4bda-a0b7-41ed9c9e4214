// Admin.NET 项目的版权、商标、专利和其他相关权利均受相应法律法规的保护。使用本项目应遵守相关法律法规和许可证的要求。
//
// 本项目主要遵循 MIT 许可证和 Apache 许可证（版本 2.0）进行分发和使用。许可证位于源代码树根目录中的 LICENSE-MIT 和 LICENSE-APACHE 文件。
//
// 不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！

namespace Admin.NET.Core.Service;

/// <summary>
/// 用户登录结果
/// </summary>
public class LoginOutput
{
    /// <summary>
    /// 令牌Token
    /// </summary>
    public string AccessToken { get; set; }

    /// <summary>
    /// 刷新Token
    /// </summary>
    public string RefreshToken { get; set; }
}

/// <summary>
/// 用户角色和科室信息
/// </summary>
public class LoginVerifyOutput : LoginOutput
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 用户账号
    /// </summary>
    public string Account { get; set; }

    /// <summary>
    /// 用户姓名
    /// </summary>
    public string RealName { get; set; }

    /// <summary>
    /// 可选角色列表
    /// </summary>
    public List<RoleInfo> Roles { get; set; }
    /// <summary>
    /// 是否超级管理员
    /// </summary>
    public bool IsSuperAdmin { get; set; } = false;
}

/// <summary>
/// 角色信息
/// </summary>
public class RoleInfo
{
    /// <summary>
    /// 角色Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 角色编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 该角色可用的科室列表
    /// </summary>
    public List<DeptInfo> Depts { get; set; }
}

/// <summary>
/// 科室信息
/// </summary>
public class DeptInfo
{
    /// <summary>
    /// 科室Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 科室名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 科室编码
    /// </summary>
    public string Code { get; set; }
}